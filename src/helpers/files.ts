import { ALLOWED_BUT_NOT_SUPPORTED, ALLOWED_FILE_TYPES } from '../constants/files'

export type FileType = 'image' | 'video' | 'audio'

export function getFileTypeFromFilename(filename?: string | null): FileType | null {
  if (!filename) {
    return null
  }

  const extension = filename.split('.').pop()?.toLowerCase?.()

  if (!extension) {
    return null
  }

  const allowedImageFormats = [
    ...ALLOWED_FILE_TYPES.image,
    ...ALLOWED_BUT_NOT_SUPPORTED.image,
  ]
  const allowedVideoFormats = [
    ...ALLOWED_FILE_TYPES.video,
  ]

  const allowedAudioFormats = [
    ...ALLOWED_FILE_TYPES.audio,
    ...ALLOWED_BUT_NOT_SUPPORTED.audio,
  ]

  if (allowedImageFormats.includes(extension)) {
    return 'image'
  }

  if (allowedVideoFormats.includes(extension)) {
    return 'video'
  }

  if (allowedAudioFormats.includes(extension)) {
    return 'audio'
  }

  return null
}
