<script setup lang="ts">
import { CheckboxIndicator, CheckboxRoot } from 'reka-ui'
import { computed, getCurrentInstance, withDefaults } from 'vue'
import { FzIcon } from '../FzIcon'
import { FzTooltip } from '../FzTooltip'

const props = withDefaults(defineProps<{
  modelValue?: boolean | any[]
  value?: any
  label?: string
  disabled?: boolean
  indeterminate?: boolean
  checked?: boolean
  id?: string
  readonly?: boolean
  tooltip?: string
  invalid?: boolean
  size?: 'sm' | 'lg'
}>(), {
  checked: undefined,
  size: 'lg',
  invalid: false,
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean | any[]): void
}>()

const uid = getCurrentInstance()?.uid
const checkboxId = computed(() => props.id || `fz-checkbox-${uid}`)

const isChecked = computed(() => {
  if (props.indeterminate) {
    return 'indeterminate'
  }
  if (props.checked !== undefined) {
    return props.checked
  }

  if (Array.isArray(props.modelValue)) {
    return props.modelValue.includes(props.value)
  }
  return props.modelValue === true
})

function handleChange(valueFromRoot: boolean | 'indeterminate') {
  if (props.readonly)
    return

  const finalCheckedState = valueFromRoot === 'indeterminate'
    ? false
    : (typeof valueFromRoot === 'boolean' ? valueFromRoot : false)

  if (Array.isArray(props.modelValue)) {
    const newValue = [...props.modelValue]
    if (finalCheckedState) {
      if (!newValue.includes(props.value)) {
        newValue.push(props.value)
      }
    }
    else {
      const index = newValue.indexOf(props.value)
      if (index > -1) {
        newValue.splice(index, 1)
      }
    }
    emit('update:modelValue', newValue)
  }
  else {
    emit('update:modelValue', finalCheckedState)
  }
}
</script>

<template>
  <label
    class="fz-checkbox"
    :class="[
      `fz-checkbox--${size}`,
      {
        'fz-checkbox--disabled': disabled,
        'fz-checkbox--checked': isChecked,
        'fz-checkbox--indeterminate': indeterminate && !isChecked,
        'fz-checkbox--invalid': invalid,
      }]"
    :for="checkboxId"
  >
    <CheckboxRoot
      :id="checkboxId"
      class="fz-checkbox__input"
      :class="`fz-checkbox__input--${size}`"
      :disabled="disabled"
      :model-value="isChecked"
      @update:model-value="handleChange"
    >
      <CheckboxIndicator class="fz-checkbox__indicator">
        <div v-if="indeterminate" class="fz-checkbox__indicator-indeterminate" />
        <FzIcon v-else name="check" svg-size="12px" />
      </CheckboxIndicator>
    </CheckboxRoot>
    <span v-if="label" class="fz-checkbox__label">
      <span>{{ label }}</span>
      <span class="fz-checkbox__tooltip">
        <FzTooltip v-if="tooltip" :content="tooltip">
          <FzIcon name="info" size="md" />
        </FzTooltip>
      </span>
    </span>
  </label>
</template>

<style>
.fz-checkbox {
  display: inline-flex;
  align-items: center;
  gap: var(--fz-spacing-6);
  cursor: pointer;
  color: var(--fz-color-text-primary);
}

.fz-checkbox--sm{
  font: var(--fz-font-control-text-medium);
}

.fz-checkbox--lg{
  font: var(--fz-font-control-text-big);
}

.fz-checkbox--disabled {
  cursor: not-allowed;
  opacity: 0.6; /* Standard disabled opacity */
}

.fz-checkbox__input {
  border: 1px solid var(--fz-color-border-input);
  background-color: var(--fz-color-surface-light);
  transition: background-color 0.2s, border-color 0.2s;
  flex-shrink: 0;
  flex-grow: 0;
}

.fz-checkbox__input--sm{
  width: 20px;
  height: 20px;
  border-radius: var(--fz-radius-xs);
}

.fz-checkbox__input--lg{
  width: 24px;
  height: 24px;
  border-radius: var(--fz-radius-sm);
}

.fz-checkbox__input:hover:not([data-disabled]) {
  border-color: var(--fz-color-border-input-focus);
}

.fz-checkbox__input[data-state='checked'],
.fz-checkbox__input[data-state='indeterminate'] {
  background-color: var(--fz-color-surface-brand-primary);
  border-color: var(--fz-color-surface-brand-primary);
}

.fz-checkbox__input[data-state='checked']:hover:not([data-disabled]),
.fz-checkbox__input[data-state='indeterminate']:hover:not([data-disabled]) {
  background-color: var(--fz-color-surface-brand-primary-hover);
  border-color: var(--fz-color-surface-brand-primary-hover);
}

.fz-checkbox__input:focus-visible {
  outline: none;
  box-shadow: 0 0 0 var(--fz-ring-offset-width) var(--fz-ring-offset-color),
             0 0 0 calc(var(--fz-ring-offset-width) + var(--fz-ring-width)) var(--fz-ring-primary-color);
}

.fz-checkbox__indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--fz-color-text-inverse); /* Icon color on checked background */
  position: relative;
  flex-basis: auto;
}

.fz-checkbox__indicator-indeterminate {
  width: 8px;
  height: 8px;
  background-color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 2px;
}

.fz-checkbox__indicator .fz-icon {
  display: flex; /* Ensure icon scales correctly */
}

.fz-checkbox__label {
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-2);
  user-select: none; /* Prevent text selection on label click */
}

.fz-checkbox__tooltip{
  display: flex;
  color: var(--fz-color-border-icon-input);
}

.fz-checkbox--invalid .fz-checkbox__input{
  border-color: var(--fz-color-border-input-error);
}
</style>
