import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import { FzCheckbox } from '.'

const meta = {
  title: 'Components/FzCheckbox',
  component: FzCheckbox,
  tags: ['autodocs'],
  argTypes: {
    'modelValue': { control: 'boolean', description: 'Binding for single checkbox (v-model)' },
    'value': { control: 'text', description: 'Value for checkbox in a group' },
    'label': { control: 'text' },
    'tooltip': { control: 'text' },
    'disabled': { control: 'boolean' },
    'indeterminate': { control: 'boolean' },
    'invalid': { control: 'boolean' },
    'size': { control: 'select', options: ['sm', 'lg'], table: { defaultValue: { summary: 'lg' } } },
    'id': { control: 'text' },
    'onUpdate:modelValue': { action: 'updated:modelValue' },
  },
} satisfies Meta<typeof FzCheckbox>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    label: 'Accept terms and conditions',
    id: 'terms-checkbox',
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(false)
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" /> <p class="fz:mt-2">Checked: {{ checked }}</p>',
  }),
}

export const Checked: Story = {
  args: {
    label: 'Pre-checked',
    modelValue: true,
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(args.modelValue)
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}

export const Indeterminate: Story = {
  args: {
    label: 'Indeterminate State',
    indeterminate: true,
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(false) // modelValue is false for indeterminate typically
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}

export const Disabled: Story = {
  args: {
    label: 'Disabled Checkbox',
    disabled: true,
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(false)
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}

export const DisabledChecked: Story = {
  args: {
    label: 'Disabled and Checked',
    modelValue: true,
    disabled: true,
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(args.modelValue)
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}

export const Group: Story = {
  args: {
    // No specific args for the story itself, setup handles it
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const selectedFruits = ref(['apple'])
      const fruits = [
        { label: 'Apple', value: 'apple' },
        { label: 'Banana', value: 'banana' },
        { label: 'Orange', value: 'orange', disabled: true },
        { label: 'Grape', value: 'grape' },
      ]
      return { args, selectedFruits, fruits }
    },
    template: `
      <div>
        <p class="fz:mb-2">Selected Fruits: {{ selectedFruits.join(', ') || 'None' }}</p>
        <div v-for="fruit in fruits" :key="fruit.value" class="fz:mb-1">
          <FzCheckbox
            v-model="selectedFruits"
            :value="fruit.value"
            :label="fruit.label"
            :disabled="fruit.disabled"
            :id="'fruit-' + fruit.value"
            @update:modelValue="args['onUpdate:modelValue']"
          />
        </div>
      </div>
    `,
  }),
}

export const Tooltip: Story = {
  args: {
    label: 'Checkbox text',
    tooltip: 'Checkbox tooltip',
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(false) // modelValue is false for indeterminate typically
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}

export const Size: Story = {
  args: {
    label: 'Checkbox size',
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(false) // modelValue is false for indeterminate typically
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}

export const Invalid: Story = {
  args: {
    label: 'Checkbox invalid',
    invalid: true,
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(false) // modelValue is false for indeterminate typically
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}

export const NoLabel: Story = {
  args: {
    id: 'no-label-checkbox',
  },
  render: args => ({
    components: { FzCheckbox },
    setup() {
      const checked = ref(false)
      return { args, checked }
    },
    template: '<FzCheckbox v-bind="args" v-model="checked" @update:modelValue="args[\'onUpdate:modelValue\']" />',
  }),
}
