<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useFancybox } from '../../composables/useFancybox.ts'
import { useFileLoader } from '../../composables/useFileLoader'
import { ALLOWED_FILE_TYPES } from '../../constants/files.ts'
import { FzIcon } from '../FzIcon'
import <PERSON>z<PERSON>oader from '../FzLoader'
import { FzTooltip } from '../FzTooltip'
import { FzTransition } from '../FzTransition'

const props = withDefaults(defineProps<{
  type?: TypeFile
  fileExtensions?: string[]
  weightLimit?: number
  text?: string
  iconName?: string
  disabled?: boolean
  horizontalText?: boolean
  invalid?: boolean
  previewUrl?: string
  group?: string
  customClass?: string
}>(), {
  type: 'image',
  iconName: 'user',
  weightLimit: 5,
  disabled: false,
  horizontalText: false,
  previewUrl: undefined,
  group: 'default-group',
  invalid: false,
})

const emit = defineEmits<{
  (e: 'fileSelected', file: File): void
}>()

const slot = defineSlots()

const { show, addItem, removeItem } = useFancybox(props.group)

const modelValue = defineModel<File | null>({
  default: null,
})

type TypeFile = 'image' | 'video' | 'audio'

const MAX_DISPLAYED_EXTENSIONS = 5
const fileInput = ref<HTMLInputElement | null>(null)
const errorVisible = ref(false)
const errorTimer = ref<ReturnType<typeof setTimeout> | null>(null)

const allowedExtensions = computed(() => {
  return props.fileExtensions || ALLOWED_FILE_TYPES[props.type]
})

const {
  previewUrl: filePreview,
  error: errorMessage,
  isLoading,
  loadFile,
  reset: resetFile,
  clearError,
} = useFileLoader({
  maxSize: props.weightLimit * 1024 * 1024,
  allowedTypes: allowedExtensions.value,
})

const displayText = computed(() => {
  if (props.text)
    return props.text
  return props.type === 'image'
    ? 'Фото'
    : props.type === 'video' ? 'Видео' : 'Аудио'
})

// Добавляем элемент в Fancybox при изменении previewUrl или выборе файла
watch([() => props.previewUrl, filePreview], ([newUrl, preview]) => {
  const src = newUrl || preview
  if (src) {
    addItem({
      src,
      type: 'image',
    })
  }
}, { immediate: true })

watch(errorMessage, (newError) => {
  if (newError) {
    errorVisible.value = true
    if (errorTimer.value)
      clearTimeout(errorTimer.value)
    errorTimer.value = setTimeout(() => {
      errorVisible.value = false
    }, 3000)
  }
})

async function handleFileChange(event: Event) {
  const input = event.target as HTMLInputElement
  const selectedFile = input.files?.[0]

  if (!selectedFile)
    return

  clearError()
  const isValid = await loadFile(selectedFile)

  if (isValid) {
    modelValue.value = selectedFile
    emit('fileSelected', selectedFile)
  }

  input.value = ''
}

function removeFile() {
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  resetFile()
  modelValue.value = null
  removeItem()
  errorVisible.value = false
  if (errorTimer.value) {
    clearTimeout(errorTimer.value)
    errorTimer.value = null
  }
}

function triggerFileInput() {
  if (!props.disabled) {
    clearError()
    errorVisible.value = false
    fileInput.value?.click()
  }
}

function openPreview() {
  show()
}

const tooltipContent = computed(() => {
  if (allowedExtensions.value.length > 5) {
    return `Допустимые форматы: ${allowedExtensions.value.join(', ')}`
  }
  return ''
})

const displayedExtensions = computed(() => {
  return allowedExtensions.value.slice(0, MAX_DISPLAYED_EXTENSIONS)
})

const hasMoreExtensions = computed(() => {
  return allowedExtensions.value.length > MAX_DISPLAYED_EXTENSIONS
})

onMounted(() => {
  if (props.previewUrl) {
    addItem({
      src: props.previewUrl,
      type: 'image',
    })
  }
})

onUnmounted(() => {
  if (errorTimer.value) {
    clearTimeout(errorTimer.value)
  }
  removeItem()
})
</script>

<template>
  <div class="fz-file-upload">
    <div
      class="fz-file-upload__block"
      :class="[
        customClass,
        {
          'fz-file-upload__block--invalid': (errorVisible && errorMessage) || invalid,
          'fz-file-upload__block--with-preview': (modelValue && filePreview) || previewUrl,
        }]"
      @click="!modelValue && !disabled && triggerFileInput()"
    >
      <template v-if="isLoading">
        <div class="fz-file-upload__loading">
          <FzLoader size="small" />
        </div>
      </template>
      <template v-else>
        <template v-if="!modelValue && !filePreview && !previewUrl">
          <FzTooltip v-if="hasMoreExtensions" :content="tooltipContent">
            <div class="fz-file-upload__tooltip" />
          </FzTooltip>
          <button
            class="fz-file-upload__button"
            :class="[
              { 'fz-file-upload__button--disabled': disabled },
              { 'fz-file-upload__button--horizontalText': horizontalText },
            ]"
          >
            <label
              v-if="iconName || slot.icon"
              class="fz-file-upload__button--icon"
            >
              <span v-if="iconName && !slot.icon">
                <FzIcon :name="iconName" size="md" />
              </span>
              <span v-if="slot.icon">
                <slot name="icon" />
              </span>
            </label>
            <label class="fz-file-upload__button--text">
              <template v-if="slot.content">
                <slot name="content" />
              </template>
              <template v-else>
                <span>{{ displayText }}</span>
                <span>{{ displayedExtensions.join(', ') }}<template v-if="hasMoreExtensions"> и др.</template></span>
                <span>до {{ weightLimit }} Мб</span>
              </template>
            </label>
          </button>
        </template>
        <div v-else class="fz-file-upload__file-selected">
          <template v-if="filePreview || previewUrl">
            <div class="fz-file-upload__preview" @click.stop.prevent="openPreview">
              <img
                :src="filePreview || previewUrl"
                :alt="modelValue?.name || 'Preview image'"
                class="fz-file-upload__preview-image"
              >
            </div>
            <button
              class="fz-file-upload__remove"
              @click.stop="removeFile"
            >
              <FzIcon name="x-small" size="sm" @click.stop="removeFile" />
            </button>
          </template>
        </div>
      </template>
    </div>
    <FzTransition name="fade">
      <div v-if="(errorVisible && errorMessage) || invalid" class="fz-file-upload__error-message">
        {{ errorMessage }}
      </div>
    </FzTransition>

    <input
      ref="fileInput"
      class="fz-file-upload__input"
      type="file"
      :accept="allowedExtensions.map(ext => `.${ext}`).join(',')"
      @change="handleFileChange"
    >
  </div>
</template>

<style>
.fz-file-upload__tooltip{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.fz-file-upload__tooltip:hover ~ .fz-file-upload__button:not(.fz-file-upload__button--disabled),
.fz-file-upload__tooltip:hover + .fz-file-upload__button:not(.fz-file-upload__button--disabled) {
  background-color: var(--fz-color-surface-secondary-hover);
}

.fz-file-upload__tooltip:active ~ .fz-file-upload__button:not(.fz-file-upload__button--disabled),
.fz-file-upload__tooltip:active + .fz-file-upload__button:not(.fz-file-upload__button--disabled) {
  background-color: var(--fz-color-surface-secondary-clicked);
}

.fz-file-upload {
  position: relative;
}

.fz-file-upload__block {
  background-color: var(--fz-color-surface-secondary);
  min-height: 48px;
  min-width: 100px;
  border-radius: var(--fz-radius-sm);
  cursor: pointer;
  border: 1px solid transparent;
  transition:
      background-color var(--fz-transition-duration),
      border-color var(--fz-transition-duration),
      opacity var(--fz-transition-duration);
  position: relative;
}

.fz-file-upload__block--invalid {
  border-color: var(--fz-color-border-input-error);
}

.fz-file-upload__block--with-preview {
  padding: 0;
  border: none;
}

.fz-file-upload__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.fz-file-upload__loading .fz-loader{
  color: var(--fz-color-text-secondary);
}

.fz-file-upload__button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--fz-spacing-4);
  cursor: pointer;
  border: 0;
  border-radius: var(--fz-radius-sm);
  padding: var(--fz-spacing-4);
  background-color: var(--fz-color-surface-secondary);
  width: 100%;
  height: 100%;
  transition:
      background-color var(--fz-transition-duration),
      border-color var(--fz-transition-duration),
      opacity var(--fz-transition-duration);
}

.fz-file-upload__button:not(.fz-file-upload__button--disabled):hover {
  background-color: var(--fz-color-surface-secondary-hover);
}

.fz-file-upload__button:not(.fz-file-upload__button--disabled):active {
  background-color: var(--fz-color-surface-secondary-clicked);
}

.fz-file-upload__button:not(.fz-file-upload__button--disabled):focus-visible {
  box-shadow: 0 0 0 var(--fz-ring-width) var(--fz-color-border-button-secondary-focused);
}

.fz-file-upload__button--icon {
  color: var(--fz-color-text-secondary);
}

.fz-file-upload__button--text {
  color: var(--fz-color-text-secondary);
  font: var(--fz-font-caption);
  cursor: pointer;
  text-align: center;
}

.fz-file-upload__button--text span {
  display: block;
}

.fz-file-upload__button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.fz-file-upload__button--horizontalText{
  flex-direction: row;
}

.fz-file-upload__file-selected {
  width: 100%;
  height: 100%;
}

.fz-file-upload__preview{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.fz-file-upload__preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--fz-radius-sm);
}

.fz-file-upload__remove{
  position: absolute;
  z-index: 3;
  top: -10px;
  right: -10px;
  background: rgba(0, 0, 0, 1);
  color: var(--fz-color-text-inverse);
  border: 0;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  padding: 0;
  line-height: 1;
  cursor: pointer;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity var(--fz-transition-duration);
}

.fz-file-upload__remove:hover {
  opacity: 0.8;
}

.fz-file-upload__input {
  display: none;
}

.fz-file-upload__error-message{
  margin-top: 8px;
  font: var(--fz-font-hint);
  color: var(--fz-color-text-critical);
}
</style>
