import type { Meta, StoryObj } from '@storybook/vue3'
import { computed, ref } from 'vue'
import { FzIcon } from '../FzIcon'
import FzFileUpload from './FzFileUpload.vue'

const meta: Meta<typeof FzFileUpload> = {
  title: 'Components/FzFileUpload',
  component: FzFileUpload,
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      description: 'File format',
      options: ['image', 'video', 'audio'],
    },
    fileExtensions: {
      control: { type: 'object' },
      description: 'Allowed file extensions (e.g. [“jpg”, “png”])',
      table: {
        type: { summary: 'string[]' },
      },
    },
    weightLimit: {
      control: 'number',
      description: 'File weight limit',
    },
    text: {
      control: 'text',
      description: 'File preview text',
    },
    iconName: {
      control: 'text',
      description: 'Icon name to display in the preview',
    },
    disabled: {
      control: 'boolean',
      description: 'Block file upload',
    },
    customClass: {
      control: 'text',
      description: 'Class on the block upload file',
    },
    height: {
      control: 'text',
      description: 'Height of the block for file upload',
    },
    horizontalText: {
      control: 'boolean',
      description: 'Arrange content horizontally',
    },
    invalid: {
      control: 'boolean',
      description: 'Invalid file',
    },
    previewUrl: {
      control: 'text',
      description: 'URL for the preview',
    },
    group: {
      control: 'text',
      description: 'Name for a group of pictures',
    },
  },
  args: {
    fileExtensions: ['jpg', 'jpeg', 'png'],
  },
}

export default meta
type Story = StoryObj<typeof FzFileUpload>

export const Default: Story = {
  args: {
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" class="fz:w-60" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const fileExtensions: Story = {
  args: {
    fileExtensions: ['png', 'jpg', 'pdf', 'mp3', 'mp4', 'gif'],
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const weightLimit: Story = {
  args: {
    weightLimit: 1,
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const Text: Story = {
  args: {
    text: 'text in file',
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const Icon: Story = {
  args: {
    iconName: 'v',
    customClass: 'componentSize',
  },

  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const Disabled: Story = {
  args: {
    disabled: true,
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const HorizontalText: Story = {
  args: {
    horizontalText: true,
    iconName: 'user',
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const PreviewUrl: Story = {
  args: {
    customClass: 'componentSize',
    group: 'urlGroup',
    previewUrl: 'https://i.pinimg.com/736x/34/7d/19/347d197e1a6dfddff9826fcbcdd22f06.jpg',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const Invalid: Story = {
  args: {
    invalid: true,
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file" />
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const SlotIcon: StoryObj = {
  args: {
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload, FzIcon },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file">
        <template #icon>
          <FzIcon name="user" size="lg" />
        </template>
      </FzFileUpload>
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}

export const SlotContent: Story = {
  args: {
    customClass: 'componentSize',
  },
  render: (args: any) => ({
    components: { FzFileUpload },
    setup() {
      const file = ref<File | null>(null)

      const fileInfo = computed(() => {
        if (!file.value)
          return 'No file selected'

        return `
          Name: ${file.value.name}
          Size: ${(file.value.size / 1024).toFixed(2)} KB
          Type: ${file.value.type}
          Last modified: ${new Date(file.value.lastModified).toLocaleString()}
        `
      })

      return { args, file, fileInfo }
    },
    template: `
      <FzFileUpload v-bind="args" v-model="file">
        <template #content>
          <span>Text Slot</span>
        </template>
      </FzFileUpload>
      <pre style="margin-top: 20px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ fileInfo }}</pre>
    `,
  }),
}
