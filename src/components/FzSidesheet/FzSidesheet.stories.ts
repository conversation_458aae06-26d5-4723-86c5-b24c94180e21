import type { Meta, StoryObj } from '@storybook/vue3'

import { ref } from 'vue'
import { FzButton } from '../FzButton'
import FzSidesheet from './FzSidesheet.vue'

const meta: Meta<typeof FzSidesheet> = {
  title: 'Components/FzSidesheet',
  component: FzSidesheet,
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Text title',
    },
    headerFixed: {
      control: 'boolean',
      description: 'Whether a fixed header is needed ',
    },
    isSuccess: {
      control: 'boolean',
      description: 'Success save',
    },
    isInvalid: {
      control: 'boolean',
      description: 'Invalid save',
    },
    isSuccessText: {
      control: 'text',
      description: 'Text success',
    },
    isInvalidText: {
      control: 'text',
      description: 'Text invalid',
    },
    footer: {
      control: 'boolean',
      description: 'whether to display the footer or not',
    },
    modelValue: {
      control: 'boolean',
      description: 'Open and close a modal window',
    },
  },
}

export default meta
type Story = StoryObj<typeof FzSidesheet>

export const Default: Story = {
  render: (args: any) => ({
    args: {

    },
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      return { args, isOpen }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet v-model="isOpen" title="Title modal">
            <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const HeaderFixed: Story = {
  args: {
    headerFixed: true,
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      return { args, isOpen }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet v-model="isOpen" title="Title modal" v-bind="args">
            <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const IsSuccess: Story = {
  args: {

  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      const success = ref(false)
      const inClickSave = () => {
        success.value = true
      }

      return { args, isOpen, inClickSave, success }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet
            v-model="isOpen"
            v-model:is-success="success"
            title="Title modal"
            v-bind="args"
            @save="inClickSave">
          <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const IsSuccessText: Story = {
  args: {
    isSuccessText: 'Success',
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      const success = ref(false)
      const inClickSave = () => {
        success.value = true
      }

      return { args, isOpen, inClickSave, success }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet
            v-model="isOpen"
            v-model:is-success="success"
            title="Title modal"
            v-bind="args"
            @save="inClickSave"
        >
          
          <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const IsInvalid: Story = {
  args: {

  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      const invalid = ref(false)
      const inClickSave = () => {
        invalid.value = true
      }

      return { args, isOpen, inClickSave, invalid }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet 
            v-model="isOpen" 
            v-model:is-invalid="invalid"
            title="Title modal" 
            v-bind="args"
            @save="inClickSave">
            <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const IsInvalidText: Story = {
  args: {
    isInvalidText: 'Error',
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      const invalid = ref(false)
      const inClickSave = () => {
        invalid.value = true
      }

      return { args, isOpen, inClickSave, invalid }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet 
            v-model="isOpen"
            v-model:is-invalid="invalid"
            title="Title modal" 
            v-bind="args"
            @save="inClickSave"
        >
            <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const FooterFalse: Story = {
  args: {
    footer: false,
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      return { args, isOpen }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet v-model="isOpen" title="Title modal" v-bind="args">
            <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const ContentScroll: Story = {
  args: {
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      return { args, isOpen }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet v-model="isOpen" title="Title modal" v-bind="args">
            <p style="height: 100vh">content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const SlotTitle: Story = {
  args: {
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      return { args, isOpen }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet v-model="isOpen" title="Title modal" v-bind="args">
          <template #title>
            Title Slot
          </template>
            <p>content</p>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const SlotFooter: Story = {
  args: {
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      return { args, isOpen }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet v-model="isOpen" title="Title modal" v-bind="args">
            <p>content</p>
          <template #footer>
            <FzButton >Footer Slot</FzButton>
          </template>
        </FzSidesheet>
      </div>
    `,
  }),
}

export const SlotFooterLeft: Story = {
  args: {
  },
  render: (args: any) => ({
    components: { FzSidesheet, FzButton },
    setup() {
      const isOpen = ref(false)
      return { args, isOpen }
    },
    template: `
      <div>
        <FzButton @click="isOpen = true">Open</FzButton>
        <FzSidesheet v-model="isOpen" title="Title modal" v-bind="args">
            <p>content</p>
          <template #footerLeft>
            <FzButton >Footer left</FzButton>
          </template>
        </FzSidesheet>
      </div>
    `,
  }),
}
