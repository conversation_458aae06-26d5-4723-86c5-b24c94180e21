<script setup lang="ts">
import { DialogClose, DialogContent, DialogOverlay, DialogPortal, DialogRoot } from 'reka-ui'
import { onUnmounted, ref, watch } from 'vue'
import FzButton from '../FzButton/FzButton.vue'
import { FzCustomScrollbar } from '../FzCustomScrollbar'
import { FzIcon } from '../FzIcon'
import { FzTransition } from '../FzTransition'

const props = withDefaults(defineProps<{
  title?: string
  headerFixed?: boolean
  isSuccess?: boolean
  isInvalid?: boolean
  isSuccessText?: string
  isInvalidText?: string
  footer?: boolean
  modelValue?: boolean
}>(), {
  headerFixed: false,
  isSuccess: false,
  isInvalid: false,
  footer: true,
  isSuccessText: 'Сохранение прошло успешно',
  isInvalidText: 'Произошла ошибка',
  modelValue: false,
})

const emit = defineEmits(['save', 'update:modelValue', 'update:isSuccess', 'update:isInvalid'])

const slot = defineSlots()
const wrapperRef = ref<InstanceType<typeof FzCustomScrollbar> | null>(null)

const showMessage = ref(false)
const localIsSuccess = ref(false)
const localIsInvalid = ref(false)
let messageTimer: ReturnType<typeof setTimeout> | null = null

function onSave() {
  emit('save')
}

function startMessageTimer() {
  if (messageTimer) {
    clearTimeout(messageTimer)
    messageTimer = null
  }

  showMessage.value = true
  localIsSuccess.value = props.isSuccess
  localIsInvalid.value = props.isInvalid

  messageTimer = setTimeout(() => {
    showMessage.value = false
    localIsSuccess.value = false
    localIsInvalid.value = false
    emit('update:isSuccess', false)
    emit('update:isInvalid', false)
  }, 3000)
}

watch(() => props.isSuccess, (newVal) => {
  if (newVal) {
    startMessageTimer()
  }
})

watch(() => props.isInvalid, (newVal) => {
  if (newVal) {
    startMessageTimer()
  }
})

onUnmounted(() => {
  if (messageTimer) {
    clearTimeout(messageTimer)
  }
})
</script>

<template>
  <DialogRoot :open="modelValue" @update:open="emit('update:modelValue', $event)">
    <DialogPortal>
      <DialogOverlay class="fz-sidesheet__mask" />
      <div class="fz-sidesheet__outer-container">
        <DialogContent
          class="fz-sidesheet"
          @escape-key-down="emit('update:modelValue', false)"
        >
          <div class="fz-sidesheet__container">
            <div class="fz-sidesheet__content">
              <DialogClose as-child class="fz-sidesheet__container--close">
                <button>
                  <FzIcon name="x" />
                </button>
              </DialogClose>
              <DialogClose as-child class="fz-sidesheet__container--close-mobile">
                <FzButton
                  icon="x"
                  :icon-only="true"
                  variant="ghost"
                  size="medium"
                />
              </DialogClose>

              <div class="fz-sidesheet__wrapper">
                <FzCustomScrollbar ref="wrapperRef">
                  <header
                    v-if="title || slot.title"
                    class="fz-sidesheet__wrapper-title"
                  >
                    <template v-if="title && !slot.title">
                      {{ title }}
                    </template>
                    <template v-if="slot.title">
                      <slot name="title" />
                    </template>
                  </header>

                  <main class="fz-sidesheet__body">
                    <slot />
                  </main>
                </FzCustomScrollbar>
                <div v-if="footer" class="fz-sidesheet__wrapper--shadow" />
              </div>

              <footer
                v-if="footer"
                class="fz-sidesheet__footer fz-sidesheet__footer--border"
                :class="{ 'fz-sidesheet__footer-left-content': slot.footerLeft }"
              >
                <FzTransition name="fade">
                  <div v-if="showMessage && (localIsSuccess || localIsInvalid)" class="fz-sidesheet__message-block">
                    <span v-if="localIsSuccess && !localIsInvalid" class="fz-sidesheet--message fz-sidesheet--success">
                      {{ isSuccessText }}
                    </span>
                    <span v-if="localIsInvalid && !localIsSuccess" class="fz-sidesheet--message fz-sidesheet--invalid">
                      {{ isInvalidText }}
                    </span>
                  </div>
                </FzTransition>

                <template v-if="slot.footer">
                  <slot name="footer" />
                </template>
                <template v-else>
                  <template v-if="slot.footerLeft">
                    <slot name="footerLeft" />
                  </template>
                  <div class="fz-sidesheet__footer-btn-block-right">
                    <FzButton variant="ghost" size="large" @click="emit('update:modelValue', false)">
                      Сбросить
                    </FzButton>
                    <FzButton
                      variant="success"
                      icon="v"
                      size="large"
                      :disabled="localIsSuccess"
                      @click="onSave"
                    >
                      Сохранить
                    </FzButton>
                  </div>
                </template>
              </footer>
            </div>
          </div>
        </DialogContent>
      </div>
    </DialogPortal>
  </DialogRoot>
</template>

<style>
.fz-sidesheet__outer-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
}

.fz-sidesheet__body{
  text-align: left;
}

.fz-sidesheet{
  position: relative;
  height: 100%;
  width: 100%;
  max-width: 100%;
  margin: 0;
  outline: 0;
}

.fz-sidesheet__container{
  position: relative;
  height: 100%;
  margin: 0;
  z-index: 2;
  pointer-events: auto;
}

.fz-sidesheet__content{
  position: relative;
  height: 100%;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 20px rgba(115, 128, 141, 0.4);
  background: var(--fz-color-surface-light);
  pointer-events: auto;
}

.fz-sidesheet__wrapper{
  position: relative;
  padding: var(--fz-spacing-8) var(--fz-spacing-2) var(--fz-spacing-4) var(--fz-spacing-10);
  overflow-y: hidden;
  flex: 1;
}

.fz-sidesheet__wrapper-title{
  text-align: left;
  margin-bottom: var(--fz-spacing-17);
  color: var(--fz-color-text-primary);
  font: var(--fz-font-modal-title);
  max-width: 712px;
}

.fz-sidesheet__container--close{
  cursor: pointer;
  position: absolute;
  left: -38px;
  top: var(--fz-spacing-4);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  color: var(--fz-color-surface-light);
  border: 2px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

.fz-sidesheet__container--close-mobile{
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  left: auto;
  z-index: 3;
  color: var(--fz-color-text-secondary);
}

.fz-sidesheet__footer{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
  height: 80px;
  box-sizing: border-box;
  padding: var(--fz-spacing-8) var(--fz-spacing-10);
}

.fz-sidesheet__footer-left-content{
  justify-content: space-between;
}

.fz-sidesheet__footer--border{
  border-top: 1px solid var(--fz-color-neutral-19);
}

.fz-sidesheet__wrapper:has(.simplebar-scrollable-y) .fz-sidesheet__wrapper--shadow {
  position: absolute;
  left: 0;
  height: 12px;
  width: 100%;
  background: linear-gradient(360deg, rgba(30, 35, 46, 0.1) 0%, rgba(30, 35, 46, 0) 100%);
}

.fz-sidesheet__mask{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--fz-color-surface-overlay-dark);
  opacity: 0;
  cursor: pointer;
  z-index: 1;
}

.fz-sidesheet__footer-btn-block-right{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--fz-spacing-6);
}

.fz-sidesheet__message-block{
  z-index: 10;
}

.fz-sidesheet--message{
  font: var(--fz-font-control-text-big);
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.fz-sidesheet--success{
  color: var(--fz-color-text-success);
  background: var(--fz-color-surface-success-primary);
}

.fz-sidesheet--invalid{
  color: var(--fz-color-text-critical);
  background: var(--fz-color-surface-critical-primary);
}

@keyframes slideInContent {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideOutContent {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

@keyframes fadeInOverlay {
  from { opacity: 0; }
  to { opacity: 0.5; }
}

@keyframes fadeOutOverlay {
  from { opacity: 0.5; }
  to { opacity: 0; }
}

.fz-sidesheet[data-state="open"]{
  animation: slideInContent var(--fz-transition-duration) var(--fz-transition-timing-function) forwards;
}

.fz-sidesheet[data-state="closed"] {
  animation: slideOutContent var(--fz-transition-duration) var(--fz-transition-timing-function) forwards;
}

.fz-sidesheet__mask[data-state="open"] {
  animation: fadeInOverlay var(--fz-transition-duration) var(--fz-transition-timing-function) forwards;
}

.fz-sidesheet__mask[data-state="closed"]{
  animation: fadeOutOverlay var(--fz-transition-duration) var(--fz-transition-timing-function) forwards;
}

@media screen and (min-width: 1200px) {
  .fz-sidesheet__outer-container {
    padding-left: 188px;
  }

  .fz-sidesheet__container--close-mobile{
    display: none;
  }
}
</style>
