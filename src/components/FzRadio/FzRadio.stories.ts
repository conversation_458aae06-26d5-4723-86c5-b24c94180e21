import type { <PERSON>a, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import { FzRadio, FzRadioGroup } from '.'

const meta = {
  title: 'Components/FzRadio',
  component: FzRadioGroup, // Story targets the group
  subcomponents: { FzRadio },
  tags: ['autodocs'],
  argTypes: {
    modelValue: { control: 'text', description: 'Selected value (v-model on FzRadioGroup)' },
    name: { control: 'text', description: 'Native name attribute for the radio group' },
    disabled: { control: 'boolean', description: 'Disables all radios in the group' },
    // FzRadio props are implicitly documented via subcomponents
  },
} satisfies Meta<typeof FzRadioGroup>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    name: 'options',
    modelValue: 'option1',
  },
  render: (args: any) => ({
    components: { FzRadio, FzRadioGroup },
    setup() {
      const selectedOption = ref('option1')
      const options = [
        { label: 'Option 1', value: 'option1', id: 'opt1' },
        { label: 'Option 2', value: 'option2', id: 'opt2' },
        { label: 'Option 3', value: 'option3', id: 'opt3' },
      ]
      return { args, selectedOption, options }
    },
    template: `
      <FzRadioGroup v-bind="args" v-model="selectedOption">
        <FzRadio
          v-for="option in options"
          :key="option.value"
          :value="option.value"
          :label="option.label"
          :id="option.id"
        />
      </FzRadioGroup>
      <p class="fz:mt-2">Selected: {{ selectedOption }}</p>
    `,
  }),
}

export const DisabledGroup: Story = {
  args: {
    name: 'disabled-options',
    disabled: true,
    modelValue: 'optionB', // Pre-select to show disabled state
  },
  render: (args: any) => ({
    components: { FzRadio, FzRadioGroup },
    setup() {
      const selectedOption = ref(args.modelValue)
      const options = [
        { label: 'Option A', value: 'optionA', id: 'dis-optA' },
        { label: 'Option B', value: 'optionB', id: 'dis-optB' },
        { label: 'Option C', value: 'optionC', id: 'dis-optC' },
      ]
      return { args, selectedOption, options }
    },
    template: `
      <FzRadioGroup v-bind="args" v-model="selectedOption">
        <FzRadio
          v-for="option in options"
          :key="option.value"
          :value="option.value"
          :label="option.label"
          :id="option.id"
        />
      </FzRadioGroup>
      <p class="fz:mt-2">Selected: {{ selectedOption }}</p>
    `,
  }),
}

export const IndividuallyDisabledRadio: Story = {
  args: {
    name: 'individual-disabled',
    modelValue: 'item1',
  },
  render: (args: any) => ({
    components: { FzRadio, FzRadioGroup },
    setup() {
      const selectedOption = ref('item1')
      const items = [
        { label: 'Item 1 (Enabled)', value: 'item1', id: 'ind-item1' },
        { label: 'Item 2 (Disabled)', value: 'item2', id: 'ind-item2', disabled: true },
        { label: 'Item 3 (Enabled)', value: 'item3', id: 'ind-item3' },
      ]
      return { args, selectedOption, items }
    },
    template: `
      <FzRadioGroup v-bind="args" v-model="selectedOption">
        <FzRadio
          v-for="item in items"
          :key="item.value"
          :value="item.value"
          :label="item.label"
          :id="item.id"
          :disabled="item.disabled"
        />
      </FzRadioGroup>
      <p class="fz:mt-2">Selected: {{ selectedOption }}</p>
    `,
  }),
}

export const NoLabels: Story = {
  args: {
    name: 'no-labels-group',
    modelValue: 'val1',
  },
  render: (args: any) => ({
    components: { FzRadio, FzRadioGroup },
    setup() {
      const selected = ref('val1')
      const values = ['val1', 'val2', 'val3']
      return { args, selected, values }
    },
    template: `
      <FzRadioGroup v-bind="args" v-model="selected">
        <FzRadio
          v-for="(val, index) in values"
          :key="val"
          :value="val"
          :id="'no-label-' + index"
        />
      </FzRadioGroup>
      <p class="fz:mt-2">Selected: {{ selected }}</p>
    `,
  }),
}

export const HorizontalLayout: Story = {
  args: {
    name: 'horizontal-options',
    modelValue: 'h-option1',
  },
  render: (args: any) => ({
    components: { FzRadio, FzRadioGroup },
    setup() {
      const selectedOption = ref('h-option1')
      const options = [
        { label: 'Horizontal 1', value: 'h-option1', id: 'h-opt1' },
        { label: 'Horizontal 2', value: 'h-option2', id: 'h-opt2' },
        { label: 'Horizontal 3', value: 'h-option3', id: 'h-opt3' },
      ]
      return { args, selectedOption, options }
    },
    template: `
      <FzRadioGroup v-bind="args" v-model="selectedOption" class="fz:flex-row fz:gap-6">
        <FzRadio
          v-for="option in options"
          :key="option.value"
          :value="option.value"
          :label="option.label"
          :id="option.id"
        />
      </FzRadioGroup>
      <p class="fz:mt-2">Selected: {{ selectedOption }}</p>
    `,
  }),
}
