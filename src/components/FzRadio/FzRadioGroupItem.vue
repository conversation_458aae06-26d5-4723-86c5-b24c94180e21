<script setup lang="ts">
import type { AcceptableValue } from 'reka-ui'
import { RadioGroupIndicator, RadioGroupItem } from 'reka-ui'
import { computed, getCurrentInstance, inject } from 'vue'

const props = defineProps<{
  modelValue?: AcceptableValue
  value: AcceptableValue
  label?: string
  disabled?: boolean
  checked?: boolean
  id?: string
  name?: string
  readonly?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: AcceptableValue): void
}>()

const uid = getCurrentInstance()?.uid
const radioId = computed(() => props.id || `fz-radio-${uid}`)

// Access potential radio group context
const groupName = inject<string | undefined>('fz-radio-group-name', undefined)
const groupModelValue = inject<{ value: AcceptableValue } | undefined>('fz-radio-group-model-value', undefined)
const groupDisabled = inject<boolean | undefined>('fz-radio-group-disabled', undefined)
const groupUpdateModelValue = inject<((value: AcceptableValue) => void) | undefined>('fz-radio-group-update-model-value', undefined)

// Support both standalone and group usage
const isDisabled = computed(() => props.disabled || groupDisabled)
const isChecked = computed(() => {
  if (props.checked !== undefined) {
    return props.checked
  }
  if (groupModelValue !== undefined) {
    return groupModelValue.value === props.value
  }
  return props.modelValue === props.value
})

function handleChange() {
  if (props.readonly)
    return
  if (!isDisabled.value) {
    if (groupModelValue !== undefined) {
      groupUpdateModelValue?.(props.value)
    }
    else {
      emit('update:modelValue', props.value)
    }
  }
}
</script>

<template>
  <label
    class="fz-radio"
    :class="{
      'fz-radio--disabled': isDisabled,
      'fz-radio--checked': isChecked,
    }"
    :for="radioId"
  >
    <RadioGroupItem
      :id="radioId"
      :value="value"
      class="fz-radio__input"
      :disabled="isDisabled"
      :name="groupName"
      @click.prevent="handleChange"
    >
      <RadioGroupIndicator class="fz-radio__indicator" />
    </RadioGroupItem>
    <span v-if="label" class="fz-radio__label">{{ label }}</span>
  </label>
</template>

<style>
.fz-radio {
  display: inline-flex;
  align-items: center;
  gap: var(--fz-spacing-4);
  cursor: pointer;
  font: var(--fz-font-control-text);
  color: var(--fz-color-text-primary);
}

.fz-radio--disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.fz-radio__input {
  appearance: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 2px solid var(--fz-color-border-input);
  border-radius: 50%;
  background-color: var(--fz-color-surface-light);
  outline: none;
  transition: all 0.2s ease;
}

.fz-radio__input:hover:not([data-disabled]) {
  border-color: var(--fz-color-border-input-focus);
}

.fz-radio__input:focus-visible:not([data-disabled]) {
  border-color: var(--fz-color-border-input-focus);
  box-shadow: 0 0 0 var(--fz-ring-offset-width) var(--fz-ring-offset-color),
    0 0 0 calc(var(--fz-ring-offset-width) + var(--fz-ring-width)) var(--fz-ring-primary-color);
}

.fz-radio__input[data-disabled] {
  background-color: var(--fz-color-surface-disabled);
  border-color: var(--fz-color-border-disabled);
}

.fz-radio__input[data-state="checked"] {
  border-color: var(--fz-color-surface-brand-primary);
  background-color: var(--fz-color-surface-light);
}

.fz-radio__indicator {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--fz-color-surface-brand-primary);
  transform: scale(0);
  transition: transform 0.2s ease;
}

.fz-radio__indicator[data-state="checked"] {
  transform: scale(1);
}

.fz-radio__label {
  user-select: none;
}
</style>
