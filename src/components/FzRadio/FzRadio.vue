<script setup lang="ts">
import type { AcceptableValue } from 'reka-ui'
import { computed, getCurrentInstance } from 'vue'

const props = defineProps<{
  modelValue?: AcceptableValue // The value of the radio group, if this radio is selected
  value: AcceptableValue // The unique value of this radio button
  label?: string
  disabled?: boolean
  id?: string
  name?: string
  readonly?: boolean // If true, user cannot change its state
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: typeof props.value): void
}>()

const uid = getCurrentInstance()?.uid
const radioId = computed(() => props.id || `fz-radio-${uid}`)

const isChecked = computed(() => props.modelValue === props.value)

function handleChange(event: Event) {
  if (props.readonly || props.disabled) {
    // Prevent default to stop the native browser radio change if readonly
    // For disabled, it's often handled by the browser, but this is an extra guard.
    event.preventDefault()
    return
  }
  // Check if it's not already checked to prevent redundant emits if that's desired,
  // though standard radio behavior allows "re-selecting" the current one.
  // For simplicity, we'll always emit.
  emit('update:modelValue', props.value)
}

function handleClick(event: MouseEvent) {
  if (props.readonly || props.disabled) {
    event.preventDefault() // Prevent label click from changing state for readonly/disabled
    return
  }
  // For readonly, we want to prevent the input from changing.
  // We ensure the modelValue is updated only via handleChange if not readonly.
  // If it's readonly, the click on label shouldn't trigger a change.
  if (props.readonly)
    return

  // Trigger a change as if the input itself was clicked,
  // ensuring the v-model updates correctly.
  // This handles clicking on the label.
  if (!props.disabled && !isChecked.value) {
    // Only emit if it's not disabled and not already checked,
    // to better mimic native radio behavior where clicking a checked radio's label
    // doesn't typically re-trigger events.
    // However, re-emitting is fine for v-model.
    emit('update:modelValue', props.value)
  }
  else if (!props.disabled && isChecked.value) {
    // If it's already checked and not disabled, we might not need to emit.
    // But for consistency with input's direct click, let's allow it.
    // This part is debatable based on exact desired UX for label clicks on active radios.
    // For now, let's ensure clicking label on an active radio still means "selecting" it.
    emit('update:modelValue', props.value)
  }
}
</script>

<template>
  <label
    class="fz-radio-simple"
    :class="{
      'fz-radio-simple--disabled': disabled,
      'fz-radio-simple--checked': isChecked,
      'fz-radio-simple--readonly': readonly,
    }"
    :for="radioId"
    @click="handleClick"
  >
    <input
      :id="radioId"
      type="radio"
      :name="name"
      :value="value"
      :checked="isChecked"
      :disabled="disabled || readonly"
      class="fz-radio-simple__input"
      @change="handleChange"
    >
    <span class="fz-radio-simple__indicator-container">
      <span class="fz-radio-simple__indicator" />
    </span>
    <span v-if="label" class="fz-radio-simple__label">{{ label }}</span>
  </label>
</template>

<style>
.fz-radio-simple {
  display: inline-flex;
  align-items: center;
  gap: var(--fz-spacing-4); /* 0.5rem or 8px if --fz-spacing-unit is 4px */
  cursor: pointer;
  font: var(--fz-font-control-text);
  color: var(--fz-color-text-primary);
  position: relative; /* For focus outline */
  user-select: none;
}

.fz-radio-simple--disabled {
  cursor: not-allowed;
  color: var(--fz-color-text-disabled); /* More specific disabled color */
  opacity: 0.6; /* General opacity for disabled state */
}
.fz-radio-simple--readonly {
  cursor: default; /* Or not-allowed if interaction should be fully blocked */
}
.fz-radio-simple--readonly .fz-radio-simple__label {
   color: var(--fz-color-text-secondary);
}

.fz-radio-simple__input {
  /* Visually hide the default radio button but keep it accessible */
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  white-space: nowrap;
  border: 0;
}

.fz-radio-simple__indicator-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* Control size */
  height: 20px; /* Control size */
  border: 2px solid var(--fz-color-border-input);
  border-radius: 50%;
  background-color: var(--fz-color-surface-light);
  transition: border-color 0.2s ease, background-color 0.2s ease;
  flex-shrink: 0; /* Prevent shrinking in flex layout */
}

.fz-radio-simple:hover .fz-radio-simple__indicator-container:not(.fz-radio-simple--disabled .fz-radio-simple__indicator-container) {
  border-color: var(--fz-color-border-input-focus);
}

/* Checked state for the custom indicator */
.fz-radio-simple--checked .fz-radio-simple__indicator-container {
  border-color: var(--fz-color-surface-brand-primary);
  /* background-color: var(--fz-color-surface-brand-primary); Optional: fill container */
}

.fz-radio-simple__indicator {
  width: 10px; /* Size of the inner dot */
  height: 10px; /* Size of the inner dot */
  border-radius: 50%;
  background-color: var(--fz-color-surface-brand-primary);
  transform: scale(0);
  transition: transform 0.2s ease;
}

.fz-radio-simple--checked .fz-radio-simple__indicator {
  transform: scale(1);
}

/* Disabled state for indicator */
.fz-radio-simple--disabled .fz-radio-simple__indicator-container {
  background-color: var(--fz-color-surface-disabled);
  border-color: var(--fz-color-border-disabled);
}
.fz-radio-simple--disabled .fz-radio-simple__indicator {
  background-color: var(--fz-color-text-disabled); /* Or a more subtle color */
}

/* Readonly state for indicator */
.fz-radio-simple--readonly .fz-radio-simple__indicator-container {
  border-color: var(--fz-color-border-input); /* Looks like a normal unchecked one */
}
.fz-radio-simple--readonly.fz-radio-simple--checked .fz-radio-simple__indicator-container {
  border-color: var(--fz-color-surface-brand-primary); /* Looks like a normal checked one */
}
.fz-radio-simple--readonly.fz-radio-simple--checked .fz-radio-simple__indicator {
   background-color: var(--fz-color-surface-brand-primary); /* Ensure dot is visible */
}

/* Focus state - applied to the label which wraps the input */
.fz-radio-simple__input:focus-visible + .fz-radio-simple__indicator-container {
  border-color: var(--fz-color-border-input-focus);
  box-shadow: 0 0 0 var(--fz-ring-offset-width) var(--fz-ring-offset-color),
    0 0 0 calc(var(--fz-ring-offset-width) + var(--fz-ring-width)) var(--fz-ring-primary-color);
}
.fz-radio-simple__input:focus-visible:checked + .fz-radio-simple__indicator-container {
   border-color: var(--fz-color-surface-brand-primary); /* Ensure focus on checked keeps brand color */
}

.fz-radio-simple__label {
  /* user-select: none; -- already on root */
  line-height: 1.2; /* Adjust for better alignment with custom radio */
}
</style>
