<script setup lang="ts">
import { RadioGroupRoot } from 'reka-ui'
import { computed, provide, toRefs } from 'vue'

const props = defineProps<{
  modelValue: any
  name?: string
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
}>()

const { modelValue, name, disabled } = toRefs(props)

provide('fzRadioGroupValue', modelValue)
provide('fzRadioGroupName', name)
provide('fzRadioGroupDisabled', disabled)
provide('fzRadioGroupUpdate', (value: any) => {
  emit('update:modelValue', value)
})

// This computed is for the Reka RadioGroupRoot, which might not be strictly necessary
// if FzRadio handles its own state based on injected values.
// However, it's good practice to pass the modelValue to the root.
const localValue = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})
</script>

<template>
  <RadioGroupRoot
    v-model="localValue"
    class="fz-radio-group"
    :name="name"
    :disabled="disabled"
  >
    <slot />
  </RadioGroupRoot>
</template>

<style>
.fz-radio-group {
  display: flex;
  flex-direction: column; /* Default to vertical stacking */
  gap: var(--fz-spacing-4); /* Default gap between radio items */
}
</style>
