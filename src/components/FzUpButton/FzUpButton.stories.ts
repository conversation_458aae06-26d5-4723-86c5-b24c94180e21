import type { Meta, StoryObj } from '@storybook/vue3'

import FzUpButton from './FzUpButton.vue'

const meta: Meta<typeof FzUpButton> = {
  title: 'Components/FzUpButton',
  component: FzUpButton,
  tags: ['autodocs'],
  argTypes: {
  },
  args: {},
}

export default meta
type Story = StoryObj<typeof FzUpButton>

export const Default: Story = {
  render: args => ({
    components: { FzUpButton },
    setup() {
      return { args }
    },
    template: `
      <div style="height: 400vh" />
      <FzUpButton />
    `,
  }),
  args: {
  },
}
