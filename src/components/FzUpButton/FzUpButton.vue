<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { FzIcon } from '../FzIcon'
import { FzTransition } from '../FzTransition'

const isVisible = ref(false)
const scrollThreshold = 220

function checkScroll() {
  isVisible.value = window.scrollY > scrollThreshold
}

function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  })
}

onMounted(() => {
  window.addEventListener('scroll', checkScroll, { passive: true })
  checkScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', checkScroll)
})
</script>

<template>
  <FzTransition
    name="scale-fade"
  >
    <div
      v-if="isVisible"
      class="fz-up-button"
      aria-label="Вернуться наверх"
      @click="scrollToTop"
    >
      <span class="fz-up-button__icon">
        <FzIcon name="tothetop" />
      </span>
    </div>
  </FzTransition>
</template>

<style>
.fz-up-button {
  position: fixed;
  bottom: 18px;
  right: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 100px;
  box-sizing: border-box;
  padding: var(--fz-spacing-3);
  background: var(--fz-color-surface-light);
  box-shadow: var(--fz-shadow-floating);
}

.fz-up-button__icon {
  transition: var(--fz-transition-duration);
  color: var(--fz-color-text-secondary);
}

.fz-up-button:hover .fz-up-button__icon{
  color: var(--fz-color-text-primary);
}
</style>
