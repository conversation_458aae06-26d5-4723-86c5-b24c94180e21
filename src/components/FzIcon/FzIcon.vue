<script setup lang="ts">
import type { Component } from 'vue'
import { computed } from 'vue'
import { getIconNames } from '../../util/helpers'

const props = defineProps<{
  name: string // Icon name (e.g., 'save', 'trash') - NO .svg extension
  clickable?: boolean // Whether the icon is clickable
  size?: 'sm' | 'md' | 'lg' // Size variant for container: sm (12px), md (16px), lg (24px)
  svgSize?: string // Direct control of SVG size (e.g., '8px', '10px', '14px')
  as?: string // Element to render the icon as (e.g., 'div', 'span', 'button'). Default is 'button' if clickable, otherwise 'span'
}>()

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

// Dynamically import all the icons
const icons = import.meta.glob('../../assets/icons/*.svg', {
  eager: true,
  import: 'default',
})

// Map icon names to imported components
const iconMap = computed<Record<string, Component>>(() => {
  const map: Record<string, Component> = {}

  // Get all available icon names
  const iconNames = getIconNames()

  // Map each icon name to its component
  iconNames.forEach((name) => {
    const iconPath = `../../assets/icons/${name}.svg`
    if (icons[iconPath]) {
      map[name] = icons[iconPath] as Component
    }
  })

  return map
})

const IconComponent = computed(() => {
  if (!iconMap.value[props.name]) {
    console.warn(`[FzIcon] Icon "${props.name}" not found.`)
    return null // Or return a default fallback icon component
  }
  return iconMap.value[props.name]
})

function handleClick(event: MouseEvent) {
  if (props.clickable) {
    event.stopPropagation()
    event.stopImmediatePropagation()
    event.preventDefault()
    emit('click', event)
  }
}

const iconClass = computed(() => {
  const classes = ['fz-icon']
  if (props.clickable) {
    classes.push('fz-icon--clickable')
  }

  if (props.size === 'sm') {
    classes.push('fz-icon--sm')
  }
  else if (props.size === 'lg') {
    classes.push('fz-icon--lg')
  }
  else {
    classes.push('fz-icon--md') // Default to md size
  }
  return classes.join(' ')
})

const svgStyle = computed(() => {
  if (props.svgSize) {
    return {
      width: props.svgSize,
      height: props.svgSize,
    }
  }
  return {}
})
</script>

<template>
  <component
    :is="as || (clickable ? 'button' : 'span')"
    v-if="IconComponent"
    :class="iconClass"
    @click="handleClick"
  >
    <component
      :is="IconComponent"
      :style="svgStyle"
    />
  </component>
</template>

<style>
.fz-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: currentColor;
  vertical-align: -0.125em;
}

/* Size variants */
.fz-icon--sm {
  width: 12px;
  height: 12px;
}

.fz-icon--md {
  width: 16px;
  height: 16px;
}

.fz-icon--lg {
  width: 24px;
  height: 24px;
}

/* Clickable styles */
.fz-icon--clickable {
  min-width: 16px;
  min-height: 16px;
  padding: 0;
  background: transparent;
  border: none;
  cursor: pointer;
  border-radius: 2px;
  transition: opacity 0.2s;
}

.fz-icon--clickable:hover {
  opacity: 0.8;
}

.fz-icon--clickable:focus {
  outline: none;
}

.fz-icon--clickable:focus-visible {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Ensure nested SVG scales */
.fz-icon :deep(svg) {
  display: block;
}
</style>
