import type { Meta, StoryObj } from '@storybook/vue3'
import { getIconNames } from '../../util/helpers'
import FzIcon from './FzIcon.vue'

// Get all available icons
const iconNames = getIconNames()

// Meta for the component
const meta = {
  title: 'Components/FzIcon',
  component: FzIcon,
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'select',
      options: iconNames,
      description: 'Icon name from the available icon set',
    },
    clickable: {
      control: 'boolean',
      description: 'Whether the icon is clickable',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the icon container: sm (12px), md (16px), lg (24px)',
    },
    svgSize: {
      control: 'text',
      description: 'Custom size for the SVG element (e.g., "10px", "14px")',
    },
    onClick: {
      action: 'clicked',
      description: 'Click event handler',
    },
  },
  args: {
    name: 'search',
    size: 'md',
  },
} satisfies Meta<typeof FzIcon>

export default meta
type Story = StoryObj<typeof meta>

// Base story
export const Default: Story = {
  args: {
    clickable: false,
  },
  render: args => ({
    components: { FzIcon },
    setup() {
      return { args }
    },
    template: '<FzIcon v-bind="args" @click="args.onClick" />',
  }),
}

// Clickable icon story
export const Clickable: Story = {
  args: {
    clickable: true,
  },
  render: args => ({
    components: { FzIcon },
    setup() {
      return { args }
    },
    template: '<FzIcon v-bind="args" @click="args.onClick" />',
  }),
}

// Icon gallery to showcase all icons
export const IconGallery: Story = {
  render: () => ({
    components: { FzIcon },
    setup() {
      return { iconNames }
    },
    template: `
      <div class="fz:p-4">
        <h3 class="fz:text-lg fz:font-bold fz:mb-4">All Available Icons</h3>
        <div class="fz:grid fz:grid-cols-5 fz:gap-4">
          <div 
            v-for="iconName in iconNames" 
            :key="iconName" 
            class="fz:flex fz:flex-col fz:items-center fz:p-3 fz:border fz:rounded"
          >
            <FzIcon :name="iconName" class="fz:mb-2" />
            <span class="fz:text-xs fz:text-gray-600">{{ iconName }}</span>
          </div>
        </div>
      </div>
    `,
  }),
}

// Size variants
export const SizeVariants: Story = {
  render: () => ({
    components: { FzIcon },
    template: `
      <div class="fz:p-4 fz:space-y-4">
        <div class="fz:flex fz:items-center fz:space-x-4">
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzIcon name="search" size="sm" />
            <span class="fz:mt-2 fz:text-xs">sm (12px)</span>
          </div>
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzIcon name="search" size="md" />
            <span class="fz:mt-2 fz:text-xs">md (16px)</span>
          </div>
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzIcon name="search" size="lg" />
            <span class="fz:mt-2 fz:text-xs">lg (24px)</span>
          </div>
        </div>

        <h4 class="fz:text-sm fz:font-semibold fz:mt-4">Custom SVG Sizes</h4>
        <div class="fz:flex fz:items-center fz:space-x-4">
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzIcon name="search" svgSize="12px" size="sm" />
            <span class="fz:mt-2 fz:text-xs">12px</span>
          </div>
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzIcon name="search" svgSize="16px" size="md" />
            <span class="fz:mt-2 fz:text-xs">16px</span>
          </div>
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzIcon name="search" svgSize="24px" size="lg" />
            <span class="fz:mt-2 fz:text-xs">24px</span>
          </div>
        </div>
      </div>
    `,
  }),
}

// Clickable examples with minimum touch area
export const ClickableExamples: Story = {
  render: () => ({
    components: { FzIcon },
    methods: {
      handleClick(_iconName: string) {
        // We would normally handle the click in a real app
        // For demo purposes we're just acknowledging the click
      },
    },
    template: `
      <div class="fz:p-4 fz:space-y-6">
        <div>
          <h4 class="fz:text-sm fz:font-semibold fz:mb-2">Clickable Icons (16px minimum touch area)</h4>
          <div class="fz:flex fz:items-center fz:space-x-4 fz:border fz:p-3 fz:rounded">
            <FzIcon name="x" clickable @click="handleClick('x')" />
            <FzIcon name="search" clickable @click="handleClick('search')" />
            <FzIcon name="trash" clickable @click="handleClick('trash')" />
          </div>
        </div>
        
        <div>
          <h4 class="fz:text-sm fz:font-semibold fz:mb-2">Small SVG in Clickable Container</h4>
          <div class="fz:flex fz:items-center fz:space-x-4 fz:border fz:p-3 fz:rounded">
            <FzIcon name="x" clickable svgSize="10px" @click="handleClick('x small')" />
            <FzIcon name="search" clickable svgSize="12px" @click="handleClick('search small')" />
          </div>
          <span class="fz:text-xs fz:text-gray-600 fz:mt-1 fz:block">
            Note: Container maintains 16px minimum touch area while SVG is smaller
          </span>
        </div>
        
        <div>
          <h4 class="fz:text-sm fz:font-semibold fz:mb-2">Non-clickable Small Icons (12px)</h4>
          <div class="fz:flex fz:items-center fz:space-x-4 fz:border fz:p-3 fz:rounded">
            <FzIcon name="arrowdown-table" size="sm" />
            <FzIcon name="arrowup-table" size="sm" />
            <FzIcon name="filter-table" size="sm" />
          </div>
        </div>
      </div>
    `,
  }),
}
