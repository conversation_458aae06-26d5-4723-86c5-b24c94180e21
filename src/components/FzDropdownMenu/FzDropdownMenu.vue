<script setup lang="ts">
import {
  DropdownMenuArrow,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRoot,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from 'reka-ui'
import { computed } from 'vue'
import FzIcon from '../FzIcon/FzIcon.vue'

export interface DropdownItem {
  label: string
  value: any
  icon?: string
  disabled?: boolean
  group?: string
  type?: 'item' | 'separator'
}

const props = withDefaults(
  defineProps<{
    modelValue?: any
    items: DropdownItem[]
    placeholder?: string
    disabled?: boolean
    asChild?: boolean
    showArrow?: boolean
    sideOffset?: number
    contentClass?: string
    itemClass?: string
    side?: 'top' | 'right' | 'bottom' | 'left'
    align?: 'start' | 'center' | 'end'
  }>(),
  {
    placeholder: 'Select…',
    disabled: false,
    asChild: false,
    showArrow: true,
    sideOffset: 8,
    contentClass: '',
    itemClass: '',
    side: 'bottom',
    align: 'center',
  },
)

const emit = defineEmits<{
  'update:modelValue': [value: any]
  'select': [value: any]
}>()

// New processedList computed property
const processedList = computed(() => {
  const result: (DropdownItem | { isGroup: true, name: string, items: DropdownItem[] })[] = []
  let currentGroupItems: DropdownItem[] = []
  let currentGroupName: string | undefined

  // Filter out adjacent separators first
  const cleanedItems = props.items.reduce((acc, current, index, array) => {
    if (current.type === 'separator') {
      const prevItem = array[index - 1]
      if (index === 0 || (prevItem && prevItem.type === 'separator')) {
        return acc
      }
    }
    acc.push(current)
    return acc
  }, [] as DropdownItem[])

  function flushCurrentGroup() {
    if (currentGroupItems.length > 0 && currentGroupName) {
      result.push({ isGroup: true, name: currentGroupName, items: [...currentGroupItems] })
    }
    currentGroupItems = []
    currentGroupName = undefined
  }

  cleanedItems.forEach((item) => {
    if (item.type === 'separator') {
      flushCurrentGroup()
      result.push(item)
    }
    else if (item.group) {
      if (item.group !== currentGroupName) {
        flushCurrentGroup()
        currentGroupName = item.group
      }
      currentGroupItems.push(item)
    }
    else { // Ungrouped item (not a separator, not part of a group)
      flushCurrentGroup()
      result.push(item)
    }
  })

  flushCurrentGroup() // Flush any remaining group at the end
  return result
})

// Handle selection
function onSelect(val: any) {
  emit('update:modelValue', val)
  emit('select', val)
}
</script>

<template>
  <DropdownMenuRoot :disabled="props.disabled">
    <DropdownMenuTrigger
      :as-child="true"
    >
      <slot />
    </DropdownMenuTrigger>

    <DropdownMenuPortal>
      <DropdownMenuContent
        class="fz-dropdown__content"
        :class="props.contentClass"
        :side-offset="props.sideOffset"
        :side="props.side"
        :align="props.align"
      >
        <DropdownMenuArrow v-if="props.showArrow" :width="10" :height="10" as-child>
          <div class="fz-dropdown__arrow" />
        </DropdownMenuArrow>

        <div class="fz-dropdown__content-inner">
          <ul class="dropdown-menu-item-list fz:list-none fz:p-0 fz:m-0">
            <template v-for="(entry, index) in processedList" :key="index">
              <template v-if="'type' in entry && entry.type === 'separator'">
                <DropdownMenuSeparator class="fz-dropdown__separator" />
              </template>
              <template v-else-if="'isGroup' in entry && entry.isGroup">
                <DropdownMenuGroup>
                  <DropdownMenuLabel class="fz-dropdown__group-label">
                    {{ entry.name }}
                  </DropdownMenuLabel>
                  <DropdownMenuItem
                    v-for="itemInGroup in entry.items"
                    :key="itemInGroup.value"
                    :disabled="itemInGroup.disabled"
                    as="li"
                    class="fz-dropdown__item"
                    :class="[
                      props.itemClass,
                      { 'fz-dropdown__item--with-icon': itemInGroup.icon },
                      { 'fz-dropdown__item--selected': itemInGroup.value === props.modelValue },
                    ]"
                    @select="onSelect(itemInGroup.value)"
                  >
                    <div class="fz-dropdown__item-icon">
                      <FzIcon v-if="itemInGroup.icon" :name="itemInGroup.icon as string" size="lg" />
                    </div>
                    <span class="fz-dropdown__item-label">{{ itemInGroup.label }}</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </template>
              <template v-else>
                <!-- entry is a DropdownItem (not separator, not group) -->
                <DropdownMenuItem
                  :disabled="(entry as DropdownItem).disabled"
                  as="li"
                  class="fz-dropdown__item"
                  :class="[
                    props.itemClass,
                    { 'fz-dropdown__item--with-icon': (entry as DropdownItem).icon },
                    { 'fz-dropdown__item--selected': (entry as DropdownItem).value === props.modelValue },
                  ]"
                  @select="onSelect((entry as DropdownItem).value)"
                >
                  <div class="fz-dropdown__item-icon">
                    <FzIcon v-if="(entry as DropdownItem).icon" :name="(entry as DropdownItem).icon as string" size="lg" />
                  </div>
                  <span class="fz-dropdown__item-label">{{ (entry as DropdownItem).label }}</span>
                </DropdownMenuItem>
              </template>
            </template>
          </ul>
        </div>
      </DropdownMenuContent>
    </DropdownMenuPortal>
  </DropdownMenuRoot>
</template>

<style>
.fz-dropdown__trigger {
  all: unset;
}

.fz-dropdown__content {
  width: 240px;
  display: flex;
  flex-direction: column;
  gap: var(--fz-spacing-2);
}

/*
  This is a hack to ensure the arrow is above the content
*/
.fz-dropdown__content > span {
  z-index: 1;
}

.fz-dropdown__content-inner {
  position: relative;
  padding: var(--fz-spacing-8);
  background-color: var(--fz-color-surface-light);
  box-shadow: var(--fz-shadow-dropdown);
  border-radius: var(--fz-radius-sm);
}

.fz-dropdown__arrow {
  background-color: var(--fz-color-surface-light);
  width: 10px;
  height: 10px;
  border-radius: 1px;
  transform: rotate(45deg) translateX(-7px);
  z-index: 1;
}

.fz-dropdown__item {
  padding: var(--fz-spacing-4) var(--fz-spacing-4);
  border-radius: var(--fz-radius-xs);
  cursor: pointer;
  transition: background-color var(--fz-transition-duration) var(--fz-transition-timing-function);
  display: flex;
  align-items: center;
  font: var(--fz-font-control-text);
  gap: var(--fz-spacing-4);
}

.fz-dropdown__item[aria-disabled="true"] {
  opacity: 0.5;
  cursor: not-allowed;
}

.fz-dropdown__item--selected {
  background-color: var(--fz-color-surface-ghost-hover);
}

.fz-dropdown__item:hover,
.fz-dropdown__item:focus-visible {
  background-color: var(--fz-color-surface-ghost-hover);
}

.fz-dropdown__item:active {
  background-color: var(--fz-color-surface-ghost-clicked);
}

.fz-dropdown__item--with-icon {
  padding-left: var(--fz-spacing-2);
}

.fz-dropdown__item-icon {
  color: var(--fz-color-border-icon);
}

.fz-dropdown__item-label {
  color: var(--fz-color-text-primary);
}

.fz-dropdown__separator {
  height: 1px;
  background-color: var(--fz-color-border-divider);
  margin: var(--fz-spacing-2) 0;
}

.fz-dropdown__group-label {
  padding: var(--fz-spacing-3) var(--fz-spacing-4);
  color: var(--fz-color-text-secondary);
  font-size: var(--fz-font-size-sm);
  font-weight: var(--fz-font-weight-medium);
}
</style>
