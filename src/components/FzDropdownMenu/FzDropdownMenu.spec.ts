import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'
import { FzDropdownMenu } from '.'

// Mock the reka-ui components
vi.mock('reka-ui', () => ({
  DropdownMenuRoot: {
    name: 'DropdownMenuRoot',
    template: '<div><slot></slot></div>',
    props: ['disabled'],
  },
  DropdownMenuTrigger: {
    name: 'DropdownMenuTrigger',
    template: '<button><slot></slot></button>',
    props: ['asChild'],
  },
  DropdownMenuPortal: {
    name: 'DropdownMenuPortal',
    template: '<div><slot></slot></div>',
  },
  DropdownMenuContent: {
    name: 'DropdownMenuContent',
    template: '<div data-testid="dropdown-content"><slot></slot></div>',
    props: ['sideOffset'],
  },
  DropdownMenuArrow: {
    name: 'DropdownMenuArrow',
    template: '<div data-testid="dropdown-arrow"></div>',
  },
  DropdownMenuItem: {
    name: 'DropdownMenuItem',
    template: '<div data-testid="dropdown-item" @click="$emit(\'select\')"><slot></slot></div>',
    props: ['disabled'],
    emits: ['select'],
  },
}))

describe('fzDropdownMenu', () => {
  const mockItems = [
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' },
    { label: 'Option 3', value: '3', disabled: true },
  ]

  it('renders correctly with default props', () => {
    const wrapper = mount(FzDropdownMenu, {
      props: {
        items: mockItems,
      },
    })

    expect(wrapper.find('.fz-dropdown__trigger').exists()).toBe(true)
    expect(wrapper.find('[data-testid="dropdown-content"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="dropdown-arrow"]').exists()).toBe(true)
    expect(wrapper.findAll('[data-testid="dropdown-item"]')).toHaveLength(3)
  })

  it('displays placeholder when no value is selected', () => {
    const placeholder = 'Select an option...'
    const wrapper = mount(FzDropdownMenu, {
      props: {
        items: mockItems,
        placeholder,
      },
    })

    expect(wrapper.find('.fz-dropdown__trigger').text()).toBe(placeholder)
  })

  it('displays selected item label when a value is selected', () => {
    const wrapper = mount(FzDropdownMenu, {
      props: {
        items: mockItems,
        modelValue: '2',
      },
    })

    expect(wrapper.find('.fz-dropdown__trigger').text()).toBe('Option 2')
  })

  it('hides arrow when showArrow is false', () => {
    const wrapper = mount(FzDropdownMenu, {
      props: {
        items: mockItems,
        showArrow: false,
      },
    })

    expect(wrapper.find('[data-testid="dropdown-arrow"]').exists()).toBe(false)
  })

  it('emits update:modelValue and select events when an item is selected', async () => {
    const wrapper = mount(FzDropdownMenu, {
      props: {
        items: mockItems,
      },
    })

    await wrapper.findAll('[data-testid="dropdown-item"]')[0].trigger('click')

    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')![0]).toEqual(['1'])

    expect(wrapper.emitted('select')).toBeTruthy()
    expect(wrapper.emitted('select')![0]).toEqual(['1'])
  })

  it('applies custom classes when provided', () => {
    const wrapper = mount(FzDropdownMenu, {
      props: {
        items: mockItems,
        contentClass: 'custom-content',
        itemClass: 'custom-item',
      },
    })

    expect(wrapper.find('[data-testid="dropdown-content"]').classes()).toContain('custom-content')
    expect(wrapper.findAll('[data-testid="dropdown-item"]')[0].classes()).toContain('custom-item')
  })
})
