import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import FzDropdownMenu from './FzDropdownMenu.vue'

// More on how to set up stories at: https://storybook.js.org/docs/vue/writing-stories/introduction
const meta = {
  title: 'Components/FzDropdownMenu',
  component: FzDropdownMenu,
  tags: ['autodocs'],
  argTypes: {
    modelValue: {
      control: 'text',
      description: 'Currently selected value (v-model)',
    },
    items: {
      control: 'object',
      description: 'Array of dropdown items',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text when no item is selected',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the dropdown is disabled',
    },
    showArrow: {
      control: 'boolean',
      description: 'Whether to show the dropdown arrow',
    },
    sideOffset: {
      control: 'number',
      description: 'Offset from the trigger',
    },
    contentClass: {
      control: 'text',
      description: 'Additional classes for the dropdown content',
    },
    itemClass: {
      control: 'text',
      description: 'Additional classes for the dropdown items',
    },
  },
} satisfies Meta<typeof FzDropdownMenu>

export default meta
type Story = StoryObj<typeof meta>

// Mock items
const mockItems = [
  { label: 'Option 1', value: '1' },
  { label: 'Option 2', value: '2' },
  { label: 'Option 3', value: '3', disabled: true },
  { label: 'Option 4', value: '4' },
]

// Basic example
export const Basic: Story = {
  args: {
    items: mockItems,
    placeholder: 'Select an option...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      return {
        items: args.items,
        placeholder: args.placeholder,
      }
    },
    template: `
      <FzDropdownMenu :items="items">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
    `,
  }),
}

// With icons
export const WithIcons: Story = {
  args: {
    items: [
      { label: 'Settings', value: 'settings', icon: 'settings' },
      { label: 'User', value: 'user', icon: 'user' },
      { label: 'Log out', value: 'logout', icon: 'log-out' },
    ],
    placeholder: 'Select an option...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      return {
        items: args.items,
        placeholder: args.placeholder,
      }
    },
    template: `
      <FzDropdownMenu :items="items">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
    `,
  }),
}

// Without arrow
export const WithoutArrow: Story = {
  args: {
    items: mockItems,
    showArrow: false,
    placeholder: 'Select an option...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      return {
        items: args.items,
        showArrow: args.showArrow,
        placeholder: args.placeholder,
      }
    },
    template: `
      <FzDropdownMenu :items="items" :show-arrow="showArrow">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
    `,
  }),
}

// Disabled
export const Disabled: Story = {
  args: {
    items: mockItems,
    disabled: true,
    placeholder: 'Select an option...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      return {
        items: args.items,
        disabled: args.disabled,
        placeholder: args.placeholder,
      }
    },
    template: `
      <FzDropdownMenu :items="items" :disabled="disabled">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
    `,
  }),
}

// With selected value
export const WithSelectedValue: Story = {
  args: {
    items: mockItems,
    modelValue: '2',
    placeholder: 'Select an option...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      const value = ref(args.modelValue)
      return {
        items: args.items,
        placeholder: args.placeholder,
        value,
      }
    },
    template: `
      <FzDropdownMenu v-model="value" :items="items">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
      <div class="fz:p-4">
          <p>Selected value: {{ value }}</p>
      </div>
    `,
  }),
}

// With separators
export const WithSeparators: Story = {
  args: {
    items: [
      { label: 'Edit', value: 'edit', icon: 'edit' },
      { label: 'Duplicate', value: 'duplicate', icon: 'copy' },
      { type: 'separator', label: '', value: 'sep1' },
      { label: 'Archive', value: 'archive', icon: 'archive' },
      { type: 'separator', label: '', value: 'sep2' },
      { label: 'Delete', value: 'delete', icon: 'trash', disabled: true },
    ],
    placeholder: 'Actions...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      return {
        items: args.items,
        placeholder: args.placeholder,
      }
    },
    template: `
      <FzDropdownMenu :items="items">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
    `,
  }),
}

// With grouped items
export const WithGroupedItems: Story = {
  args: {
    items: [
      { label: 'New File', value: 'new-file', icon: 'file', group: 'File Operations' },
      { label: 'Open', value: 'open', icon: 'folder-open', group: 'File Operations' },
      { label: 'Save', value: 'save', icon: 'save', group: 'File Operations' },
      { label: 'Cut', value: 'cut', icon: 'scissors', group: 'Edit Operations' },
      { label: 'Copy', value: 'copy', icon: 'copy', group: 'Edit Operations' },
      { label: 'Paste', value: 'paste', icon: 'clipboard', group: 'Edit Operations' },
      { label: 'Help', value: 'help', icon: 'help-circle' },
      { label: 'About', value: 'about', icon: 'info' },
    ],
    placeholder: 'Select an action...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      return {
        items: args.items,
        placeholder: args.placeholder,
      }
    },
    template: `
      <FzDropdownMenu :items="items">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
    `,
  }),
}

// Combined: Groups and separators
export const WithGroupsAndSeparators: Story = {
  args: {
    items: [
      { label: 'Recent', value: 'recent', icon: 'clock' },
      { type: 'separator', label: '', value: 'sep1' },
      { label: 'Google Drive', value: 'gdrive', icon: 'drive', group: 'Cloud Storage' },
      { label: 'Dropbox', value: 'dropbox', icon: 'dropbox', group: 'Cloud Storage' },
      { label: 'OneDrive', value: 'onedrive', icon: 'cloud', group: 'Cloud Storage' },
      { type: 'separator', label: '', value: 'sep2' },
      { label: 'Documents', value: 'documents', icon: 'folder', group: 'Local Storage' },
      { label: 'Desktop', value: 'desktop', icon: 'monitor', group: 'Local Storage' },
      { label: 'Downloads', value: 'downloads', icon: 'download', group: 'Local Storage' },
    ],
    placeholder: 'Select storage location...',
  },
  render: args => ({
    components: { FzDropdownMenu },
    setup() {
      return {
        items: args.items,
        placeholder: args.placeholder,
      }
    },
    template: `
      <FzDropdownMenu :items="items">
        <button>{{placeholder}}</button>
      </FzDropdownMenu>
    `,
  }),
}
