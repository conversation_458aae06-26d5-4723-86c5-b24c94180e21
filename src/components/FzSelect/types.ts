import type { AcceptableValue } from 'reka-ui'

/**
 * @description Примитивное значение для внутреннего 'value' объекта FzSelectOptionValueObject.
 */
export type FzSelectPrimitiveValue = AcceptableValue

/**
 * @description Тип для фактического значения опции выбора.
 * Может быть примитивным значением или структурированным объектом FzSelectOption.
 */
export type FzSelectValue = FzSelectPrimitiveValue | FzSelectOption

/**
 * @description Интерфейс для структурированного объекта опции.
 */
export interface FzSelectOption {
  label: string
  value: FzSelectPrimitiveValue
  children?: FzSelectOption[]
  [key: string]: any
}

/**
 * @description Базовый интерфейс для опции FzSelect.
 * @template V Тип значения опции
 */
export interface FzSelectOptionBase<V extends FzSelectOption = FzSelectOption> {
  /**
   * @description Отображаемый текст опции.
   */
  label: string
  /**
   * @description Уникальное значение опции (теперь структурированный объект).
   */
  value: V
  /**
   * @description Флаг, указывающий, отключена ли опция.
   * @default false
   */
  disabled?: boolean
  /**
   * @description Позволяет передавать любые другие поля в объекте опции.
   */
  [key: string]: any
}

/**
 * @description Интерфейс для опции FzSelect в режиме дерева. Расширяет FzSelectOptionBase.
 * @template V Тип значения опции, теперь FzSelectOptionValueObject.
 */
export interface FzSelectTreeOption<V extends FzSelectOption = FzSelectOption> extends FzSelectOptionBase<V> {
  /**
   * @description Дочерние опции для узла дерева.
   */
  children?: FzSelectTreeOption<V>[]
}

/**
 * @description Общий тип для опции, используемый в FzSelectProps.
 * По умолчанию соответствует FzSelectTreeOption для поддержки свойства children.
 */
export type FzSelectDefaultOptionType = FzSelectTreeOption<FzSelectOption>

/**
 * @description Базовые свойства компонента FzSelect, не зависящие от типа опции.
 */
export interface FzSelectPropsBase {
  /**
   * @description Текст-плейсхолдер, когда ничего не выбрано.
   */
  placeholder?: string
  /**
   * @description Разрешает выбор нескольких опций.
   * @default false
   */
  multiple?: boolean
  /**
   * @description Показывает кнопку для очистки выбора (только для одиночного выбора).
   * @default false
   */
  clearable?: boolean
  /**
   * @description Указывает, является ли поле невалидным (визуальное отображение ошибки).
   * @default false
   */
  invalid?: boolean
  /**
   * @description Включает поле для поиска опций.
   * @default false
   */
  searchable?: boolean
  /**
   * @description Текст-заполнитель для поля поиска.
   */
  searchPlaceholder?: string
  /**
   * @description Отключает компонент.
   * @default false
   */
  disabled?: boolean
  /**
   * @description Включает режим дерева.
   * @default false
   */
  tree?: boolean
  /**
   * @description Тип элемента в выпадающем списке.
   * @default 'default'
   */
  itemType?: 'default' | 'checkbox'
  /**
   * @description Визуальный вариант компонента.
   * @default 'borderline'
   */
  variant?: 'borderline' | 'underline'
  /**
   * @description Показывает кнопку "Сбросить все" (только для множественного выбора).
   * @default false
   */
  showResetButton?: boolean
  /**
   * @description Уникальный идентификатор для компонента.
   */
  id?: string
  /**
   * @description Имя для элемента формы.
   */
  name?: string

  /**
   * @description Максимальная высота выпадающего списка.
   */
  dropdownMaxHeight?: string

  /**
   * @description Показывает индикатор загрузки.
   * @default false
   */
  loading?: boolean
  /**
   * @description Текст, отображаемый при отсутствии результатов поиска.
   * @default 'Совпадений не найдено'
   */
  noResultsText?: string
  /**
   * @description Включает виртуализацию для длинных списков опций.
   * @default false
   */
  virtualize?: boolean
  /**
   * @description Примерный размер элемента для виртуализации.
   * @default 36
   */
  virtualizeEstimateSize?: number
  /**
   * @description Показывает состояние ошибки (например, при неудачной загрузке опций).
   * @default false
   */
  error?: boolean
  /**
   * @description Текст ошибки.
   * @default 'Ошибка при загрузке'
   */
  errorText?: string
}

/**
 * @description Полные свойства компонента FzSelect, включая зависящие от типа опции.
 * @template OptionType Тип объекта опции. Должен расширять FzSelectOptionBase.
 */
export interface FzSelectProps<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType> extends FzSelectPropsBase {
  /**
   * @description Текущее выбранное значение (или массив значений для multiple).
   */
  modelValue: OptionType | OptionType[] | null
  /**
   * @description Массив доступных опций.
   */
  options: ReadonlyArray<OptionType>
  /**
   * @description Ключ или функция для получения значения опции.
   * @default 'value'
   */
  optionValue?: string | ((option: OptionType) => FzSelectOption)
  /**
   * @description Ключ или функция для получения метки опции.
   * @default 'label'
   */
  optionLabel?: string | ((option: OptionType) => string)
  /**
   * @description Ключ или функция для определения, отключена ли опция.
   * @default 'disabled'
   */
  optionDisabled?: string | ((option: OptionType) => boolean)
  /**
   * @description Ключ или функция для получения дочерних опций (в режиме дерева).
   * @default 'children'
   */
  optionChildren?: string | ((option: OptionType) => ReadonlyArray<OptionType> | undefined)
}

export interface FzSelectPathSegment {
  truncatedValue: string | null
  originalValue: string | null
  isParent: boolean
}

export interface FzSelectTriggerDisplayValueTreeItem {
  value: FzSelectPrimitiveValue
  pathSegments: FzSelectPathSegment[]
  fullPath: string
}

export type FzSelectTriggerDisplayValue<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType> =
  | Array<FzSelectTriggerDisplayValueTreeItem>
  | OptionType[]
  | null
  | undefined
  | string

/**
 * @description Интерфейс для "сплющенной" опции, используемой для отображения.
 */
export interface FlattenedOption<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType> {
  /**
   * @description Оригинальный объект опции.
   */
  originalValue: OptionType
  /**
   * @description Структурированный объект значения, используемый для внутреннего представления.
   */
  value: FzSelectOption
  /**
   * @description Действительное значение опции (результат getActualOptionValue).
   */
  actualValue: FzSelectPrimitiveValue | FzSelectOption
  /**
   * @description Отображаемая метка опции.
   */
  label: string
  /**
   * @description Уровень вложенности в дереве (0 для корневых элементов).
   */
  level: number
  /**
   * @description Флаг, указывающий, есть ли у опции дочерние элементы.
   */
  hasChildren: boolean
  /**
   * @description Флаг, указывающий, отключена ли опция (уже разрешенный).
   */
  disabled: boolean
}
