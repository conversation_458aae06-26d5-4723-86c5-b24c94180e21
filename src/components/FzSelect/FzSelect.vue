<script
  setup
  lang="ts"
  generic="OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType"
>
import type { FzSelectEmit } from './composables/useFzSelectSelectionLogic'
import type {
  FzSelectDefaultOptionType,
  FzSelectOption,
  FzSelectOptionBase,
  FzSelectProps,
} from './types'

import {
  ComboboxContent,
  ComboboxEmpty,
  ComboboxInput,
  ComboboxItem,
  ComboboxPortal,
  ComboboxRoot,
} from 'reka-ui'
import { computed, getCurrentInstance, ref, watch } from 'vue'
import { FzButton } from '../FzButton'
import { FzCheckbox } from '../FzCheckbox'
import { FzCustomScrollbar } from '../FzCustomScrollbar'
import { FzIcon } from '../FzIcon'

import { FzLoader } from '../FzLoader'

import { useFzSelectClasses } from './composables/useFzSelectClasses'
import { useFzSelectDisplayLogic } from './composables/useFzSelectDisplayLogic'
import { useFzSelectPropsParser } from './composables/useFzSelectPropsParser'
import { useFzSelectSelectionLogic } from './composables/useFzSelectSelectionLogic'
import { useFzSelectTreeLogic } from './composables/useFzSelectTreeLogic'
import { useFzSelectVirtualization } from './composables/useFzSelectVirtualization'
import { FzSelectTrigger } from './FzSelectTrigger'

type Props = FzSelectProps<OptionType>

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Select an option',
  multiple: false,
  clearable: false,
  invalid: false,
  searchable: false,
  disabled: false,
  tree: false,
  variant: 'borderline',
  itemType: 'default',
  optionValue: 'value',
  optionLabel: 'label',
  optionDisabled: 'disabled',
  optionChildren: 'children',
  dropdownMaxHeight: '300px',

  loading: false,
  noResultsText: 'Совпадений не найдено',
  virtualize: false,
  virtualizeEstimateSize: 36,
  error: false,
  errorText: 'Ошибка при загрузке',
  showResetButton: false,
})

const emit = defineEmits<FzSelectEmit>()

// Локальное реактивное состояние
const open = ref(false)
const searchTerm = ref('')
const searchInputRef = ref<HTMLInputElement | null>(null)

// Уникальный ID для компонента select
const uid = getCurrentInstance()?.uid
const selectId = computed(() => props.id || `fz-select-${uid}`)

// Инициализация композаблов
const parsedPropsAccessors = useFzSelectPropsParser<OptionType>(props)

const treeLogic = useFzSelectTreeLogic<OptionType>(props, parsedPropsAccessors)

const selectionLogic = useFzSelectSelectionLogic<OptionType>(
  props,
  () => props.modelValue,
  emit,
  parsedPropsAccessors,
  treeLogic,
)

const displayLogic = useFzSelectDisplayLogic<OptionType>(
  props,
  () => props.modelValue,
  searchTerm,
  parsedPropsAccessors,
  treeLogic,
  selectionLogic,
)

const classes = useFzSelectClasses<OptionType>(props, open)

// Логика виртуализации и скроллбара
const virtualizationLogic = useFzSelectVirtualization<OptionType>(
  props,
  displayLogic.finalDisplayOptions,
)

// Экспорт необходимых значений в шаблон
const {
  comboboxModelValue,
  isOptionSelected,
  getCheckboxState,
  handleCheckboxSelect,
  handleTagDelete,
  clearSelection,
  preventSelectIfMultipleTree,
} = selectionLogic

const {
  finalDisplayOptions,
  triggerDisplayValue,
  defaultTags,
  showClearButton,
  showResetButton,
} = displayLogic

const { comboboxRootClass, comboboxTriggerClass, comboboxContentClass }
  = classes

const { compareOptionsByActualValue, getItemTextValue, getActualOptionValue }
  = parsedPropsAccessors

const { getOptionFullPath } = treeLogic

const { customScrollbarRef, virtualizer } = virtualizationLogic

// Локальный обработчик события клика по триггеру, взаимодействует с preventOpen из selectionLogic
function onTriggerClick(event: MouseEvent) {
  if (selectionLogic.preventOpen.value) {
    event.preventDefault()
    event.stopPropagation()
    event.stopImmediatePropagation()
    open.value = false
    selectionLogic.preventOpen.value = false
  }
}

// Отслеживание изменений open для генерации события 'openChange'
watch(open, (newVal) => {
  emit('openChange', newVal)
})
</script>

<template>
  <ComboboxRoot
    v-model="comboboxModelValue"
    v-model:open="open"
    :multiple="props.multiple"
    :disabled="props.disabled"
    :name="props.name"
    :by="compareOptionsByActualValue"
    :class="comboboxRootClass"
    :ignore-filter="true"
    :reset-search-term-on-select="false"
    :reset-search-term-on-blur="false"
  >
    <FzSelectTrigger
      :select-id="selectId"
      :disabled="props.disabled"
      :multiple="props.multiple"
      :tree="props.tree"
      :placeholder="props.placeholder"
      :trigger-display-value="triggerDisplayValue"
      :default-tags="defaultTags"
      :show-clear-button="showClearButton"
      :model-value="props.modelValue"
      :combobox-trigger-class="comboboxTriggerClass"
      :get-option-full-path="getOptionFullPath"
      :get-actual-option-value="getActualOptionValue"
      @clear-selection="clearSelection"
      @tag-delete="handleTagDelete"
      @trigger-click="onTriggerClick"
    />

    <ComboboxPortal>
      <ComboboxContent
        :class="comboboxContentClass"
        position="popper"
        side="bottom"
        align="start"
        :side-offset="4"
        :style="{ maxHeight: props.dropdownMaxHeight }"
      >
        <div class="fz-select__content-inner">
          <div v-if="props.searchable" class="fz-select__search-container">
            <FzIcon
              name="search"
              class="fz-select__search-icon"
              size="md"
              as="div"
            />
            <ComboboxInput
              ref="searchInputRef"
              v-model="searchTerm"
              class="fz-select__combobox-input"
              :placeholder="props.searchPlaceholder"
            />
          </div>

          <ComboboxEmpty
            v-if="!props.loading && !props.error"
            class="fz-select__no-results"
          >
            {{ props.noResultsText }}
          </ComboboxEmpty>

          <div
            v-if="props.error && !props.loading"
            class="fz-select__error-container"
          >
            <span class="fz-select__error-text">{{ props.errorText }}</span>
            <FzButton variant="ghost" size="small" @click="emit('retry')">
              Повторить
            </FzButton>
          </div>

          <div v-if="props.loading" class="fz-select__loader-container">
            <FzLoader size="small" />
          </div>

          <template
            v-if="
              !props.loading && !props.error && finalDisplayOptions.length > 0
            "
          >
            <FzCustomScrollbar ref="customScrollbarRef">
              <template v-if="props.virtualize && virtualizer">
                <div
                  :style="{
                    height: `${virtualizer.value?.getTotalSize()}px`,
                    width: '100%',
                    position: 'relative',
                  }"
                >
                  <div
                    v-for="virtualItem in virtualizer.value?.getVirtualItems()"
                    :key="virtualItem.index"
                    :style="{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: `${virtualItem.size}px`,
                      transform: `translateY(${virtualItem.start}px)`,
                    }"
                  >
                    <ComboboxItem
                      :value="
                        finalDisplayOptions[virtualItem.index].originalValue
                      "
                      :disabled="
                        finalDisplayOptions[virtualItem.index].disabled
                      "
                      :text-value="
                        getItemTextValue(finalDisplayOptions[virtualItem.index])
                      "
                      class="fz-select__item"
                      :class="{
                        'fz-select__tree-node': props.tree,
                        'fz-select__tree-node--selected': isOptionSelected(
                          finalDisplayOptions[virtualItem.index].originalValue,
                        ),
                        'fz-select__item--tree': props.tree,
                        'fz-select__item--multiple': props.multiple,
                      }"
                      :style="
                        props.tree
                          ? {
                            '--fz-tree-level': finalDisplayOptions[virtualItem.index].level,
                          }
                          : {}
                      "
                      @click="
                        props.tree && props.multiple
                          ? handleCheckboxSelect(
                            finalDisplayOptions[virtualItem.index]
                              .originalValue,
                          )
                          : undefined
                      "
                      @select="
                        props.tree && props.multiple
                          ? preventSelectIfMultipleTree
                          : undefined
                      "
                    >
                      <template v-if="props.multiple">
                        <FzCheckbox
                          :checked="
                            getCheckboxState(
                              finalDisplayOptions[virtualItem.index]
                                .originalValue,
                            ) === 'checked'
                          "
                          :indeterminate="
                            getCheckboxState(
                              finalDisplayOptions[virtualItem.index]
                                .originalValue,
                            ) === 'indeterminate'
                          "
                          :label="finalDisplayOptions[virtualItem.index].label"
                          :disabled="
                            finalDisplayOptions[virtualItem.index].disabled
                          "
                          class="fz-select__item-checkbox"
                          :class="{
                            'fz-select__tree-node-checkbox': props.tree,
                          }"
                          size="sm"
                          readonly
                        />
                      </template>
                      <template v-else>
                        <span class="fz-select__item-text">{{
                          finalDisplayOptions[virtualItem.index].label
                        }}</span>
                      </template>
                    </ComboboxItem>
                  </div>
                </div>
              </template>
              <template v-else>
                <template
                  v-for="optionItem in finalDisplayOptions"
                  :key="String(optionItem.actualValue)"
                >
                  <ComboboxItem
                    :value="optionItem.originalValue"
                    :disabled="optionItem.disabled"
                    :text-value="getItemTextValue(optionItem)"
                    class="fz-select__item"
                    :class="{
                      'fz-select__tree-node': props.tree,
                      'fz-select__tree-node--selected': isOptionSelected(
                        optionItem.originalValue,
                      ),
                      'fz-select__item--tree': props.tree,
                      'fz-select__item--multiple': props.multiple,
                    }"
                    :style="
                      props.tree
                        ? { '--fz-tree-level': optionItem.level }
                        : {}
                    "
                    @click="
                      props.tree && props.multiple
                        ? handleCheckboxSelect(optionItem.originalValue)
                        : undefined
                    "
                    @select="
                      props.tree && props.multiple
                        ? preventSelectIfMultipleTree
                        : undefined
                    "
                  >
                    <template v-if="props.multiple">
                      <FzCheckbox
                        :checked="
                          getCheckboxState(optionItem.originalValue)
                            === 'checked'
                        "
                        :indeterminate="
                          getCheckboxState(optionItem.originalValue)
                            === 'indeterminate'
                        "
                        :label="optionItem.label"
                        :disabled="optionItem.disabled"
                        class="fz-select__item-checkbox"
                        :class="{ 'fz-select__tree-node-checkbox': props.tree }"
                        size="sm"
                        readonly
                      />
                    </template>
                    <template v-else>
                      <span class="fz-select__item-text">{{
                        optionItem.label
                      }}</span>
                    </template>
                  </ComboboxItem>
                </template>
              </template>
            </FzCustomScrollbar>
          </template>
        </div>
        <div v-if="showResetButton" class="fz-select__footer">
          <FzButton
            variant="ghost"
            size="small"
            class="fz-select__reset-button"
            @click="(event: MouseEvent) => clearSelection(event)"
          >
            Сбросить все
          </FzButton>
        </div>
      </ComboboxContent>
    </ComboboxPortal>
  </ComboboxRoot>
</template>

<style>
/* Styles remain unchanged */
.fz-select {
  position: relative;
  width: 100%;
  font: var(--fz-font-control-text);
}

.fz-select__trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: var(--fz-spacing-24);
  padding: var(--fz-spacing-4) var(--fz-spacing-6) var(--fz-spacing-4)
    var(--fz-spacing-8);
  background-color: var(--fz-color-surface-light);
  border: 1px solid var(--fz-color-border-input);
  border-radius: var(--fz-radius-sm);
  color: var(--fz-color-text-primary);
  cursor: default;
  transition: border-color var(--fz-transition-duration),
    box-shadow var(--fz-transition-duration);
  text-align: left;
  gap: var(--fz-spacing-4);
  position: relative;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
}

.fz-select__trigger:focus {
  outline: none;
}

.fz-select__trigger--invalid {
  border-color: var(--fz-color-border-input-error);
}

.fz-select__trigger-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
  gap: var(--fz-spacing-4);
  min-width: 0;
  width: 100%;
}

.fz-select__trigger:hover:not(:disabled) {
  border-color: var(--fz-color-border-input-focus);
  cursor: pointer;
}

.fz-select--open .fz-select__trigger,
.fz-select__trigger[data-state="open"],
.fz-select__trigger:focus-visible:not([data-disabled="true"]) {
  outline: none;
  border-color: var(--fz-color-border-input-focus);
}

.fz-select--disabled .fz-select__trigger,
.fz-select__trigger[data-disabled="true"] {
  background-color: var(--fz-color-surface-disabled);
  border-color: var(--fz-color-border-input);
  color: var(--fz-color-text-secondary);
  cursor: not-allowed;
}

.fz-select--disabled .fz-select__arrow-icon {
  opacity: 0;
}

.fz-select__value-container {
  flex-grow: 1;
  display: flex;
  align-items: center;
  position: relative;
  min-width: 0;
}

.fz-select__value,
.fz-select__value--placeholder {
  font: var(--fz-font-control-text-big);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fz-select__value--placeholder {
  color: var(--fz-color-text-secondary);
}

.fz-select__combobox-input {
  flex-grow: 1;
  border: none;
  outline: none;
  background-color: transparent;
  font: var(--fz-font-control-text-medium);
  color: var(--fz-color-text-primary);
  padding: 0;
  height: var(--fz-spacing-15);
  width: 100%;
  border-radius: 0;
}

.fz-select__combobox-input::placeholder {
  color: var(--fz-color-text-secondary);
}
.fz-select__combobox-input[disabled] {
  cursor: not-allowed;
  background-color: transparent;
}

.fz-select__placeholder-text {
  color: var(--fz-color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fz-select__value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fz-select__tags-wrapper {
  position: relative;
  z-index: 1;
  pointer-events: all;
  cursor: default;
  user-select: none;
  display: flex;
  flex-grow: 1;
  max-width: calc(100% + var(--fz-spacing-4));
  margin-left: calc(var(--fz-spacing-4) * -1);
}

.fz-select__tag-list-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--fz-spacing-3);
}

.fz-select__tags-wrapper,
.fz-select__tag-list-container {
  min-width: 0;
}

.fz-select__icons {
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-3);
  color: var(--fz-color-border-icon-input);
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.fz-select__arrow-icon {
  transition: transform 0.2s;
  pointer-events: none;
}

.fz-select--open .fz-select__arrow-icon,
.fz-select__trigger[data-state="open"] .fz-select__arrow-icon {
  transform: rotate(180deg);
}

.fz-select__clear-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--fz-color-border-icon-critical);
}

.fz-select__clear-icon {
  padding: 2px;
  transition: opacity 0.3s;
}

.fz-select__clear-icon-wrapper:hover .fz-select__clear-icon {
  opacity: 0.8;
}

.fz-select__trigger--underline {
  min-height: var(--fz-spacing-15);
  padding-top: var(--fz-spacing);
  padding-bottom: var(--fz-spacing-4);
  padding-right: 0;
  padding-left: 0;
  border-left: none;
  border-right: none;
  border-top: none;
  border-radius: 0;
}

.fz-select__trigger--underline .fz-select__value,
.fz-select__trigger--underline .fz-select__value--placeholder {
  font: var(--fz-font-control-text-medium);
}

.fz-select__trigger--underline .fz-select__tags-wrapper {
  margin-left: 0;
  max-width: 100%;
}

.fz-select__trigger--underline:focus-visible:not(:disabled) {
  box-shadow: none;
}

.fz-select__trigger--underline:has(.fz-tag) {
  padding-bottom: var(--fz-spacing);
}

.fz-select__content {
  min-width: 100%;
  background-color: var(--fz-color-surface-light);
  border-radius: var(--fz-radius-sm);
  box-shadow: var(--fz-shadow-dropdown);
  border: 1px solid var(--fz-color-border-divider);
  z-index: 50;
  overflow: hidden;
  min-width: 240px;
  width: var(--reka-combobox-trigger-width);
  max-width: 100%;
  padding: var(--fz-spacing-8);
  position: relative;
}

.fz-select__content-inner {
  display: flex;
  flex-direction: column;
  gap: var(--fz-spacing-6);
  min-height: 0;
}

.fz-select__content[data-state="open"] {
  animation: fz-zoom-in-95 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.fz-select__content[data-state="closed"] {
  animation: fz-zoom-out-95 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.fz-select__content:has(.simplebar-scrollable-y) .fz-custom-scrollbar {
  width: calc(100% + 14px);
}

.fz-select__content .simplebar-track {
  opacity: 0;
}
.fz-select__content:has(.simplebar-scrollable-y) .simplebar-track {
  opacity: 1;
}

.fz-select__viewport {
  /* Styles for native scrolling when virtualized */
  overflow-y: auto;
  /* max-height is set via inline style from props.dropdownMaxHeight */
  /* padding: var(--fz-spacing-3); /* Consider if padding is needed here or on items */
  /* Explicitly allow scrollbar display for FzCustomScrollbar compatibility */
  scrollbar-width: auto !important; /* Override reka-ui's none if present */
}

.fz-select__viewport::-webkit-scrollbar {
  display: block !important; /* Override reka-ui's none if present for WebKit */
}

.fz-select__item {
  display: flex;
  align-items: center;
  padding: var(--fz-spacing-4) var(--fz-spacing-3);
  border-radius: var(--fz-radius-xs);
  cursor: pointer;
  color: var(--fz-color-text-primary);
  font: var(--fz-font-control-text-medium);
  outline: none;
  user-select: none;
  transition: background-color var(--fz-transition-duration)
    var(--fz-transition-timing-function);
  gap: var(--fz-spacing-3);
  width: 100%;
  box-sizing: border-box;
}

.fz-select__item[data-disabled] {
  color: var(--fz-color-text-tertiary);
  cursor: not-allowed;
  opacity: 0.7;
}

.fz-select__item[data-highlighted] {
  background-color: var(--fz-color-surface-ghost-hover);
}

.fz-select__content--multiple .fz-select__item {
  padding-right: 0;
}

.fz-select__content--multiple .fz-select__item[data-highlighted] {
  background-color: transparent;
}

.fz-select__item .fz-checkbox {
  min-width: 0;
  pointer-events: none;
}

.fz-select__item .fz-checkbox__label {
  min-width: 0;
  overflow: hidden;
  display: block;
  overflow-wrap: break-word;
}

.fz-select__item[data-highlighted]:not([data-disabled]) .fz-checkbox__input {
  border-color: var(--fz-color-border-input-focus);
}

.fz-select__item:focus-visible {
  outline: none;
}

.fz-select__item:not([data-disabled])[data-state="checked"]
  .fz-select__item-text {
  color: var(--fz-color-text-secondary);
}

.fz-select__item--custom {
  padding: 0;
}

.fz-select__item-text {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fz-select__item-checkbox,
.fz-select__item-radio {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

/* Hover state for items matching FzCheckbox */
.fz-select__item:hover:not([data-disabled]) {
  border-color: var(--fz-color-border-input-focus);
}

.fz-select__no-results {
  color: var(--fz-color-text-secondary);
  font: var(--fz-font-control-text-medium);
  padding-top: var(--fz-spacing-4);
  padding-bottom: var(--fz-spacing-3);
}

.fz-select__tree-node-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: transform 0.2s;
  flex-shrink: 0;
}

.fz-select__tree-node-toggle[data-state="open"] {
  transform: rotate(90deg);
}

.fz-select__tree-node-toggle-placeholder {
  width: 20px;
  flex-shrink: 0;
}

.fz-select__content--loading {
  position: relative;
}

.fz-select__loader-container {
  flex-shrink: 0;
  flex-grow: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 28px;
  z-index: 10;
  top: 0;
  left: 0;
  flex-grow: 1;
  padding-top: var(--fz-spacing);
}

.fz-select__item .fz-checkbox,
.fz-select__tree-node .fz-checkbox {
  pointer-events: none;
  cursor: inherit;
}

.fz-select__item .fz-checkbox__input,
.fz-select__tree-node .fz-checkbox__input {
  cursor: inherit;
  pointer-events: none;
}

.fz-select__search-container {
  display: flex;
  align-items: center;
  position: relative;
}

.fz-select__search-container:focus-within {
  border-color: var(--fz-color-border-input-focus);
}

.fz-select__combobox-input {
  flex-grow: 1;
  border: none;
  outline: none;
  background-color: transparent;
  font: var(--fz-font-control-text-medium);
  color: var(--fz-color-text-primary);
  padding: var(--fz-spacing-2) 0 var(--fz-spacing-2) var(--fz-spacing-12);
  border-bottom: 1px solid var(--fz-color-border-input);
  height: 30px;
  width: 100%;
  min-width: 50px;
  box-sizing: border-box;
  transition: border-color var(--fz-transition-duration);
}

.fz-select__combobox-input:focus {
  border-color: var(--fz-color-border-input-focus);
}

.fz-select__combobox-input::placeholder {
  color: var(--fz-color-text-secondary);
  font-weight: var(--fz-font-weight-regular);
}

.fz-select__combobox-input[disabled] {
  cursor: not-allowed;
  background-color: var(--fz-color-surface-disabled);
}

.fz-select__search-icon {
  position: absolute;
  left: 0;
  color: var(--fz-color-border-icon-input) !important;
  pointer-events: none;
  display: flex;
}

.fz-select__search-container--underline {
  height: 30px;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid var(--fz-color-text-secondary);
}

.fz-select__search-container--underline:focus-within {
  border-color: var(--fz-color-border-input-focus);
}

.fz-select__combobox-input--underline {
  font-size: var(--fz-font-size-xs);
  padding: 7.5px 0 7.5px 0;
}

.fz-select__error-container {
  display: flex;
  flex-direction: column;
  gap: var(--fz-spacing-2);
  padding-top: var(--fz-spacing-4);
  text-align: center;
}

.fz-select__error-text {
  color: var(--fz-color-text-secondary);
  font: var(--fz-font-control-text-medium);
}

.fz-select__error-container .fz-button {
  align-self: flex-start;
  margin-top: var(--fz-spacing-2);
  width: 100%;
}

.fz-select__reset-button {
  width: 100%;
}

/* Tag path display for tree select multiple */
.fz-select__tag-tree-content {
  min-width: 0;
}

.fz-select__tag-parent {
  color: var(--fz-color-text-secondary);
}

.fz-select__tag-separator {
  color: var(--fz-color-text-secondary);
}

.fz-select__tag-child {
  color: var(--fz-color-text-primary);
}

.fz-select__tag-parent--has-children,
.fz-select__tag-child--has-children {
  color: var(--fz-color-text-secondary);
}

/* Tree indentation variables and styles */
.fz-select__item--tree {
  --fz-tree-indent-step: 24px;
  padding-left: calc(var(--fz-tree-level, 0) * var(--fz-tree-indent-step));
}

/* First level padding for single tree mode */
.fz-select__item--tree:not(.fz-select__item--multiple)[style*="--fz-tree-level:0"],
.fz-select__item--tree:not(.fz-select__item--multiple)[style*="--fz-tree-level: 0"] {
  padding-left: var(--fz-spacing-6);
}
</style>
