<script
  setup
  lang="ts"
  generic="OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType"
>
import type {
  FzSelectDefaultOptionType,
  FzSelectOption,
  FzSelectOptionBase,
  FzSelectTriggerDisplayValue,
  FzSelectTriggerDisplayValueTreeItem,
} from '../types'

import { ComboboxAnchor, ComboboxTrigger } from 'reka-ui'
import { computed } from 'vue'
import { FzIcon } from '../../FzIcon'
import FzTag from '../../FzTag'
import { FzTooltip } from '../../FzTooltip'

export interface FzSelectTriggerProps<T extends FzSelectOptionBase<FzSelectOption>> {
  selectId: string
  disabled?: boolean
  multiple?: boolean
  tree?: boolean
  placeholder?: string
  triggerDisplayValue: FzSelectTriggerDisplayValue
  defaultTags: Array<{
    value: any
    label: string
    originalLabel?: string
  }>
  showClearButton: boolean
  modelValue: T | T[] | null | undefined
  comboboxTriggerClass: any
  getOptionFullPath: (option: T) => string
  getActualOptionValue: (option: T) => any
}

interface FzSelectTriggerEmits {
  clearSelection: [event?: MouseEvent]
  tagDelete: [tagValue: any]
  triggerClick: [event: MouseEvent]
}

type Props = FzSelectTriggerProps<OptionType>

const props = defineProps<Props>()
const emit = defineEmits<FzSelectTriggerEmits>()

// Вспомогательные функции для рендеринга тултипов
function shouldShowTooltip(tag: FzSelectTriggerDisplayValueTreeItem) {
  return Array.isArray(tag.pathSegments) && tag.pathSegments.some(segment => !!segment.truncatedValue)
}

function getTagDisplayLabel(tag: FzSelectTriggerDisplayValueTreeItem) {
  if ('pathSegments' in tag) {
    // Для сегментов пути показываем полный путь или только последний сегмент
    const leafSegment = tag.pathSegments.find(segment => !segment.isParent)
    return leafSegment?.truncatedValue ?? leafSegment?.originalValue ?? ''
  }
  return ''
}

function handleTagDelete(tagValue: any) {
  emit('tagDelete', tagValue)
}

// Локальный обработчик события клика по триггеру
function onTriggerClick(event: MouseEvent) {
  emit('triggerClick', event)
}

// Проверка, является ли значение плейсхолдером
const isPlaceholder = computed(() => {
  return !props.triggerDisplayValue
    || (Array.isArray(props.triggerDisplayValue) && props.triggerDisplayValue.length === 0)
})

function renderSingleTriggerDisplayValue() {
  if (Array.isArray(props.triggerDisplayValue)) {
    return (props.triggerDisplayValue as Array<OptionType>).map(tag => tag.label).join(', ')
  }
  return ''
}
</script>

<template>
  <ComboboxAnchor :id="selectId" :disabled="disabled" as-child>
    <ComboboxTrigger
      tabindex="0"
      :class="comboboxTriggerClass"
      @click="onTriggerClick"
    >
      <div class="fz-select__trigger-inner">
        <div class="fz-select__value-container">
          <template
            v-if="
              tree && triggerDisplayValue && triggerDisplayValue.length > 0
            "
          >
            <div class="fz-select__tags-wrapper">
              <div class="fz-select__tag-list-container">
                <FzTooltip
                  v-for="tag in triggerDisplayValue as Array<FzSelectTriggerDisplayValueTreeItem>"
                  :key="String(tag.value)"
                  position="top"
                  :disabled="!shouldShowTooltip(tag)"
                >
                  <template #content>
                    <span class="fz-select__tag-tree-content">
                      <span
                        v-for="(segment, index) in tag.pathSegments" :key="index"
                        :class="{
                          'fz-select__tag-parent': segment.isParent,
                          'fz-select__tag-child': !segment.isParent,
                          'fz-select__tag-parent--has-children': segment.isParent,
                        }"
                      >
                        {{ segment.originalValue }}
                        <span v-if="index !== tag.pathSegments.length - 1" class="fz-select__tag-separator">
                          >
                        </span>
                      </span>
                    </span>
                  </template>
                  <FzTag
                    :label="getTagDisplayLabel(tag)"
                    :value="tag.value"
                    :disabled="disabled"
                    @delete="handleTagDelete"
                  >
                    <div class="fz-select__tag-tree-content">
                      <template v-for="(segment, index) in tag.pathSegments" :key="index">
                        <span
                          v-if="index > 0"
                          class="fz-select__tag-separator"
                        >
                          >
                        </span>
                        <span
                          :class="{
                            'fz-select__tag-parent': segment.isParent,
                            'fz-select__tag-child': !segment.isParent,
                            'fz-select__tag-parent--has-children': segment.isParent,
                          }"
                        >
                          {{ segment.truncatedValue ?? segment.originalValue }}
                        </span>
                      </template>
                    </div>
                  </FzTag>
                </FzTooltip>
              </div>
            </div>
          </template>
          <template v-else-if="!tree && !multiple && Array.isArray(triggerDisplayValue)">
            <span class="fz-select__value">
              {{ renderSingleTriggerDisplayValue() }}
            </span>
          </template>
          <template v-else-if="!tree && multiple && triggerDisplayValue && triggerDisplayValue.length > 0">
            <div class="fz-select__tags-wrapper">
              <div class="fz-select__tag-list-container">
                <FzTag
                  v-for="tag in triggerDisplayValue as Array<OptionType>"
                  :key="String(tag.value)"
                  :label="tag.label"
                  :value="tag.value"
                  :disabled="disabled"
                  @delete="handleTagDelete"
                />
              </div>
            </div>
          </template>
          <template v-else>
            <span
              class="fz-select__value"
              :class="{
                'fz-select__value--placeholder': isPlaceholder,
              }"
            >
              {{ placeholder }}
            </span>
          </template>
        </div>

        <div class="fz-select__icons">
          <div
            v-if="showClearButton"
            class="fz-select__clear-icon-wrapper"
            role="button"
            aria-label="Clear selection"
            @pointerdown.stop.prevent="emit('clearSelection')"
          >
            <FzIcon
              name="x-small"
              class="fz-select__clear-icon"
              size="md"
              as="div"
            />
          </div>
          <FzIcon
            name="arrowdown"
            class="fz-select__arrow-icon"
            size="md"
            as="div"
          />
        </div>
      </div>
    </ComboboxTrigger>
  </ComboboxAnchor>
</template>

<style scoped>
/* Tooltip-specific styles for tag tree content */
.fz-tooltip .fz-select__tag-tree-content {
  display: inline;
  font: var(--fz-font-hint);
}

.fz-tooltip .fz-select__tag-parent {
  color: var(--fz-color-text-secondary);
}

.fz-tooltip .fz-select__tag-separator {
  color: var(--fz-color-text-secondary);
}

.fz-tooltip .fz-select__tag-child {
  color: var(--fz-color-text-primary);
}
</style>
