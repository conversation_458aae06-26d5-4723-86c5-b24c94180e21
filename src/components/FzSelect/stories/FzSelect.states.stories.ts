import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import FzSelect from '../FzSelect.vue'

const meta = {
  title: 'Components/FzSelect/States',
  component: FzSelect as any,
  tags: ['autodocs'],
} satisfies Meta<typeof FzSelect>

export default meta
type Story = StoryObj<typeof meta>

const simpleOptions = [
  { label: 'Vue', value: 'vue' },
  { label: 'React', value: 'react' },
  { label: 'Angular', value: 'angular' },
]

export const Loading: Story = {
  args: {
    options: [],
    loading: true,
    placeholder: 'Loading options...',
    modelValue: null,
    searchable: true,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(null)
      const currentOptions = ref(args.options)
      const isLoading = ref(args.loading)

      const handleOpenChange = (isOpen: boolean) => {
        if (isOpen && currentOptions.value.length === 0) {
          isLoading.value = true
          setTimeout(() => {
            currentOptions.value = simpleOptions
            isLoading.value = false
          }, 1000)
        }
        args.onOpenChange?.(isOpen)
      }

      return { args, selected, currentOptions, isLoading, handleOpenChange }
    },
    template: `
      <div style="width: 240px">
        <FzSelect
          v-bind="args"
          v-model="selected"
          :options="currentOptions"
          :loading="isLoading"
          @openChange="handleOpenChange"
        />
        <p class="fz:mt-4">Selected: {{ selected }}</p>
        <p class="fz:mt-2">Loading: {{ isLoading }}</p>
      </div>
    `,
  }),
}

export const Error: Story = {
  args: {
    options: [],
    placeholder: 'Ошибка при загрузке',
    modelValue: null,
    loading: false,
    error: true,
    errorText: 'Ошибка при загрузке',
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(null)
      return { args, selected }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" />
        <p class="fz:mt-4">Selected: {{ selected }}</p>
      </div>
    `,
  }),
}
