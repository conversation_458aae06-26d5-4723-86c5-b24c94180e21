import type { Meta, StoryObj } from '@storybook/vue3'
import type { FzSelectTreeOption } from '../types'
import { ref } from 'vue'
import { FzButton } from '../../FzButton'
import FzSelect from '../FzSelect.vue'

const meta = {
  title: 'Components/FzSelect/Tree',
  component: FzSelect as any,
  tags: ['autodocs'],
} satisfies Meta<typeof FzSelect>

export default meta
type Story = StoryObj<typeof meta>

const nestedOptions = [
  {
    label: 'Frontend Frameworks',
    value: 'frontend',
    children: [
      { label: 'Vue.js', value: 'vue' },
      { label: 'React', value: 'react' },
      {
        label: 'Angular',
        value: 'angular',
        children: [
          { label: 'Angular CLI', value: 'angular-cli' },
          { label: 'NgRx', value: 'ngrx' },
        ],
      },
    ],
  },
  {
    label: 'Backend Frameworks',
    value: 'backend',
    children: [
      { label: 'Express.js', value: 'express' },
      { label: 'NestJS', value: 'nestjs', disabled: true },
      { label: 'FastAPI', value: 'fastapi' },
    ],
  },
  { label: 'Vanilla JavaScript', value: 'vanilla_js' },
]

const deepNestedOptions = [
  {
    label: 'Technology Stack',
    value: 'tech',
    children: [
      {
        label: 'Programming Languages',
        value: 'languages',
        children: [
          {
            label: 'Compiled Languages',
            value: 'compiled',
            children: [
              {
                label: 'System Programming',
                value: 'system',
                children: [
                  {
                    label: 'Это очень длинный текст опции, который демонстрирует, как компонент обрабатывает переполнение текста в глубоко вложенных структурах',
                    value: 'long-text',
                  },
                  { label: 'C++', value: 'cpp' },
                  { label: 'Rust', value: 'rust' },
                ],
              },
              { label: 'Java', value: 'java' },
            ],
          },
          {
            label: 'Interpreted Languages',
            value: 'interpreted',
            children: [
              { label: 'Python', value: 'python' },
              { label: 'Ruby', value: 'ruby' },
            ],
          },
        ],
      },
      {
        label: 'Databases',
        value: 'db',
        children: [
          { label: 'SQL', value: 'sql' },
          { label: 'NoSQL', value: 'nosql' },
        ],
      },
    ],
  },
]

export const Single: Story = {
  args: {
    options: nestedOptions,
    tree: true,
    placeholder: 'Select from tree',
    modelValue: null,
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref(null)
      const allOptions = ref([...nestedOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, {
          label: 'New Framework Category',
          value: 'new-category',
          children: [
            { label: 'New Framework 1', value: 'new-1' },
            { label: 'New Framework 2', value: 'new-2' },
          ],
        }]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

export const Multiple: Story = {
  args: {
    options: [...nestedOptions, ...deepNestedOptions],
    tree: true,
    multiple: true,
    placeholder: 'Select multiple from tree',
    modelValue: [],
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref([])
      const allOptions = ref([...nestedOptions, ...deepNestedOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, {
          label: 'New Framework Category',
          value: 'new-category',
          children: [
            { label: 'New Framework 1', value: 'new-1' },
            { label: 'New Framework 2', value: 'new-2' },
          ],
        }]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" propagate-select />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

const lotsOfTreeItemsOptions = Array.from({ length: 4 }, (_, i) => ({
  label: `Category ${i + 1}`,
  value: `category-${i + 1}`,
  children: Array.from({ length: 10 }, (_, j) => ({
    label: `Subcategory ${i + 1}.${j + 1}`,
    value: `subcategory-${i + 1}-${j + 1}`,
    children: Array.from({ length: 20 }, (_, k) => ({
      label: `Item ${i + 1}.${j + 1}.${k + 1}`,
      value: `item-${i + 1}-${j + 1}-${k + 1}`,
    })),
  })),
}))

export const LotsOfItemsTree: Story = {
  args: {
    options: lotsOfTreeItemsOptions,
    tree: true,
    multiple: true,
    itemType: 'checkbox',
    placeholder: 'Tree with checkboxes',
    propagateSelect: true,

    // Предварительно выбираем Vue.js
    modelValue: [nestedOptions[0]?.children?.[0] as unknown as FzSelectTreeOption],

    // Включаем кнопку сброса для древовидного представления с чекбоксами
    showResetButton: true,

    virtualize: true,
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref([])
      return { args, selected, lotsOfTreeItemsOptions }
    },
    template: `
      <div style="width: 300px">
        <FzSelect v-bind="args" v-model="selected" :options="lotsOfTreeItemsOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}
