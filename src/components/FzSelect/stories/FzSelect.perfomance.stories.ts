import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import FzSelect from '../FzSelect.vue'

const meta = {
  title: 'Components/FzSelect/Performance',
  component: FzSelect as any,
  tags: ['autodocs'],
} satisfies Meta<typeof FzSelect>

export default meta
type Story = StoryObj<typeof meta>

// Генерация большого набора данных дерева для тестирования производительности
function generateLargeTreeData(categoriesCount: number = 20, subcategoriesCount: number = 15, itemsCount: number = 30) {
  const startTime = performance.now()

  const data = Array.from({ length: categoriesCount }, (_, i) => ({
    label: `Category ${i + 1}`,
    value: `category-${i + 1}`,
    children: Array.from({ length: subcategoriesCount }, (_, j) => ({
      label: `Subcategory ${i + 1}.${j + 1}`,
      value: `subcategory-${i + 1}-${j + 1}`,
      children: Array.from({ length: itemsCount }, (_, k) => ({
        label: `Item ${i + 1}.${j + 1}.${k + 1}`,
        value: `item-${i + 1}-${j + 1}-${k + 1}`,
        disabled: Math.random() < 0.1, // 10% вероятность быть отключенным
      })),
    })),
  }))

  const endTime = performance.now()
  console.warn(`Сгенерировано ${categoriesCount * subcategoriesCount * itemsCount} элементов за ${endTime - startTime}мс`)

  return data
}

// Малый набор данных для сравнения
const smallTreeData = generateLargeTreeData(3, 5, 10)

// Средний набор данных
const mediumTreeData = generateLargeTreeData(10, 8, 20)

// Большой набор данных
const largeTreeData = generateLargeTreeData(20, 15, 30)

// Очень большой набор данных
const extraLargeTreeData = generateLargeTreeData(30, 20, 50)

export const SmallDataset: Story = {
  args: {
    options: smallTreeData,
    tree: true,
    multiple: true,
    placeholder: 'Малый набор данных (150 элементов)',
    modelValue: [],
    searchable: true,
    showResetButton: true,
    virtualize: true,
    virtualizeEstimateSize: 36,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref([])
      const renderTime = ref(0)

      const onOpenChange = (isOpen: boolean) => {
        if (isOpen) {
          const start = performance.now()
          setTimeout(() => {
            renderTime.value = performance.now() - start
          }, 0)
        }
      }

      return { args, selected, smallTreeData, renderTime, onOpenChange }
    },
    template: `
      <div style="width: 300px">
        <FzSelect
          v-bind="args"
          v-model="selected"
          :options="smallTreeData"
          @openChange="onOpenChange"
        />
        <p class="fz:mt-4">Выбрано: {{ selected.length }} элементов</p>
        <p class="fz:mt-2">Время рендеринга: {{ renderTime.toFixed(2) }}мс</p>
        <p class="fz:mt-2">Всего элементов: {{ smallTreeData.length * 5 * 10 }}</p>
      </div>
    `,
  }),
}

export const MediumDataset: Story = {
  args: {
    options: mediumTreeData,
    tree: true,
    multiple: true,
    placeholder: 'Средний набор данных (1,600 элементов)',
    modelValue: [],
    searchable: true,
    showResetButton: true,
    virtualize: true,
    virtualizeEstimateSize: 36,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref([])
      const renderTime = ref(0)

      const onOpenChange = (isOpen: boolean) => {
        if (isOpen) {
          const start = performance.now()
          setTimeout(() => {
            renderTime.value = performance.now() - start
          }, 0)
        }
      }

      return { args, selected, mediumTreeData, renderTime, onOpenChange }
    },
    template: `
      <div style="width: 300px">
        <FzSelect
          v-bind="args"
          v-model="selected"
          :options="mediumTreeData"
          @openChange="onOpenChange"
        />
        <p class="fz:mt-4">Выбрано: {{ selected.length }} элементов</p>
        <p class="fz:mt-2">Время рендеринга: {{ renderTime.toFixed(2) }}мс</p>
        <p class="fz:mt-2">Всего элементов: {{ mediumTreeData.length * 8 * 20 }}</p>
      </div>
    `,
  }),
}

export const LargeDataset: Story = {
  args: {
    options: largeTreeData,
    tree: true,
    multiple: true,
    placeholder: 'Большой набор данных (9,000 элементов)',
    modelValue: [],
    searchable: true,
    showResetButton: true,
    virtualize: true,
    virtualizeEstimateSize: 36,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref([])
      const renderTime = ref(0)

      const onOpenChange = (isOpen: boolean) => {
        if (isOpen) {
          const start = performance.now()
          setTimeout(() => {
            renderTime.value = performance.now() - start
          }, 0)
        }
      }

      return { args, selected, largeTreeData, renderTime, onOpenChange }
    },
    template: `
      <div style="width: 300px">
        <FzSelect
          v-bind="args"
          v-model="selected"
          :options="largeTreeData"
          @openChange="onOpenChange"
        />
        <p class="fz:mt-4">Выбрано: {{ selected.length }} элементов</p>
        <p class="fz:mt-2">Время рендеринга: {{ renderTime.toFixed(2) }}мс</p>
        <p class="fz:mt-2">Всего элементов: {{ largeTreeData.length * 15 * 30 }}</p>
      </div>
    `,
  }),
}

export const LargeDatasetWithVirtualization: Story = {
  args: {
    options: largeTreeData,
    tree: true,
    multiple: true,
    placeholder: 'Большой набор данных с виртуализацией (9,000 элементов)',
    modelValue: [],
    searchable: true,
    showResetButton: true,
    virtualize: true,
    virtualizeEstimateSize: 36,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref([])
      const renderTime = ref(0)

      const onOpenChange = (isOpen: boolean) => {
        if (isOpen) {
          const start = performance.now()
          setTimeout(() => {
            renderTime.value = performance.now() - start
          }, 0)
        }
      }

      return { args, selected, largeTreeData, renderTime, onOpenChange }
    },
    template: `
      <div style="width: 300px">
        <FzSelect
          v-bind="args"
          v-model="selected"
          :options="largeTreeData"
          @openChange="onOpenChange"
        />
        <p class="fz:mt-4">Выбрано: {{ selected.length }} элементов</p>
        <p class="fz:mt-2">Время рендеринга: {{ renderTime.toFixed(2) }}мс</p>
        <p class="fz:mt-2">Всего элементов: {{ largeTreeData.length * 15 * 30 }}</p>
        <p class="fz:mt-2 fz:text-green-600">✓ Виртуализация включена</p>
      </div>
    `,
  }),
}

export const ExtraLargeDataset: Story = {
  args: {
    options: extraLargeTreeData,
    tree: true,
    multiple: true,
    placeholder: 'Очень большой набор данных (30,000 элементов)',
    modelValue: [],
    searchable: true,
    showResetButton: true,
    virtualize: true,
    virtualizeEstimateSize: 36,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref([])
      const renderTime = ref(0)

      const onOpenChange = (isOpen: boolean) => {
        if (isOpen) {
          const start = performance.now()
          setTimeout(() => {
            renderTime.value = performance.now() - start
          }, 0)
        }
      }

      return { args, selected, extraLargeTreeData, renderTime, onOpenChange }
    },
    template: `
      <div style="width: 300px">
        <FzSelect
          v-bind="args"
          v-model="selected"
          :options="extraLargeTreeData"
          @openChange="onOpenChange"
        />
        <p class="fz:mt-4">Выбрано: {{ selected.length }} элементов</p>
        <p class="fz:mt-2">Время рендеринга: {{ renderTime.toFixed(2) }}мс</p>
        <p class="fz:mt-2">Всего элементов: {{ extraLargeTreeData.length * 20 * 50 }}</p>
        <p class="fz:mt-2 fz:text-green-600">✓ Виртуализация включена</p>
        <p class="fz:mt-2 fz:text-orange-600">⚠️ Используйте с осторожностью - очень большой набор данных</p>
      </div>
    `,
  }),
}

export const SingleSelectionPerformance: Story = {
  args: {
    options: largeTreeData,
    tree: true,
    multiple: false,
    placeholder: 'Одиночное выбор (9,000 элементов)',
    modelValue: null,
    searchable: true,
    clearable: true,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(null)
      const renderTime = ref(0)

      const onOpenChange = (isOpen: boolean) => {
        if (isOpen) {
          const start = performance.now()
          setTimeout(() => {
            renderTime.value = performance.now() - start
          }, 0)
        }
      }

      return { args, selected, largeTreeData, renderTime, onOpenChange }
    },
    template: `
      <div style="width: 300px">
        <FzSelect
          v-bind="args"
          v-model="selected"
          :options="largeTreeData"
          @openChange="onOpenChange"
        />
        <p class="fz:mt-4">Выбрано: {{ selected ? 'Да' : 'Нет' }}</p>
        <p class="fz:mt-2">Время рендеринга: {{ renderTime.toFixed(2) }}мс</p>
        <p class="fz:mt-2">Всего элементов: {{ largeTreeData.length * 15 * 30 }}</p>
      </div>
    `,
  }),
}
