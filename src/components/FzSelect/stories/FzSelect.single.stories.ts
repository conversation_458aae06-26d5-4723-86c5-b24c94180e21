import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import { FzButton } from '../../FzButton'
import FzSelect from '../FzSelect.vue'

const meta = {
  title: 'Components/FzSelect/Single',
  component: FzSelect as any,
  tags: ['autodocs'],
} satisfies Meta<typeof FzSelect>

export default meta
type Story = StoryObj<typeof meta>

const simpleOptions = [
  { label: 'Vue', value: 'vue' },
  { label: 'React', value: 'react', disabled: true },
  { label: 'Angular', value: 'angular' },
  { label: 'Svelte', value: 'svelte' },
  { label: 'SolidJS', value: 'solid' },
]

export const Default: Story = {
  args: {
    options: simpleOptions,
    placeholder: 'Select a framework',
    modelValue: null,
    searchable: true,
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref(null)
      const allOptions = ref([...simpleOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, { label: 'New Option', value: 'new-option' }, { label: 'New Option 2', value: 'new-option-2' }, { label: 'New Option 3', value: 'new-option-3' },
        ]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

export const Underline: Story = {
  args: {
    options: simpleOptions,
    variant: 'underline',
    placeholder: 'Select a framework',
    modelValue: null,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(null)
      return { args, selected }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}

export const PreSelected: Story = {
  args: {
    options: simpleOptions,
    modelValue: simpleOptions[2], // Angular
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(args.modelValue)
      return { args, selected }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}

export const Clearable: Story = {
  args: {
    options: simpleOptions,
    clearable: true,
    placeholder: 'Select or clear',
    modelValue: simpleOptions[0],
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(args.modelValue)
      return { args, selected }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}

export const Searchable: Story = {
  args: {
    options: simpleOptions,
    searchable: true,
    placeholder: 'Search and select',
    modelValue: null,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(null)
      return { args, selected }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}

export const Disabled: Story = {
  args: {
    options: simpleOptions,
    disabled: true,
    modelValue: simpleOptions[0],
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(args.modelValue)
      return { args, selected }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}

export const FullWidth: Story = {
  args: {
    options: simpleOptions,
    placeholder: 'Full width select',
    modelValue: null,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(null)
      return { args, selected }
    },
    template: `
      <div style="width: 100%">
        <FzSelect v-bind="args" v-model="selected" style="width: 100%" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}

const lotsOfItemsOptions = Array.from({ length: 200 }, (_, i) => ({
  label: `Item ${i + 1}`,
  value: `item-${i + 1}`,
}))

export const LotsOfItems: Story = {
  args: {
    options: lotsOfItemsOptions,
    placeholder: 'Select from many items',
    modelValue: null,
    searchable: true,
    virtualize: true,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref(null)
      return { args, selected, lotsOfItemsOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="lotsOfItemsOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}
