import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import { FzButton } from '../../FzButton'
import FzSelect from '../FzSelect.vue'

const meta = {
  title: 'Components/FzSelect/Multiple',
  component: FzSelect as any,
  tags: ['autodocs'],
} satisfies Meta<typeof FzSelect>

export default meta
type Story = StoryObj<typeof meta>

const simpleOptions = [
  { label: 'Vue', value: 'vue' },
  { label: 'React', value: 'react', disabled: true },
  { label: 'Angular', value: 'angular' },
  { label: 'Svelte', value: 'svelte' },
  { label: 'SolidJS', value: 'solid' },
]

export const Default: Story = {
  args: {
    options: simpleOptions,
    multiple: true,
    placeholder: 'Select frameworks',
    modelValue: [],
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref([])
      const allOptions = ref([...simpleOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, { label: 'New Option', value: 'new-option' }, { label: 'New Option 2', value: 'new-option-2' }, { label: 'New Option 3', value: 'new-option-3' },
        ]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

export const Underline: Story = {
  args: {
    options: simpleOptions,
    multiple: true,
    variant: 'underline',
    placeholder: 'Select frameworks',
    modelValue: [],
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref([])
      const allOptions = ref([...simpleOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, { label: 'New Option', value: 'new-option' }, { label: 'New Option 2', value: 'new-option-2' }, { label: 'New Option 3', value: 'new-option-3' },
        ]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

export const PreSelected: Story = {
  args: {
    options: simpleOptions,
    multiple: true,
    modelValue: [simpleOptions[0], simpleOptions[3]], // Vue, Svelte
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref(args.modelValue)
      const allOptions = ref([...simpleOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, { label: 'New Option', value: 'new-option' }, { label: 'New Option 2', value: 'new-option-2' }, { label: 'New Option 3', value: 'new-option-3' },
        ]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

export const Searchable: Story = {
  args: {
    options: simpleOptions,
    multiple: true,
    searchable: true,
    placeholder: 'Search and select multiple',
    modelValue: [],
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref([])
      const allOptions = ref([...simpleOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, { label: 'New Option', value: 'new-option' }, { label: 'New Option 2', value: 'new-option-2' }, { label: 'New Option 3', value: 'new-option-3' },
        ]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

export const WithResetButton: Story = {
  args: {
    options: simpleOptions,
    multiple: true,
    placeholder: 'Select frameworks (with reset)',
    modelValue: [simpleOptions[0]], // Предварительно выбираем один элемент для показа кнопки
    clearable: true,
    showResetButton: true, // Явно включаем кнопку сброса
  },
  render: (args: any) => ({
    components: { FzSelect, FzButton },
    setup() {
      const selected = ref(args.modelValue)
      const allOptions = ref([...simpleOptions])

      const addMoreOptions = () => {
        allOptions.value = [...allOptions.value, { label: 'New Option', value: 'new-option' }, { label: 'New Option 2', value: 'new-option-2' }, { label: 'New Option 3', value: 'new-option-3' },
        ]
      }

      return { args, selected, allOptions, addMoreOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="allOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
        <FzButton class="fz:mt-4" @click="addMoreOptions">Добавить больше опций</FzButton>
      </div>
    `,
  }),
}

const lotsOfItemsOptions = Array.from({ length: 200 }, (_, i) => ({
  label: `Item ${i + 1}`,
  value: `item-${i + 1}`,
}))

export const LotsOfItems: Story = {
  args: {
    options: lotsOfItemsOptions,
    multiple: true,
    placeholder: 'Select multiple from many items',
    modelValue: [],
    searchable: true,
    virtualize: true,
  },
  render: (args: any) => ({
    components: { FzSelect },
    setup() {
      const selected = ref([])
      return { args, selected, lotsOfItemsOptions }
    },
    template: `
      <div style="width: 240px">
        <FzSelect v-bind="args" v-model="selected" :options="lotsOfItemsOptions" @update:modelValue="args['onUpdate:modelValue']" />
        <p class="fz:mt-4">Выбрано: {{ selected }}</p>
      </div>
    `,
  }),
}
