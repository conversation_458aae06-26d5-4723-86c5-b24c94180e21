import type {
  FzSelectDefaultOptionType,
  FzSelectOption,
  FzSelectOptionBase,
  FzSelectProps,
  FzSelectValue,
} from '../types'
import type { useFzSelectPropsParser } from './useFzSelectPropsParser'
import type { useFzSelectTreeLogic } from './useFzSelectTreeLogic'
import { computed, ref, toValue } from 'vue'

/**
 * @description Тип для эмиттера событий FzSelect.
 */
export interface FzSelectEmit {
  <OptionType extends FzSelectOptionBase<FzSelectOption>>(event: 'update:modelValue', value: OptionType | OptionType[] | null): void
  (event: 'openChange', open: boolean): void
  (event: 'retry'): void
}

/**
 * @description Композабл для управления логикой выбора элементов в FzSelect.
 * @template OptionType Тип объекта опции, расширяющий FzSelectOptionBase.
 * @param {Readonly<FzSelectProps<OptionType>>} props - Реактивные свойства компонента FzSelect.
 * @param {OptionType | OptionType[] | null} modelValue - Реактивная ссылка на `modelValue`.
 * @param {FzSelectEmit} emit - Функция для генерации событий.
 * @param {ReturnType<typeof useFzSelectPropsParser<OptionType>>} parsedPropsAccessors - Аксессоры для свойств опций.
 * @param {ReturnType<typeof useFzSelectTreeLogic<OptionType>>} _treeLogic - Логика для работы с деревом.
 * @returns {object} Объект с состоянием и функциями для управления выбором.
 */
export function useFzSelectSelectionLogic<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType>(
  props: Readonly<FzSelectProps<OptionType>>,
  modelValue: () => OptionType | OptionType[] | null,
  emit: FzSelectEmit,
  parsedPropsAccessors: ReturnType<typeof useFzSelectPropsParser<OptionType>>,
  _treeLogic: ReturnType<typeof useFzSelectTreeLogic<OptionType>>,
) {
  const { getActualOptionValue, getOptionResolvedChildren, isOptionNativelyDisabled } = parsedPropsAccessors

  /**
   * @description Реф для предотвращения открытия выпадающего списка в определенных ситуациях (например, при удалении тега).
   * @type {Ref<boolean>}
   */
  const preventOpen = ref(false)

  /**
   * @description Вычисляемое свойство для двусторонней привязки `modelValue` с `ComboboxRoot`.
   */
  const comboboxModelValue = computed({
    get: () => {
      if (props.tree && props.multiple) {
        return undefined
      }
      return toValue(modelValue) ?? undefined
    },
    set: (valFromCombobox: OptionType | OptionType[] | undefined | null) => {
      if (props.tree && props.multiple) {
        return
      }
      emit('update:modelValue', valFromCombobox ?? null)
    },
  })

  const selectedValuesSet = computed(() => {
    if (!props.multiple) {
      return null
    }
    const mv = toValue(modelValue)
    if (Array.isArray(mv)) {
      return new Set(mv.map(item => getActualOptionValue(item as OptionType)))
    }
    return new Set()
  })

  /**
   * @description Проверяет, выбрана ли указанная опция.
   * @param {OptionType} option - Опция для проверки.
   * @returns {boolean} `true`, если опция выбрана, иначе `false`.
   */
  function isOptionSelected(option: OptionType): boolean {
    const valToCompare = getActualOptionValue(option)

    const modelValueValue = toValue(modelValue)
    if (props.multiple) {
      return selectedValuesSet.value ? selectedValuesSet.value.has(valToCompare) : false
    }
    return modelValueValue !== null && modelValueValue !== undefined && getActualOptionValue(modelValueValue as OptionType) === valToCompare
  }

  /**
   * @description Определяет, отключена ли опция, учитывая ее "нативное" состояние и логику дерева.
   * @param {OptionType} option - Объект опции.
   * @returns {boolean} `true`, если опция отключена, иначе `false`.
   */
  function isOptionDisabled(option: OptionType): boolean {
    const nativelyDisabled = isOptionNativelyDisabled(option)

    if (nativelyDisabled || !props.tree) {
      return nativelyDisabled
    }

    return nativelyDisabled
  }

  const checkboxStateCache = new WeakMap<OptionType, Map<string, 'checked' | 'unchecked' | 'indeterminate'>>()

  /**
   * @description Получает состояние чекбокса для опции (checked, unchecked, indeterminate).
   * @param {OptionType} option - Объект опции.
   */
  function getCheckboxState(option: OptionType): 'checked' | 'unchecked' | 'indeterminate' {
    const modelValueString = JSON.stringify(toValue(modelValue))

    if (checkboxStateCache.has(option)) {
      const modelValueCache = checkboxStateCache.get(option)!
      if (modelValueCache.has(modelValueString)) {
        return modelValueCache.get(modelValueString)!
      }
    }

    let result: 'checked' | 'unchecked' | 'indeterminate'

    if (!props.tree || (!props.multiple && props.itemType !== 'checkbox')) {
      result = isOptionSelected(option) ? 'checked' : 'unchecked'
    }
    else {
      const isSelected = isOptionSelected(option)
      const children = getOptionResolvedChildren(option)

      if (isSelected) {
        result = 'checked'
      }
      else if (!children || children.length === 0) {
        let ancestorSelected = false
        if (props.tree) {
          const currentModelValue = toValue(modelValue)
          if (Array.isArray(currentModelValue)) {
            for (const selectedItem of currentModelValue) {
              if (isDescendantOf(option, selectedItem as OptionType)) {
                ancestorSelected = true
                break
              }
            }
          }
        }
        result = ancestorSelected ? 'checked' : 'unchecked'
      }
      else {
        const enabledChildren = children.filter(child => !isOptionDisabled(child))
        if (enabledChildren.length === 0) {
          result = 'unchecked'
        }
        else {
          let checkedEnabledChildrenCount = 0
          let indeterminateEnabledChildrenCount = 0

          for (const child of enabledChildren) {
            const childState = getCheckboxState(child) // Recursive call still happens here
            if (childState === 'checked') {
              checkedEnabledChildrenCount++
            }
            else if (childState === 'indeterminate') {
              indeterminateEnabledChildrenCount++
            }
          }

          if (checkedEnabledChildrenCount === enabledChildren.length) {
            result = 'checked'
          }
          else if (checkedEnabledChildrenCount > 0 || indeterminateEnabledChildrenCount > 0) {
            result = 'indeterminate'
          }
          else {
            result = 'unchecked'
          }
        }
      }
    }

    if (!checkboxStateCache.has(option)) {
      checkboxStateCache.set(option, new Map())
    }
    checkboxStateCache.get(option)!.set(modelValueString, result)

    return result
  }

  /**
   * @description Проверяет, является ли опция потомком другой опции.
   * @param {OptionType} option - Опция для проверки.
   * @param {OptionType} potentialAncestor - Потенциальный предок.
   * @returns {boolean} `true`, если опция является потомком, иначе `false`.
   */
  function isDescendantOf(option: OptionType, potentialAncestor: OptionType): boolean {
    const checkDescendants = (parent: OptionType): boolean => {
      const children = getOptionResolvedChildren(parent)
      if (!children)
        return false

      for (const child of children) {
        if (getActualOptionValue(child) === getActualOptionValue(option)) {
          return true
        }
        if (checkDescendants(child)) {
          return true
        }
      }
      return false
    }

    return checkDescendants(potentialAncestor)
  }

  /**
   * @description Обрабатывает выбор/снятие выбора чекбокса для опции в режиме дерева.
   * @param {OptionType} option - Опция, для которой изменилось состояние чекбокса.
   */
  function handleCheckboxSelect(option: OptionType) {
    if (isOptionDisabled(option)) {
      return
    }

    const currentState = getCheckboxState(option)
    const newTargetState = currentState === 'checked' || currentState === 'indeterminate'
      ? 'unchecked'
      : 'checked'

    let newModel: OptionType[] = Array.isArray(modelValue()) ? [...(modelValue() as OptionType[])] : []

    if (newTargetState === 'checked') {
      // Если чекбокс отмечен, добавляем опцию в модель
      const optionValue = getActualOptionValue(option)
      const existingValues = new Set(newModel.map(opt => getActualOptionValue(opt)))

      if (!existingValues.has(optionValue)) {
        newModel.push(option)
      }

      // Если опция имеет дочерние элементы, добавляем только родителя, а не дочерние элементы (для чистого отображения)
      // Дочерние элементы считаются выбранными через родителя
      const children = getOptionResolvedChildren(option)
      if (props.tree && children && children.length > 0) {
        // Однако мы должны удалить любые существующие дочерние элементы из модели
        // так как родитель теперь представляет их
        const collectDescendantValues = (parent: OptionType, values: Set<FzSelectValue>) => {
          const parentChildren = getOptionResolvedChildren(parent)
          if (parentChildren) {
            for (const child of parentChildren) {
              if (!isOptionDisabled(child)) {
                values.add(getActualOptionValue(child))
                collectDescendantValues(child, values)
              }
            }
          }
        }

        const descendantValues = new Set<FzSelectValue>()
        collectDescendantValues(option, descendantValues)
        newModel = newModel.filter(item => !descendantValues.has(getActualOptionValue(item)))
      }
    }
    else {
      // Когда чекбокс снят, мы должны обработать три случая:
      // 1. Опция сама по себе находится в модели - удаляем ее и всех ее потомков
      // 2. Опция появляется в модели из-за выбора родителя - нужно заменить родителя его дочерними элементами, исключая эту опцию
      // 3. Опция находится в неопределенном состоянии (некоторые дочерние элементы выбраны) - удаляем всех ее потомков

      const optionValue = getActualOptionValue(option)
      const isDirectlySelected = newModel.some(item => getActualOptionValue(item) === optionValue)
      const isIndeterminate = currentState === 'indeterminate'

      if (isDirectlySelected) {
        // 1. Опция сама по себе находится в модели - удаляем ее и всех ее потомков
        const affectedOptions: OptionType[] = [option]

        if (props.tree) {
          const collectDescendants = (parent: OptionType, descendants: OptionType[]) => {
            const children = getOptionResolvedChildren(parent)
            if (children) {
              for (const child of children) {
                if (!isOptionDisabled(child)) {
                  descendants.push(child)
                  collectDescendants(child, descendants)
                }
              }
            }
          }
          collectDescendants(option, affectedOptions)
        }

        const valuesToUnselect = new Set(affectedOptions.map(opt => getActualOptionValue(opt)))
        newModel = newModel.filter(item => !valuesToUnselect.has(getActualOptionValue(item)))
      }
      else if (isIndeterminate) {
        // 3. Опция находится в неопределенном состоянии (некоторые дочерние элементы выбраны) - снимаем всех ее потомков
        const collectAllDescendants = (parent: OptionType, descendants: OptionType[]) => {
          const children = getOptionResolvedChildren(parent)
          if (children) {
            for (const child of children) {
              if (!isOptionDisabled(child)) {
                descendants.push(child)
                collectAllDescendants(child, descendants)
              }
            }
          }
        }

        const descendantsToUnselect: OptionType[] = []
        collectAllDescendants(option, descendantsToUnselect)

        const valuesToUnselect = new Set(descendantsToUnselect.map(opt => getActualOptionValue(opt)))
        newModel = newModel.filter(item => !valuesToUnselect.has(getActualOptionValue(item)))
      }
      else {
        // 2. Опция появляется в модели из-за выбора родителя - нужно заменить родителя его дочерними элементами, исключая эту опцию
        const ancestorsToReplace: OptionType[] = []

        for (const selectedItem of newModel) {
          if (isDescendantOf(option, selectedItem)) {
            ancestorsToReplace.push(selectedItem)
          }
        }

        // Для каждого родителя заменяем его дочерними элементами, исключая эту опцию
        for (const ancestor of ancestorsToReplace) {
          // Удаляем родителя из модели
          const ancestorValue = getActualOptionValue(ancestor)
          newModel = newModel.filter(item => getActualOptionValue(item) !== ancestorValue)

          // Добавляем всех дочерних элементов родителя, кроме опции, которая снимается, и ее потомков
          const collectChildrenExcluding = (parent: OptionType, excludeOption: OptionType, children: OptionType[]) => {
            const parentChildren = getOptionResolvedChildren(parent)
            if (parentChildren) {
              for (const child of parentChildren) {
                if (!isOptionDisabled(child)) {
                  const childValue = getActualOptionValue(child)
                  const excludeValue = getActualOptionValue(excludeOption)

                  // Не добавляем опцию, которая снимается, и ее потомков
                  if (childValue !== excludeValue && !isDescendantOf(child, excludeOption)) {
                    children.push(child)
                  }

                  // Рекурсивно собираем дочерние элементы, но пропускаем поддерево опции, которая снимается
                  if (childValue !== excludeValue) {
                    collectChildrenExcluding(child, excludeOption, children)
                  }
                }
              }
            }
          }

          const childrenToAdd: OptionType[] = []
          collectChildrenExcluding(ancestor, option, childrenToAdd)
          newModel = [...newModel, ...childrenToAdd]
        }
      }
    }

    emit('update:modelValue', newModel)
  }

  /**
   * @description Обрабатывает удаление тега (для множественного выбора).
   * @param {FzSelectValue} tagValueToDelete - Значение тега для удаления.
   */
  function handleTagDelete(tagValueToDelete: FzSelectValue) {
    const modelValueValue = toValue(modelValue)
    if (Array.isArray(modelValueValue)) {
      const newSelectedOptions = modelValueValue.filter(
        option => getActualOptionValue(option as OptionType) !== tagValueToDelete,
      )
      emit('update:modelValue', newSelectedOptions)
    }
    else {
      emit('update:modelValue', null)
    }
    preventOpen.value = true
    setTimeout(() => {
      preventOpen.value = false
    }, 40)
  }

  /**
   * @description Очищает текущий выбор.
   */
  function clearSelection(event?: Event) {
    if (event) {
      // Разрешаем событию распространяться для обработки состояния Combobox, но останавливаем наше собственное немедленное распространение.
      event.stopPropagation()
    }
    if (props.multiple) {
      emit('update:modelValue', [])
    }
    else {
      emit('update:modelValue', null)
    }
    preventOpen.value = true
    setTimeout(() => {
      preventOpen.value = false
    }, 40)
  }

  /**
   * @description Предотвращает стандартное поведение выбора элемента ComboboxItem, если это множественный выбор в режиме дерева,
   * так как выбор обрабатывается через handleCheckboxSelect.
   * @param {Event} event - Событие выбора.
   */
  function preventSelectIfMultipleTree(event: Event) {
    if (props.multiple && props.tree) {
      event.preventDefault()
      event.stopPropagation()
    }
  }

  return {
    preventOpen,
    comboboxModelValue,
    isOptionSelected,
    isOptionDisabled,
    getCheckboxState,
    handleCheckboxSelect,
    handleTagDelete,
    clearSelection,
    preventSelectIfMultipleTree,
  }
}
