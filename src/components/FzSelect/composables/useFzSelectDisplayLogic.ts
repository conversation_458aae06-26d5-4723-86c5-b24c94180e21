import type { Ref } from 'vue'
import type {
  FlattenedOption,
  FzSelectDefaultOptionType,
  FzSelectOption,
  FzSelectOptionBase,
  FzSelectProps,
  FzSelectTriggerDisplayValue,
  FzSelectTriggerDisplayValueTreeItem,
} from '../types'
import type { useFzSelectPropsParser } from './useFzSelectPropsParser'
import type { useFzSelectSelectionLogic } from './useFzSelectSelectionLogic'
import type { useFzSelectTreeLogic } from './useFzSelectTreeLogic'
import { useFilter } from 'reka-ui'
import { computed, toValue } from 'vue'

/**
 * @description Компоубл для управления логикой отображения данных в FzSelect.
 * @template OptionType Тип объекта опции, расширяющий FzSelectOptionBase.
 * @param {Readonly<FzSelectProps<OptionType>>} props - Реактивные свойства компонента FzSelect.
 * @param {OptionType | OptionType[] | null} modelValue - Реактивная ссылка на `modelValue`.
 * @param {Ref<string>} searchTerm - Реактивная ссылка на поисковый запрос.
 * @param {ReturnType<typeof useFzSelectPropsParser<OptionType>>} parsedPropsAccessors - Аксессоры для свойств опций.
 * @param {ReturnType<typeof useFzSelectTreeLogic<OptionType>>} treeLogic - Логика для работы с деревом.
 * @param {ReturnType<typeof useFzSelectSelectionLogic<OptionType>>} selectionLogic - Логика для управления выбором.
 * @returns {object} Объект с вычисляемыми значениями для отображения.
 */
export function useFzSelectDisplayLogic<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType>(
  props: Readonly<FzSelectProps<OptionType>>,
  modelValue: () => OptionType | OptionType[] | null,
  searchTerm: Ref<string>,
  parsedPropsAccessors: ReturnType<typeof useFzSelectPropsParser<OptionType>>,
  treeLogic: ReturnType<typeof useFzSelectTreeLogic<OptionType>>,
  selectionLogic: ReturnType<typeof useFzSelectSelectionLogic<OptionType>>,
) {
  const { getActualOptionValue, getOptionResolvedLabel, getOptionResolvedChildren } = parsedPropsAccessors
  const { getOptionFullPath } = treeLogic
  const { isOptionDisabled } = selectionLogic // isOptionDisabled is needed for flattenedDisplayOptions

  const { contains } = useFilter({ sensitivity: 'base' })

  /**
   * @description Обрабатывает опцию дерева для отображения, извлекая путь и сегменты.
   */
  function processTreeOption(option: OptionType): FzSelectTriggerDisplayValueTreeItem {
    const value = getActualOptionValue(option)
    const fullPath = getOptionFullPath(option)
    const pathParts = fullPath.split(' > ')
    const children = getOptionResolvedChildren(option)
    const hasChildren = !!(children && children.length > 0)
    const pathSegments = pathParts.map((part, index) => ({
      truncatedValue: part.length > 49 ? `${part.substring(0, 46)}...` : null,
      originalValue: part,
      isParent: index < pathParts.length - 1 || (index === pathParts.length - 1 && hasChildren),
    }))
    return { value, pathSegments, fullPath }
  }

  /**
   * @description Создает "сплющенный" список опций для отображения, включая уровни для дерева.
   * @type {Ref<ReadonlyArray<FlattenedOption<OptionType>>>}
   */
  const flattenedDisplayOptions = computed((): ReadonlyArray<FlattenedOption<OptionType>> => {
    if (!props.tree) {
      return props.options.map(opt => ({
        originalValue: opt,
        value: opt.value,
        label: getOptionResolvedLabel(opt),
        actualValue: getActualOptionValue(opt),
        level: 0,
        hasChildren: false,
        disabled: isOptionDisabled(opt), // Используем isOptionDisabled из selectionLogic
      }))
    }

    const result: FlattenedOption<OptionType>[] = []
    function flatten(optionsToFlatten: ReadonlyArray<OptionType>, level: number) {
      optionsToFlatten.forEach((option) => {
        const children = getOptionResolvedChildren(option)
        result.push({
          originalValue: option,
          value: option.value,
          label: getOptionResolvedLabel(option),
          actualValue: getActualOptionValue(option),
          level,
          hasChildren: !!(children && children.length > 0),
          disabled: isOptionDisabled(option), // Используем isOptionDisabled из selectionLogic
        })
        if (children && children.length > 0) {
          flatten(children, level + 1)
        }
      })
    }
    flatten(props.options, 0)
    return result
  })

  /**
   * @description Финальный список опций для отображения, учитывающий поиск.
   * @type {Ref<ReadonlyArray<FlattenedOption<OptionType>>>}
   */
  const finalDisplayOptions = computed((): ReadonlyArray<FlattenedOption<OptionType>> => {
    const allFlattenedOptions = flattenedDisplayOptions.value
    if (props.searchable && searchTerm.value) {
      return allFlattenedOptions.filter(opt =>
        contains(opt.label, searchTerm.value),
      )
    }
    return allFlattenedOptions
  })

  /**
   * @description Текст, отображаемый в триггере селекта.
   */
  const triggerDisplayValue = computed((): FzSelectTriggerDisplayValue => {
    const modelValueValue = toValue(modelValue)
    if (props.multiple) {
      if (Array.isArray(modelValueValue) && modelValueValue?.length > 0) {
        if (props.tree) {
          return modelValueValue?.map((option) => {
            const currentOption = option as OptionType
            const { value, pathSegments, fullPath } = processTreeOption(currentOption)
            return { value, pathSegments, fullPath }
          })
        }
        else {
          return modelValueValue?.map((option: OptionType) => ({
            ...option,
            label: getOptionResolvedLabel(option),
            value: getActualOptionValue(option),
          }))
        }
      }
      return null
    }
    if (modelValueValue !== null && modelValueValue !== undefined) {
      if (props.tree) {
        const { value, pathSegments, fullPath } = processTreeOption(modelValueValue as OptionType)
        return [{ value, pathSegments, fullPath }]
      }
      return [modelValueValue as OptionType]
    }
    return null
  })

  /**
   * @description Теги для стандартного множественного выбора.
   * @type {Ref<Array<{ value: FzSelectValue; label: string; originalLabel: string }>>}
   */
  const defaultTags = computed(() => {
    const modelValueValue = toValue(modelValue)
    if (props.multiple && !props.tree && Array.isArray(modelValueValue) && modelValueValue?.length > 0) {
      return modelValueValue
        ?.map((option) => {
          const currentOption = option as OptionType
          const originalLabel = getOptionResolvedLabel(currentOption)
          let label = originalLabel
          if (label.length > 49) {
            label = `${label.substring(0, 46)}...`
          }
          return {
            label,
            originalLabel,
            value: getActualOptionValue(currentOption),
          }
        })
    }
    return []
  })

  /**
   * @description Показывает ли кнопку очистки.
   * @type {Ref<boolean>}
   */
  const showClearButton = computed(() => {
    if (props.multiple || props.disabled || !props.clearable) {
      return false
    }
    return toValue(modelValue) !== null && toValue(modelValue) !== undefined
  })

  /**
   * @description Показывает ли кнопку сброса.
   * @type {Ref<boolean>}
   */
  const showResetButton = computed(() => {
    const modelValueValue = toValue(modelValue)
    return props.showResetButton && props.multiple && Array.isArray(modelValueValue) && modelValueValue?.length > 0
  })

  return {
    flattenedDisplayOptions,
    finalDisplayOptions,
    triggerDisplayValue,
    defaultTags,
    showClearButton,
    showResetButton,
  }
}
