import type {
  FlattenedOption,
  FzSelectDefaultOptionType,
  FzSelectOption,
  FzSelectOptionBase,
  FzSelectProps,
  FzSelectValue,
} from '../types'

/**
 * @description Компоубл для парсинга свойств FzSelect и предоставления методов доступа к данным опций.
 * @template OptionType Тип объекта опции, расширяющий FzSelectOptionBase.
 * @param {Readonly<FzSelectProps<OptionType>>} props - Реактивные свойства компонента FzSelect.
 * @returns {object} Объект с функциями-аксессорами для работы с данными опций.
 */
export function useFzSelectPropsParser<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType>(
  props: Readonly<FzSelectProps<OptionType>>,
) {
  const actualValueCache = new WeakMap<OptionType, FzSelectValue>()
  /**
   * @description Получает действительное значение опции.
   * Гарантированно возвращает FzSelectValue (string, number, или Record<string, any>).
   * @param {OptionType} option - Объект опции.
   * @returns {FzSelectValue} Действительное значение опции.
   */
  function getActualOptionValue(option: OptionType): FzSelectValue {
    if (actualValueCache.has(option)) {
      return actualValueCache.get(option)!
    }

    let result: FzSelectValue
    if (typeof props.optionValue === 'function') {
      const val = props.optionValue(option)
      if (typeof val === 'string' || typeof val === 'number' || (typeof val === 'object' && val !== null)) {
        result = val as FzSelectValue
      }
      else if (typeof val === 'boolean') {
        result = val ? 1 : 0
      }
      else {
        console.warn(`[FzSelect] Value from optionValue function for option ${JSON.stringify(option)} (resolved as ${JSON.stringify(val)}) is not directly FzSelectValue. Converting to string.`)
        result = String(val)
      }
    }
    else if (typeof option === 'object' && option !== null && typeof props.optionValue === 'string' && props.optionValue in option) {
      const val = option[props.optionValue]
      if (typeof val === 'string' || typeof val === 'number' || (typeof val === 'object' && val !== null)) {
        result = val as FzSelectValue
      }
      else if (typeof val === 'boolean') {
        result = val ? 1 : 0
      }
      else {
        console.warn(`[FzSelect] Value from object key '${props.optionValue}' for option ${JSON.stringify(option)} (resolved as ${JSON.stringify(val)}) is not directly FzSelectValue. Converting to string.`)
        result = String(val)
      }
    }
    else if (typeof option === 'object' && option !== null && 'value' in option) {
      const val = (option as { value: any }).value
      if (typeof val === 'string' || typeof val === 'number' || (typeof val === 'object' && val !== null)) {
        result = val as FzSelectValue
      }
      else if (typeof val === 'boolean') {
        result = val ? 1 : 0
      }
      else {
        console.warn(`[FzSelect] Value from default 'value' property for option ${JSON.stringify(option)} (resolved as ${JSON.stringify(val)}) is not directly FzSelectValue. Converting to string.`)
        result = String(val)
      }
    }
    else if (typeof option === 'string' || typeof option === 'number' || (typeof option === 'object' && option !== null)) {
      result = option as FzSelectValue
    }
    else if (typeof option === 'boolean') {
      result = option ? 1 : 0
    }
    else {
      console.warn(`[FzSelect] Option ${JSON.stringify(option)} itself is not directly FzSelectValue and no accessor worked. Converting to string.`)
      result = String(option)
    }

    actualValueCache.set(option, result)
    return result
  }

  const labelCache = new WeakMap<OptionType, string>()
  /**
   * @description Получает отображаемую метку (label) для опции.
   * @param {OptionType} option - Объект опции.
   * @returns {string} Строковое представление метки опции.
   */
  function getOptionResolvedLabel(option: OptionType): string {
    if (labelCache.has(option)) {
      return labelCache.get(option)!
    }

    let result: string
    if (typeof props.optionLabel === 'function') {
      result = String(props.optionLabel(option))
    }
    else if (typeof option === 'object' && option !== null && typeof props.optionLabel === 'string' && props.optionLabel in option) {
      result = String(option[props.optionLabel])
    }
    else if (typeof option === 'object' && option !== null && 'label' in option) {
      result = String((option as { label: any }).label)
    }
    else {
      result = String(option)
    }

    labelCache.set(option, result)
    return result
  }

  const disabledCache = new WeakMap<OptionType, boolean>()
  /**
   * @description Определяет, является ли опция "нативно" отключенной на основе свойства `disabled` самой опции или конфигурации `optionDisabled`.
   * Не учитывает логику отключения родительских элементов в режиме дерева.
   * @param {OptionType} option - Объект опции.
   * @returns {boolean} `true`, если опция нативно отключена, иначе `false`.
   */
  function isOptionNativelyDisabled(option: OptionType): boolean {
    if (disabledCache.has(option)) {
      return disabledCache.get(option)!
    }

    let result: boolean
    if (typeof props.optionDisabled === 'function') {
      result = Boolean(props.optionDisabled(option))
    }
    else if (typeof option === 'object' && option !== null && typeof props.optionDisabled === 'string' && props.optionDisabled in option) {
      result = Boolean(option[props.optionDisabled])
    }
    else if (typeof option === 'object' && option !== null && 'disabled' in option) {
      result = Boolean((option as { disabled?: boolean }).disabled)
    }
    else {
      result = false
    }

    disabledCache.set(option, result)
    return result
  }

  const childrenCache = new WeakMap<OptionType, ReadonlyArray<OptionType> | undefined>()
  /**
   * @description Получает дочерние опции для указанной опции (актуально для режима дерева).
   * @param {OptionType} option - Родительский объект опции.
   * @returns {ReadonlyArray<OptionType> | undefined} Массив дочерних опций или `undefined`, если их нет или режим дерева отключен.
   */
  function getOptionResolvedChildren(option: OptionType): ReadonlyArray<OptionType> | undefined {
    if (childrenCache.has(option)) {
      return childrenCache.get(option)!
    }

    let result: ReadonlyArray<OptionType> | undefined
    if (props.tree) {
      if (typeof props.optionChildren === 'function') {
        result = props.optionChildren(option) as ReadonlyArray<OptionType> | undefined
      }
      else if (typeof option === 'object' && option !== null && typeof props.optionChildren === 'string' && props.optionChildren in option) {
        const children = option[props.optionChildren]
        result = Array.isArray(children) ? children as ReadonlyArray<OptionType> : undefined
      }
      else if (typeof option === 'object' && option !== null && 'children' in option) {
        const children = (option as { children?: any[] }).children
        result = Array.isArray(children) ? children as ReadonlyArray<OptionType> : undefined
      }
      else {
        result = undefined
      }
    }
    else {
      result = undefined
    }

    childrenCache.set(option, result)
    return result
  }

  /**
   * @description Сравнивает две опции по их действительным значениям.
   * @param {OptionType} optionA - Первая опция для сравнения.
   * @param {OptionType} optionB - Вторая опция для сравнения.
   * @returns {boolean} `true`, если действительные значения опций равны, иначе `false`.
   */
  function compareOptionsByActualValue(optionA: OptionType, optionB: OptionType): boolean {
    return getActualOptionValue(optionA) === getActualOptionValue(optionB)
  }

  /**
   * @description Получает текстовое значение для элемента списка (используется для `text-value` в `ComboboxItem`).
   * Если передана "сплющенная" опция, возвращает ее уже разрешенную метку.
   * Если передана обычная опция, разрешает ее метку.
   * @param {OptionType | FlattenedOption<OptionType>} option - Обычная или "сплющенная" опция.
   * @returns {string} Текстовое значение опции.
   */
  function getItemTextValue(option: OptionType | FlattenedOption<OptionType>): string {
    if (typeof option === 'object' && option !== null && 'originalValue' in option && 'actualValue' in option) {
      return (option as FlattenedOption<OptionType>).label
    }
    return getOptionResolvedLabel(option as OptionType)
  }

  return {
    getActualOptionValue,
    getOptionResolvedLabel,
    isOptionNativelyDisabled,
    getOptionResolvedChildren,
    compareOptionsByActualValue,
    getItemTextValue,
  }
}
