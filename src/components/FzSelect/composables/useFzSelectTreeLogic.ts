import type {
  FzSelectDefaultOptionType,
  FzSelectOption,
  FzSelectOptionBase,
  FzSelectProps,
  FzSelectValue,
} from '../types'
import type { useFzSelectPropsParser } from './useFzSelectPropsParser'
import { ref, watch } from 'vue'

/**
 * @description Компоубл для управления логикой древовидной структуры в FzSelect.
 * @template OptionType Тип объекта опции, расширяющий FzSelectOptionBase.
 * @param {Readonly<FzSelectProps<OptionType>>} props - Реактивные свойства компонента FzSelect.
 * @param {ReturnType<typeof useFzSelectPropsParser<OptionType>>} parsedPropsAccessors - Объект с функциями-аксессорами из useFzSelectPropsParser.
 * @returns {object} Объект с данными и функциями для работы с деревом.
 */
export function useFzSelectTreeLogic<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType>(
  props: Readonly<FzSelectProps<OptionType>>,
  parsedPropsAccessors: ReturnType<typeof useFzSelectPropsParser<OptionType>>,
) {
  const { getActualOptionValue, getOptionResolvedLabel, getOptionResolvedChildren } = parsedPropsAccessors

  /**
   * @description Карта для хранения связей "родитель -> массив дочерних значений".
   * Ключ - действительное значение родительской опции.
   * Значение - массив действительных значений дочерних опций.
   */
  const parentChildMap = ref(new Map<FzSelectValue, FzSelectValue[]>())

  /**
   * @description Карта дочерних элементов к их родителям.
   */
  const childParentMap = ref(new Map<FzSelectValue, FzSelectOptionBase<FzSelectOption>>())

  /**
   * @description Строит карты связей `parentChildMap` и `childParentMap`.
   */
  function buildRelationshipMaps() {
    parentChildMap.value.clear()
    childParentMap.value.clear()

    const localValueCache = new Map<OptionType, FzSelectValue>()

    /**
     * @description Вспомогательная функция для получения кэшированного действительного значения опции.
     * @param {OptionType} option - Опция.
     * @returns {FzSelectValue} Кэшированное действительное значение.
     */
    const getCachedActualOptionValue = (option: OptionType): FzSelectValue => {
      if (localValueCache.has(option)) {
        return localValueCache.get(option)!
      }
      const val = getActualOptionValue(option)
      localValueCache.set(option, val)
      return val
    }

    /**
     * @description Рекурсивно строит карту `parentChildMap`.
     * @param {ReadonlyArray<OptionType>} optionsToProcess - Опции для обработки.
     * @param {OptionType | null} parentOpt - Родительская опция.
     * @param {FzSelectValue | null} parentActualVal - Действительное значение родительской опции.
     */
    const buildParentChildMapRecursive = (
      optionsToProcess: ReadonlyArray<OptionType>,
      parentOpt: OptionType | null,
      parentActualVal: FzSelectValue | null,
    ) => {
      for (const opt of optionsToProcess) {
        const currentActualVal = getCachedActualOptionValue(opt)

        if (parentOpt && parentActualVal !== null) {
          if (!parentChildMap.value.has(parentActualVal)) {
            parentChildMap.value.set(parentActualVal, [])
          }
          parentChildMap.value.get(parentActualVal)!.push(currentActualVal)
          childParentMap.value.set(currentActualVal, parentOpt as OptionType)
        }

        const children = getOptionResolvedChildren(opt)
        if (children && children.length > 0) {
          buildParentChildMapRecursive(children, opt, currentActualVal)
        }
      }
    }

    buildParentChildMapRecursive(props.options, null, null)
  }

  watch(() => props.options, () => {
    buildRelationshipMaps()
  }, { deep: true, immediate: true })

  const fullPathCache = new WeakMap<OptionType, string>()

  /**
   * @description Получает полный путь для опции в дереве (например, "Родитель > Под-родитель > Элемент").
   * @param {OptionType} option - Опция, для которой нужно получить путь.
   * @returns {string} Строка, представляющая полный путь.
   */
  function getOptionFullPath(option: OptionType): string {
    if (fullPathCache.has(option)) {
      return fullPathCache.get(option)!
    }

    let result: string
    if (!props.tree) {
      result = getOptionResolvedLabel(option)
    }
    else {
      const optionValue = getActualOptionValue(option)
      const path: string[] = [getOptionResolvedLabel(option)]
      let currentParentKey: FzSelectValue = optionValue
      let parent = childParentMap.value.get(currentParentKey) as OptionType | undefined

      while (parent) {
        path.unshift(getOptionResolvedLabel(parent as OptionType))
        const parentActualValue = getActualOptionValue(parent as OptionType)
        currentParentKey = parentActualValue
        parent = childParentMap.value.get(currentParentKey) as OptionType | undefined
      }
      result = path.join(' > ')
    }

    fullPathCache.set(option, result)
    return result
  }

  watch(() => props.options, () => {
    fullPathCache.delete(props.options as any)
  }, { deep: true })

  return {
    parentChildMap,
    childParentMap,
    getOptionFullPath,
  }
}
