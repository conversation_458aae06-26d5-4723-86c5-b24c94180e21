import type { Ref } from 'vue'
import type { FzCustomScrollbar } from '../../FzCustomScrollbar'
import type { FlattenedOption, FzSelectOption, FzSelectOptionBase, FzSelectProps } from '../types'
import { useVirtualizer } from '@tanstack/vue-virtual'
import { computed, nextTick, ref, watch } from 'vue'

/**
 * @description Композабл для обработки логики виртуализации и кастомного скроллбара в FzSelect.
 * @template OptionType Тип объекта опции, расширяющий FzSelectOptionBase.
 * @param {Readonly<FzSelectProps<OptionType>>} props - Реактивные свойства компонента FzSelect.
 * @param {Ref<ReadonlyArray<FlattenedOption<OptionType>>>} finalDisplayOptions - Финальный список опций для отображения.
 * @returns {object} Объект с логикой виртуализации и кастомного скроллбара.
 */
export function useFzSelectVirtualization<OptionType extends FzSelectOptionBase<FzSelectOption>>(
  props: Readonly<FzSelectProps<OptionType>>,
  finalDisplayOptions: Ref<ReadonlyArray<FlattenedOption<OptionType>>>,
) {
  // Рефы для интеграции с кастомным скроллбаром
  const customScrollbarRef = ref<InstanceType<typeof FzCustomScrollbar> | null>(null)
  const scrollElementRef = ref<HTMLElement | null>(null)

  // Настройка TanStack Virtual
  const virtualizer = computed(() => {
    if (!props.virtualize || !scrollElementRef.value || finalDisplayOptions.value.length === 0) {
      return null
    }

    return useVirtualizer({
      count: finalDisplayOptions.value.length,
      getScrollElement: () => scrollElementRef.value,
      estimateSize: () => props.virtualizeEstimateSize || 36,
      overscan: 6,
    })
  })

  // Получение элемента прокрутки из FzCustomScrollbar
  watch(customScrollbarRef, async (newRef) => {
    if (newRef?.simplebarInstance) {
      scrollElementRef.value = newRef.simplebarInstance.getScrollElement() || null
    }
    else {
      // Если simplebarInstance еще не готов, ждем его
      await nextTick()
      if (newRef?.simplebarInstance) {
        scrollElementRef.value = newRef.simplebarInstance.getScrollElement() || null
      }
    }
  }, { immediate: true })

  // Отслеживание появления simplebarInstance
  watch(() => customScrollbarRef.value?.simplebarInstance, (simplebarInstance) => {
    if (simplebarInstance) {
      scrollElementRef.value = simplebarInstance.getScrollElement() || null
    }
  }, { immediate: true })

  return {
    customScrollbarRef,
    scrollElementRef,
    virtualizer,
  }
}
