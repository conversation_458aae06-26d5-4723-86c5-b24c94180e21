import type { Ref } from 'vue'
import type { FzSelectDefaultOptionType, FzSelectOption, FzSelectOptionBase, FzSelectProps } from '../types'
import { computed } from 'vue'

/**
 * @description Композабл для вычисления динамических CSS-классов для компонента FzSelect.
 * @template OptionType Тип объекта опции, расширяющий FzSelectOptionBase.
 * @param {Readonly<FzSelectProps<OptionType>>} props - Реактивные свойства компонента FzSelect.
 * @param {Ref<boolean>} open - Реактивная ссылка на состояние открытости выпадающего списка.
 * @returns {object} Объект с вычисляемыми свойствами для CSS-классов.
 */
export function useFzSelectClasses<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType>(
  props: Readonly<FzSelectProps<OptionType>>,
  open: Ref<boolean>,
) {
  /**
   * @description Вычисляемые классы для корневого элемента Combobox.
   * @type {Ref<object>}
   */
  const comboboxRootClass = computed(() => {
    return {
      'fz-select': true,
      'fz-select--disabled': props.disabled,
      'fz-select--loading': props.loading,
      'fz-select--open': open.value,
      'fz-select--borderline': props.variant === 'borderline',
      'fz-select--underline': props.variant === 'underline',
      'fz-select--multiple': props.multiple,
      'fz-select--invalid': props.invalid,
    }
  })

  /**
   * @description Вычисляемые классы для триггера (кнопки) Combobox.
   * @type {Ref<object>}
   */
  const comboboxTriggerClass = computed(() => {
    return {
      'fz-select__trigger': true,
      'fz-select__trigger--borderline': props.variant === 'borderline',
      'fz-select__trigger--underline': props.variant === 'underline',
      'fz-select__trigger--invalid': props.invalid,
    }
  })

  /**
   * @description Вычисляемые классы для контентной части (выпадающего списка) Combobox.
   * @type {Ref<object>}
   */
  const comboboxContentClass = computed(() => {
    return {
      'fz-select__content': true,
      'fz-select__content--loading': props.loading,
      'fz-select__content--multiple': props.multiple,
    }
  })

  return {
    comboboxRootClass,
    comboboxTriggerClass,
    comboboxContentClass,
  }
}
