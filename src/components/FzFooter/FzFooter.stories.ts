import type { Meta, StoryObj } from '@storybook/vue3'

import FzFooter from './FzFooter.vue'

const meta: Meta<typeof FzFooter> = {
  title: 'Components/FzFooter',
  component: FzFooter,
  tags: ['autodocs'],
  argTypes: {
    policyLink: {
      control: 'text',
      description: 'link "Политика конфиденциальности"',
    },
    userConsentLink: {
      control: 'text',
      description: 'link "Пользовательское соглашение"',
    },
  },
  args: {},
}

export default meta
type Story = StoryObj<typeof FzFooter>

export const Default: Story = {
  args: {
  },
}
