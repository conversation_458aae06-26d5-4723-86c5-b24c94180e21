<script setup lang="ts">
import { FzIcon } from '../FzIcon'
import { FzLogo } from '../FzLogo'

withDefaults(defineProps<{
  policyLink?: string
  userConsentLink?: string
}>(), {
  policyLink: '#',
  userConsentLink: '#',
})

const currentYear = new Date().getFullYear()
</script>

<template>
  <div class="fz-footer">
    <div class="fz-footer__block-left">
      <div class="fz-footer__block-left--left">
        <a href="/" class="fz-footer__logo">
          <FzLogo />
        </a>
        <p>© FOQUZ {{ currentYear }}</p>
      </div>
      <div class="fz-footer__block-left--right">
        <a class="fz-footer__link" :href="policyLink" download target="_blank">Политика конфиденциальности</a>
        <a class="fz-footer__link" :href="userConsentLink" download target="_blank">Пользовательское соглашение</a>
      </div>
    </div>
    <div class="fz-footer__block-right">
      <div class="fz-footer__block-social-networks">
        <a href="https://t.me/foquz_cx" target="_blank" class="fz-footer__link">
          <FzIcon name="tg" size="lg" />
        </a>
        <a href="https://vk.com/foquz" target="_blank" class="fz-footer__link">
          <FzIcon name="vk" size="lg" />
        </a>
      </div>
      <div class="fz-footer__block-right--right">
        <a href="tel:+78005002637" class="fz-footer__phone">****** 500 26 37</a>
        <p>9:00–18:00, пн–пт (время Мск)</p>
        <a href="mailto:<EMAIL>" class="fz-footer__mail"><EMAIL></a>
      </div>
    </div>
  </div>
</template>

<style>
.fz-footer{
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--fz-spacing-10);
  font: var(--fz-font-caption);
  padding: var(--fz-spacing-12) var(--fz-spacing-10);
  background: var(--fz-color-surface-background);
  color: var(--fz-color-text-secondary);
}

.fz-footer p{
  margin: 0;
}

.fz-footer__block-left{
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--fz-spacing-16);
}

.fz-footer__logo{
  display: block;
  transition: color var(--fz-transition-duration);
  color: var(--fz-color-surface-secondary-clicked);
  margin-bottom: var(--fz-spacing-5);
}

.fz-footer__logo .fz-logo{
  justify-content: flex-start;
}

.fz-footer__logo:hover{
  color: var(--fz-color-text-primary);
}

.fz-footer__block-left--right{
  display: flex;
  flex-direction: column;
  gap: var(--fz-spacing-2);
  margin-left: var(--fz-spacing-12);
}

.fz-footer__link{
  cursor: pointer;
  transition: color var(--fz-transition-duration);
  display: block;
  text-decoration: none;
  color: var(--fz-color-text-secondary);
}

.fz-footer__link:hover{
  color: var(--fz-color-text-primary);
}

.fz-footer__block-right{
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--fz-spacing-18);
}

.fz-footer__block-social-networks{
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-6);
}

.fz-footer__block-right--right{
  display: flex;
  flex-direction: column;
  gap: var(--fz-spacing-2);
}

.fz-footer__block-right--right .fz-footer__phone{
  text-decoration: none;
  color: var(--fz-color-text-secondary);
  font: var(--fz-font-body-bold);
}

.fz-footer__block-right--right .fz-footer__mail{
  transition: color var(--fz-transition-duration);
  text-decoration: none;
  color: var(--fz-color-text-link);
}

.fz-footer__block-right--right .fz-footer__mail:hover{
  text-decoration: none;
  color: var(--fz-color-brand-10);
}
</style>
