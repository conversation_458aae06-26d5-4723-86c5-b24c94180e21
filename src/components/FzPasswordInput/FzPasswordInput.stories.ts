import type { Meta, StoryObj } from '@storybook/vue3'

import FzPasswordInput from './FzPasswordInput.vue'

const meta: Meta<typeof FzPasswordInput> = {
  title: 'Components/FzInput/FzPasswordInput',
  component: FzPasswordInput,
  tags: ['autodocs'],
  argTypes: {},
  args: {
    modelValue: 'Password',
  },
}

export default meta
type Story = StoryObj<typeof FzPasswordInput>

export const Default: Story = {
  args: {
  },
}

export const Clearable: Story = {
  args: {
    clearable: true,
  },
}

export const isInvalid: Story = {
  args: {
    isInvalid: true,
  },
}

export const isSuccess: Story = {
  args: {
    isSuccess: true,
  },
}
