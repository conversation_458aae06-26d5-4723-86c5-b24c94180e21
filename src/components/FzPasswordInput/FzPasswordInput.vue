<script setup lang="ts">
import { ref } from 'vue'
import { FzIcon } from '../FzIcon'
import { FzInput } from '../FzInput'

withDefaults(
  defineProps<{
    maxlength?: number
    clearable?: boolean
    isInvalid?: boolean
    isSuccess?: boolean
  }>(),
  {
    maxlength: 20,
    clearable: false,
    isInvalid: false,
    isSuccess: false,
  },
)

const model = defineModel<string>()

const showPass = ref<boolean>(false)

function onShow(): void {
  showPass.value = !showPass.value
}
</script>

<template>
  <FzInput
    v-model="model"
    class="fz-password-input"
    variant="outline"
    :type="showPass ? 'text' : 'password'"
    :maxlength="maxlength"
    :clearable="clearable"
    :is-invalid="isInvalid"
    :is-success="isSuccess"
  >
    <template #appendIcon>
      <FzIcon
        class="fz-password-input--icon" :class="{ 'fz-password-input__show-pass': !model }"
        :name="showPass ? 'show-eye' : 'hide-strike-eye'"
        size="md"
        clickable
        @click="onShow"
      />
    </template>
  </FzInput>
</template>

<style>
.fz-password-input--icon{
  transition: var(--fz-transition-duration) var(--fz-transition-timing-function);
}

.fz-password-input__show-pass{
  opacity: 50%;
}
</style>
