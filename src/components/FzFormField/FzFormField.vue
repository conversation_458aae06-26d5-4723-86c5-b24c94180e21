<script setup lang="ts">
import { FzIcon } from '../FzIcon'
import { FzTooltip } from '../FzTooltip'

withDefaults(defineProps<{
  label?: string
  size?: 'sm' | 'lg'
  tooltip?: string
  required?: boolean
  errorText?: string
}
>(), {
  size: 'lg',
  required: false,
})
</script>

<template>
  <div class="fz-form-field">
    <p
      class="fz-form-field__title"
      :class="`fz-form-field__title--${size}`"
    >
      <span>
        {{ label }}<span v-if="required" class="fz-form-field--required">*</span>
      </span>
      <span v-if="tooltip" class="fz-form-field__tooltip">
        <FzTooltip :content="tooltip">
          <FzIcon name="info" size="md" />
        </FzTooltip>
      </span>
    </p>
    <slot />
    <span
      v-if="errorText"
      class="fz-form-field__error"
      :class="`fz-form-field__error--${size}`"
    >
      {{ errorText }}
    </span>
  </div>
</template>

<style>
.fz-form-field{

}

.fz-form-field__title{
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-2);
  color: var(--fz-color-text-primary);
}

.fz-form-field__tooltip{
  color: var(--fz-color-border-icon-input);
}

.fz-form-field__title--sm{
  margin-top: 0;
  margin-bottom: 4px;
  font: var(--fz-font-caption-bold);

}

.fz-form-field__title--lg{
  margin-top: 0;
  margin-bottom: 8px;
  font: var(--fz-font-label);
}

.fz-form-field--required{
  color: var(--fz-color-text-critical);
}

.fz-form-field__error{
  display: block;
  color: var(--fz-color-text-critical);
  font: var(--fz-font-hint);
}

.fz-form-field__error--sm{
  margin-top: 4px;
}

.fz-form-field__error--lg{
  margin-top: 8px;
}
</style>
