import type { StoryObj } from '@storybook/vue3'

import { ref } from 'vue'
import FzInput from '../FzInput/FzInput.vue'
import FzForm<PERSON>ield from './FzFormField.vue'

const meta = {
  title: 'Components/FzFormField',
  component: FzFormField,
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Label text',
    },
    size: {
      control: 'select',
      description: 'Size text',
      options: ['sm', 'lg'],
    },
    tooltip: {
      control: 'text',
      description: 'Tooltip text',
    },
    required: {
      control: 'boolean',
      description: 'Required text',
    },
    errorText: {
      control: 'text',
      description: 'Error text',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    label: 'label text',
    size: 'lg',
  },
  render: args => ({
    components: { FzFormField, FzInput },
    setup() {
      const modelText = ref('')
      return { args, modelText }
    },
    template: `<FzFormField v-bind="args">
       <FzInput v-model="modelText" />
    </FzFormField>`,
  }),
}

export const SizeSm: Story = {
  args: {
    label: 'label text size sm',
    size: 'sm',
  },
  render: args => ({
    components: { FzFormField, FzInput },
    setup() {
      const modelText = ref('')
      return { args, modelText }
    },
    template: `<FzFormField v-bind="args">
       <FzInput v-model="modelText" variant="underline"/>
    </FzFormField>`,
  }),
}

export const Tooltip: Story = {
  args: {
    label: 'label text tooltip',
    tooltip: 'label text tooltip',
  },
  render: args => ({
    components: { FzFormField, FzInput },
    setup() {
      const modelText = ref('')
      return { args, modelText }
    },
    template: `<FzFormField v-bind="args">
       <FzInput v-model="modelText" />
    </FzFormField>`,
  }),
}

export const Required: Story = {
  args: {
    label: 'label text tooltip',
    required: true,
  },
  render: args => ({
    components: { FzFormField, FzInput },
    setup() {
      const modelText = ref('')
      return { args, modelText }
    },
    template: `<FzFormField v-bind="args">
       <FzInput v-model="modelText" />
    </FzFormField>`,
  }),
}

export const Error: Story = {
  args: {
    label: 'label text error',
    required: true,
    errorText: 'label text error',
  },
  render: args => ({
    components: { FzFormField, FzInput },
    setup() {
      const modelText = ref('')
      return { args, modelText }
    },
    template: `<FzFormField v-bind="args">
       <FzInput v-model="modelText" />
    </FzFormField>`,
  }),
}
