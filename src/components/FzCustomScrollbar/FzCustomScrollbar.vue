<script setup lang="ts">
import type SimpleBarCore from 'simplebar-core' // Import type for SimpleBar instance
import type { SimpleBarOptions } from 'simplebar-core'
import type { PropType } from 'vue'
import simplebar from 'simplebar-vue' // Import the simplebar-vue component
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
// @TODO: migrate all the styles to here
import 'simplebar-vue/dist/simplebar.min.css'

const props = defineProps({
  showGradientShadows: {
    type: Boolean,
    default: true,
  },
  options: {
    type: Object as PropType<SimpleBarOptions>,
    default: () => ({
      autoHide: false,
      forceVisible: false,
      direction: 'auto',
      clickOnTrack: true,
      scrollbarMinSize: 34,
    }),
  },
})

// simplebarRef will hold the simplebar-vue component instance
type SimplebarRef = InstanceType<typeof simplebar>
type SidebarRefWithSimplebar = SimplebarRef & { SimpleBar: SimpleBarCore }

const simplebarRef = ref<SidebarRefWithSimplebar | null>(null)
// simplebarInstance will hold the actual SimpleBar instance
const simplebarInstance = ref<SimpleBarCore | null>(null)

const showTopShadow = ref(false)
const showBottomShadow = ref(false)
// Add refs for horizontal shadows
const showLeftShadow = ref(false)
const showRightShadow = ref(false)
const hasScroll = ref(false)

// Use simplebarInstance to access the SimpleBar instance methods
const scrollElement = computed(() => simplebarInstance.value?.getScrollElement())

function checkHasScroll() {
  if (scrollElement.value) {
    const el = scrollElement.value
    hasScroll.value = el.scrollHeight > el.clientHeight + 1
  }
  return hasScroll.value
}

function handleScroll() {
  checkHasScroll()
  if (!props.showGradientShadows || !scrollElement.value) {
    showTopShadow.value = false
    showBottomShadow.value = false
    // Also hide horizontal shadows if the prop is false
    showLeftShadow.value = false
    showRightShadow.value = false
    return
  }

  const el = scrollElement.value
  const scrollTop = el.scrollTop
  const scrollHeight = el.scrollHeight
  const clientHeight = el.clientHeight
  const scrollLeft = el.scrollLeft // Get horizontal scroll position
  const scrollWidth = el.scrollWidth // Get total scrollable width
  const clientWidth = el.clientWidth // Get visible width
  const threshold = 5 // px, to avoid flickering at exact edges

  // Vertical shadow logic (existing)
  showTopShadow.value = scrollTop > threshold
  showBottomShadow.value = scrollTop < scrollHeight - clientHeight - threshold

  // Horizontal shadow logic (new)
  showLeftShadow.value = scrollLeft > threshold
  showRightShadow.value = scrollLeft < scrollWidth - clientWidth - threshold
}

onMounted(async () => {
  await nextTick() // Ensure simplebarRef is populated

  if (simplebarRef.value) {
    if (simplebarRef.value.SimpleBar as SimpleBarCore) {
      simplebarInstance.value = simplebarRef.value.SimpleBar as SimpleBarCore
      checkHasScroll()
    }
  }

  if (simplebarInstance.value) {
    handleScroll() // Initial check
    if (scrollElement.value) {
      scrollElement.value.addEventListener('scroll', handleScroll, { passive: true })
    }
  }
  else if (props.showGradientShadows) {
    console.warn('[FzCustomScrollbar] Could not get scroll element to attach listeners.')
  }
})

onUnmounted(() => {
  if (scrollElement.value) {
    scrollElement.value.removeEventListener('scroll', handleScroll)
  }
})

// Expose the simplebarInstance ref
defineExpose({
  simplebarInstance,
  checkHasScroll,
  hasScroll,
})
</script>

<template>
  <div class="fz-custom-scrollbar" :class="{ 'fz-custom-scrollbar--with-shadows': props.showGradientShadows }">
    <div
      v-if="props.showGradientShadows"
      class="fz-custom-scrollbar__shadow fz-custom-scrollbar__shadow--top"
      :class="{ 'fz-custom-scrollbar__shadow--visible': showTopShadow }"
    />
    <!-- Add horizontal shadow elements -->
    <div
      v-if="props.showGradientShadows"
      class="fz-custom-scrollbar__shadow fz-custom-scrollbar__shadow--left"
      :class="{ 'fz-custom-scrollbar__shadow--visible': showLeftShadow }"
    />
    <simplebar :ref="(el) => simplebarRef = el as any" :auto-hide="false" v-bind="$attrs">
      <slot />
    </simplebar>
    <div
      v-if="props.showGradientShadows"
      class="fz-custom-scrollbar__shadow fz-custom-scrollbar__shadow--right"
      :class="{ 'fz-custom-scrollbar__shadow--visible': showRightShadow }"
    />
    <div
      v-if="props.showGradientShadows"
      class="fz-custom-scrollbar__shadow fz-custom-scrollbar__shadow--bottom"
      :class="{ 'fz-custom-scrollbar__shadow--visible': showBottomShadow }"
    />
  </div>
</template>

<style>
.fz-custom-scrollbar {
  /* Base styling for the wrapper if needed */
  --fz-full-scrollbar-size: 8px;
  --fz-scrollbar-size: calc(var(--fz-full-scrollbar-size) / 2);
  --fz-scrollbar-content-padding-right: 14px;
  --fz-scrollbar-radius: var(--fz-radius-md);
  --fz-scrollbar-background: var(--fz-color-surface-secondary-clicked);
  --fz-scrollbar-hover-background: var(--fz-color-surface-secondary-hover);
  --fz-scrollbar-dragging-background: var(--fz-color-surface-secondary-clicked);
  width: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  min-height: 0;
  max-height: 100%;
}

/*
  SimpleBar CSS Customization:
  SimpleBar generates its own DOM structure. We need to target those specific classes.
  The classes are typically:
  - .simplebar-wrapper
  - .simplebar-height-auto-observer-wrapper
  - .simplebar-mask
  - .simplebar-offset
  - .simplebar-content-wrapper
  - .simplebar-content
  - .simplebar-track
  - .simplebar-scrollbar
  - .simplebar-horizontal, .simplebar-vertical

  We will use FZ theme variables to style them.
*/

.fz-custom-scrollbar [data-simplebar] {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.fz-custom-scrollbar .simplebar-scrollable-y .simplebar-content-wrapper {
  padding-right: var(--fz-scrollbar-content-padding-right);
  scroll-behavior: auto;
}

/* Scrollbar track */
.fz-custom-scrollbar .simplebar-track {
  background-color: var(--fz-color-surface-strong);
  border-radius: var(--fz-radius-full);
}

.fz-custom-scrollbar .simplebar-track.simplebar-vertical {
  width: var(--fz-full-scrollbar-size);
  right: calc(var(--fz-scrollbar-size) / 2);
  visibility: hidden;
  z-index: 2;
}

.fz-custom-scrollbar .simplebar-track.simplebar-horizontal {
  height: var(--fz-full-scrollbar-size);
  bottom: 0; /* Изменено с calc(var(--fz-scrollbar-size) / 2) */
  left: 0;
  right: 0;
  visibility: hidden;
  z-index: 3;
}

.fz-custom-scrollbar .simplebar-track.simplebar-vertical:before {
  content: '';
  display: block;
  width: var(--fz-scrollbar-size);
  right: calc(var(--fz-scrollbar-size) / 2);
  top: 0;
  bottom: 0;
  height: 100%;
  background-color: var(--fz-color-surface-background);
  position: absolute;
  border-radius: var(--fz-radius-sm);
}

.fz-custom-scrollbar .simplebar-track.simplebar-horizontal:before {
  content: '';
  display: block;
  height: var(--fz-scrollbar-size);
  right: 0;
  top: 2px;
  bottom: 0;
  width: 100%;
  background-color: var(--fz-color-surface-background);
  position: absolute;
  border-radius: var(--fz-radius-sm);
}

/* Scrollbar thumb (the draggable part) */
.fz-custom-scrollbar .simplebar-scrollbar {
  z-index: 2;
}

.fz-custom-scrollbar .simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  right: 0;
  border-radius: var(--fz-radius-md);
  position: absolute;
  height: calc(100% - 2px);
  width: var(--fz-scrollbar-size);
  top: 0px;
  left: calc(50% + 1px);
  transform: translateX(-50%);
  z-index: 1;
  background-color: var(--fz-color-surface-secondary-clicked);
  transition: width var(--fz-transition-duration) var(--fz-transition-timing-function);
}

.fz-custom-scrollbar .simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  top: 50%;
  left: 0;
  right: 0;
  width: 100%;
  height: var(--fz-scrollbar-size);
  transform: translateY(-50%);
  background-color: var(--fz-color-surface-secondary-clicked);
  transition: height var(--fz-transition-duration) var(--fz-transition-timing-function);
}

.fz-custom-scrollbar .simplebar-track.simplebar-horizontal .simplebar-scrollbar.simplebar-hover:before,
.fz-custom-scrollbar .simplebar-dragging .simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  height: var(--fz-full-scrollbar-size);
}

.fz-custom-scrollbar .simplebar-track.simplebar-vertical .simplebar-scrollbar.simplebar-hover:before,
.fz-custom-scrollbar .simplebar-dragging .simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  width: var(--fz-full-scrollbar-size);
}

.fz-custom-scrollbar .simplebar-track.simplebar-vertical .simplebar-scrollbar {
  width: var(--fz-scrollbar-size);
  top: 1px;
  bottom: 1px;
  left: 1px; /* Center it */
}

.fz-custom-scrollbar .simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  height: var(--fz-scrollbar-size);
  top: calc(50% - var(--fz-scrollbar-size) / 2); /* Центрирование по вертикали */
  left: 0;
  right: auto;
  min-width: 40px;
}

/* Optional: Hover effect for the thumb */
.fz-custom-scrollbar .simplebar-scrollbar:hover {
  background-color: var(--fz-color-surface-hover);
  opacity: 1;
}

/* Styling for when data-simplebar-auto-hide="false" is set */
.fz-custom-scrollbar [data-simplebar-auto-hide='false'] .simplebar-scrollbar {
  opacity: 0.8;
}

/* Gradient Shadow Styles */
.fz-custom-scrollbar__shadow {
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--fz-transition-duration-fast) var(--fz-transition-timing-function);
  background: linear-gradient(to bottom, var(--fz-color-background, white) 20%, transparent 100%);
}

.fz-custom-scrollbar__shadow--top {
  top: 0;
  background: linear-gradient(to bottom, var(--fz-color-background, white) 20%, transparent 100%);
}

.fz-custom-scrollbar__shadow--bottom {
  bottom: 0;
  background: linear-gradient(to top, var(--fz-color-background, white) 20%, transparent 100%);
}

/* Hide vertical shadows when no vertical scrolling is available */
.fz-custom-scrollbar:not(:has(.simplebar-track.simplebar-vertical .simplebar-scrollbar.simplebar-visible)) .fz-custom-scrollbar__shadow--top,
.fz-custom-scrollbar:not(:has(.simplebar-track.simplebar-vertical .simplebar-scrollbar.simplebar-visible)) .fz-custom-scrollbar__shadow--bottom {
  display: none !important;
}

/* New styles for horizontal shadows */
.fz-custom-scrollbar__shadow--left,
.fz-custom-scrollbar__shadow--right {
  top: 0;
  bottom: 0;
  width: 20px;
  height: auto;
  left: auto;
  right: auto;
}

.fz-custom-scrollbar__shadow--left {
  left: 0;
  background: linear-gradient(to right, var(--fz-color-background, white) 20%, transparent 100%);
}

.fz-custom-scrollbar__shadow--right {
  right: 0;
  background: linear-gradient(to left, var(--fz-color-background, white) 20%, transparent 100%);
}

/* Hide horizontal shadows when no horizontal scrolling is available */
.fz-custom-scrollbar:not(:has(.simplebar-track.simplebar-horizontal .simplebar-scrollbar.simplebar-visible)) .fz-custom-scrollbar__shadow--left,
.fz-custom-scrollbar:not(:has(.simplebar-track.simplebar-horizontal .simplebar-scrollbar.simplebar-visible)) .fz-custom-scrollbar__shadow--right {
  display: none !important;
}

.fz-custom-scrollbar--with-shadows .fz-custom-scrollbar__shadow--visible {
  opacity: 1;
}

/* Adjust simplebar track position if shadows are present and track is on edge */
.fz-custom-scrollbar--with-shadows .simplebar-track.simplebar-vertical {
  height: calc(100% - 7px);
}

.fz-custom-scrollbar--with-shadows .simplebar-track.simplebar-horizontal {
  width: calc(100% - 7px);
}
</style>
