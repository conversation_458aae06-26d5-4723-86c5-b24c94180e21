import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import FzCustomScrollbar from './FzCustomScrollbar.vue'

describe('fzCustomScrollbar', () => {
  it('renders correctly with default slot content', () => {
    const wrapper = mount(FzCustomScrollbar, {
      slots: {
        default: '<div style="height: 200px; width: 200px;">Scrollable content</div>',
      },
    })
    expect(wrapper.html()).toContain('Scrollable content')
    // Check if simplebar classes are present (example)
    expect(wrapper.find('.simplebar-content-wrapper').exists()).toBe(true)
  })

  it('passes attributes to the simplebar component', async () => {
    const wrapper = mount(FzCustomScrollbar, {
      slots: {
        default: '<div>Content</div>',
      },
      attrs: {
        'data-simplebar-auto-hide': 'false',
      },
    })
    // Accessing the simplebar component instance or its root element might be tricky
    // depending on how simplebar-vue renders. We check the attribute on the root
    // of the mounted simplebar component if possible, or on our component's root if $attrs are passed there.
    // In our case, $attrs are bound to the <simplebar> tag inside FzCustomScrollbar
    const simplebarComponent = wrapper.findComponent({ name: 'simplebar' })
    expect(simplebarComponent.attributes('data-simplebar-auto-hide')).toBe('false')
  })

  // Add more tests for:
  // - Emitted events (e.g., scroll)
  // - Different simplebar options and their effects (if testable via DOM attributes/classes)
  // - Styling application (might require visual regression or more complex DOM checks)
})
