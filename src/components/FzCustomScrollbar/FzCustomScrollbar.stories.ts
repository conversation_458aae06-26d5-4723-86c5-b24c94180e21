import type { Meta, StoryObj } from '@storybook/vue3'
import { FzCustomScrollbar } from './index'

const meta: Meta<typeof FzCustomScrollbar> = {
  title: 'Components/FzCustomScrollbar',
  component: FzCustomScrollbar,
  tags: ['autodocs'],
  argTypes: {
    // Define argTypes for props if needed, e.g., SimpleBar options
    // 'data-simplebar-auto-hide': { control: 'boolean', defaultValue: true },
  },
  parameters: {
    layout: 'centered',
  },
}

export default meta
type Story = StoryObj<typeof meta>

const content = Array.from({ length: 50 }, (_, i) => `Line item ${i + 1}`).join('\n')
// Content for horizontal scroll
const horizontalContent = 'This is some very long content that should cause a horizontal scrollbar to appear. '.repeat(20)

export const Default: Story = {
  render: args => ({
    components: { FzCustomScrollbar },
    setup() {
      return { args, content }
    },
    template: `
      <div style="width: 300px; height: 200px">
        <FzCustomScrollbar v-bind="args">
          <pre style="margin: 0; padding: 10px;">{{ content }}</pre>
        </FzCustomScrollbar>
      </div>
    `,
  }),
}

export const HorizontalScroll: Story = {
  render: args => ({
    components: { FzCustomScrollbar },
    setup() {
      return { args }
    },
    template: `
      <div style="width: 300px; height: 100px">
        <FzCustomScrollbar v-bind="args">
          <div style="width: 600px; padding: 10px;">
            This content is very wide and should make a horizontal scrollbar appear.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque nec odio.
          </div>
        </FzCustomScrollbar>
      </div>
    `,
  }),
  args: {
    // 'data-simplebar-auto-hide': false,
  },
}

export const BothScrollbars: Story = {
  render: args => ({
    components: { FzCustomScrollbar },
    setup() {
      return { args }
    },
    template: `
      <div style="width: 300px; height: 200px">
        <FzCustomScrollbar v-bind="args">
          <div style="width: 800px; padding: 10px;">
            <div v-for="i in 30" :key="i">
              Row {{ i }}: This is a long line of text that will cause horizontal scrolling. The repeated content ensures we have both vertical and horizontal scrollbars.
            </div>
          </div>
        </FzCustomScrollbar>
      </div>
    `,
  }),
  args: {
    showGradientShadows: true,
  },
}

export const WithGradientShadows: Story = {
  render: args => ({
    components: { FzCustomScrollbar },
    setup() {
      return { args, content }
    },
    template: `
      <div style="width: 300px; height: 200px; position: relative;">
        <FzCustomScrollbar v-bind="args">
          <pre style="margin: 0; padding: 10px;">{{ content }}</pre>
        </FzCustomScrollbar>
      </div>
    `,
  }),
  args: {
    showGradientShadows: true,
    // You can also pass simplebar options here if needed, e.g.,
    // options: { autoHide: false }
  },
}

// New story for horizontal gradient shadows
export const WithHorizontalGradientShadows: Story = {
  render: args => ({
    components: { FzCustomScrollbar },
    setup() {
      return { args, horizontalContent }
    },
    template: `
      <div style="width: 300px; height: 100px position: relative;">
        <FzCustomScrollbar v-bind="args">
          <div style="width: 800px; padding: 10px;">
            {{ horizontalContent }}
          </div>
        </FzCustomScrollbar>
      </div>
    `,
  }),
  args: {
    showGradientShadows: true,
  },
}
