import type { Meta, StoryObj } from '@storybook/vue3'
import FzLoader from './FzLoader.vue'

const meta: Meta<typeof FzLoader> = {
  title: 'Components/FzLoader',
  component: FzLoader,
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'Size of the loader',
      table: {
        defaultValue: { summary: 'medium' },
      },
    },
  },
  args: {
    size: 'medium',
  },
}

export default meta
type Story = StoryObj<typeof FzLoader>

export const Default: Story = {}

export const Small: Story = {
  args: {
    size: 'small',
  },
}

export const Medium: Story = {
  args: {
    size: 'medium',
  },
}

export const Large: Story = {
  args: {
    size: 'large',
  },
}

export const SizeVariants: Story = {
  render: () => ({
    components: { FzLoader },
    template: `
      <div class="fz:p-4 fz:space-y-4">
        <div class="fz:flex fz:items-center fz:space-x-6">
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzLoader size="small" />
            <span class="fz:mt-2 fz:text-xs">small (16px)</span>
          </div>
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzLoader size="medium" />
            <span class="fz:mt-2 fz:text-xs">medium (30px)</span>
          </div>
          <div class="fz:flex fz:flex-col fz:items-center">
            <FzLoader size="large" />
            <span class="fz:mt-2 fz:text-xs">large (40px)</span>
          </div>
        </div>
      </div>
    `,
  }),
}
