<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  size?: 'small' | 'medium' | 'large'
}>()

const loaderClasses = computed(() => {
  return {
    'fz-loader': true,
    'fz-loader--small': props.size === 'small',
    'fz-loader--medium': props.size === 'medium',
    'fz-loader--large': props.size === 'large',
  }
})
</script>

<template>
  <div :class="loaderClasses">
    <svg class="fz-loader__icon" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M17.8125 2.8125C17.8125 2.05078 17.5195 1.40625 16.9922 0.820312C16.4062 0.292969 15.7617 0 15 0C14.1797 0 13.5352 0.292969 13.0078 0.820312C12.4219 1.40625 12.1875 2.05078 12.1875 2.8125C12.1875 3.63281 12.4219 4.27734 13.0078 4.80469C13.5352 5.39062 14.1797 5.625 15 5.625C15.7617 5.625 16.4062 5.39062 16.9922 4.80469C17.5195 4.27734 17.8125 3.63281 17.8125 2.8125ZM15 24.375C15.7617 24.375 16.4062 24.668 16.9922 25.1953C17.5195 25.7812 17.8125 26.4258 17.8125 27.1875C17.8125 28.0078 17.5195 28.6523 16.9922 29.1797C16.4062 29.7656 15.7617 30 15 30C14.1797 30 13.5352 29.7656 13.0078 29.1797C12.4219 28.6523 12.1875 28.0078 12.1875 27.1875C12.1875 26.4258 12.4219 25.7812 13.0078 25.1953C13.5352 24.668 14.1797 24.375 15 24.375ZM27.1875 12.1875C27.9492 12.1875 28.5938 12.4805 29.1797 13.0078C29.707 13.5938 30 14.2383 30 15C30 15.8203 29.707 16.4648 29.1797 16.9922C28.5938 17.5781 27.9492 17.8125 27.1875 17.8125C26.3672 17.8125 25.7227 17.5781 25.1953 16.9922C24.6094 16.4648 24.375 15.8203 24.375 15C24.375 14.2383 24.6094 13.5938 25.1953 13.0078C25.7227 12.4805 26.3672 12.1875 27.1875 12.1875ZM5.625 15C5.625 15.8203 5.33203 16.4648 4.80469 16.9922C4.21875 17.5781 3.57422 17.8125 2.8125 17.8125C1.99219 17.8125 1.34766 17.5781 0.820312 16.9922C0.234375 16.4648 0 15.8203 0 15C0 14.2383 0.234375 13.5938 0.820312 13.0078C1.34766 12.4805 1.99219 12.1875 2.8125 12.1875C3.57422 12.1875 4.21875 12.4805 4.80469 13.0078C5.33203 13.5938 5.625 14.2383 5.625 15ZM6.38672 20.8008C7.14844 20.8008 7.79297 21.0938 8.37891 21.6211C8.90625 22.207 9.19922 22.8516 9.19922 23.6133C9.19922 24.4336 8.90625 25.0781 8.37891 25.6055C7.79297 26.1914 7.14844 26.4258 6.38672 26.4258C5.56641 26.4258 4.92188 26.1914 4.39453 25.6055C3.80859 25.0781 3.57422 24.4336 3.57422 23.6133C3.57422 22.8516 3.80859 22.207 4.39453 21.6211C4.92188 21.0938 5.56641 20.8008 6.38672 20.8008ZM23.6133 20.8008C24.375 20.8008 25.0195 21.0938 25.6055 21.6211C26.1328 22.207 26.4258 22.8516 26.4258 23.6133C26.4258 24.4336 26.1328 25.0781 25.6055 25.6055C25.0195 26.1914 24.375 26.4258 23.6133 26.4258C22.793 26.4258 22.1484 26.1914 21.6211 25.6055C21.0352 25.0781 20.8008 24.4336 20.8008 23.6133C20.8008 22.8516 21.0352 22.207 21.6211 21.6211C22.1484 21.0938 22.793 20.8008 23.6133 20.8008ZM6.38672 3.57422C7.14844 3.57422 7.79297 3.86719 8.37891 4.39453C8.90625 4.98047 9.19922 5.625 9.19922 6.38672C9.19922 7.20703 8.90625 7.85156 8.37891 8.37891C7.79297 8.96484 7.14844 9.19922 6.38672 9.19922C5.56641 9.19922 4.92188 8.96484 4.39453 8.37891C3.80859 7.85156 3.57422 7.20703 3.57422 6.38672C3.57422 5.625 3.80859 4.98047 4.39453 4.39453C4.92188 3.86719 5.56641 3.57422 6.38672 3.57422Z" fill="currentColor" />
    </svg>
  </div>
</template>

<style>
.fz-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--fz-color-surface-brand-primary);
}

.fz-loader--small {
  width: 16px;
  height: 16px;
}

.fz-loader--medium {
  width: 30px;
  height: 30px;
}

.fz-loader--large {
  width: 40px;
  height: 40px;
}

.fz-loader__icon {
  animation-name: spin;
  animation-direction: normal;
  animation-duration: 2000ms;
  animation-iteration-count: infinite;
  animation-timing-function: steps(8);
  display: block;
  width: 100%;
  height: 100%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
