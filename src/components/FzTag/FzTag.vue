<script setup lang="ts">
import type { AcceptableValue } from 'reka-ui'
import { FzIcon } from '../FzIcon'

const props = withDefaults(
  defineProps<{
    label: string
    disabled?: boolean
    clearable?: boolean
    value: AcceptableValue
  }>(),
  {
    disabled: false,
    clearable: true,
  },
)

const emit = defineEmits<{
  (e: 'delete', value: AcceptableValue, event: Event): void
}>()

function handleDelete(e: Event) {
  emit('delete', props.value, e)
}
</script>

<template>
  <div
    class="fz-tag"
    :class="{
      'fz-tag--disabled': disabled,
    }"
  >
    <slot>
      <span class="fz-tag__label">{{ label }}</span>
    </slot>
    <button
      v-if="clearable && !disabled"
      type="button"
      class="fz-tag__delete"
      aria-label="Remove tag"
      @click="handleDelete"
      @pointerdown="handleDelete"
    >
      <FzIcon name="x-small" size="md" />
    </button>
  </div>
</template>

<style>
.fz-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--fz-spacing-2) var(--fz-spacing-4);
  background-color: var(--fz-color-neutral-18);
  color: var(--fz-color-text-primary);
  border-radius: var(--fz-radius-xs);
  font: var(--fz-font-caption);
  gap: var(--fz-spacing-2);
  line-height: 1;
  min-width: 0;
}

.fz-tag--disabled {
  background-color: var(--fz-color-neutral-19);
  color: var(--fz-color-text-tertiary);
  cursor: not-allowed;
}

.fz-tag__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 0.1em;
  padding-bottom: 0.1em;
}

.fz-tag__delete {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  color: var(--fz-color-border-icon-input);
  border-radius: var(--fz-radius-xs);
  transition: color var(--fz-transition-duration) var(--fz-transition-timing-function);
}

.fz-tag__delete:hover {
  color: var(--fz-color-border-icon-active);
}

.fz-tag__delete:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--fz-color-surface-brand-primary); /* Using a common focus ring color */
}

.fz-tag--disabled .fz-tag__delete {
  display: none; /* Or style as disabled if it should be visible but non-interactive */
}
</style>
