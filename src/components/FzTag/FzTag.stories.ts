import type { Meta, StoryObj } from '@storybook/vue3'
import { action } from '@storybook/addon-actions'
import { FzTag } from '.'

const meta = {
  title: 'Components/FzTag',
  component: FzTag,
  tags: ['autodocs'],
  argTypes: {
    label: { control: 'text' },
    disabled: { control: 'boolean' },
    clearable: { control: 'boolean' },
    onDelete: { action: 'deleted' },
  },
  args: {
    label: 'Example Tag',
    disabled: false,
    clearable: true,
    value: 'example-tag-value',
  },
} satisfies Meta<typeof FzTag>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
  render: args => ({
    components: { FzTag },
    setup() {
      return { args, onDelete: action('delete') }
    },
    template: '<FzTag v-bind="args" @delete="onDelete" />',
  }),
}

export const Disabled: Story = {
  args: {
    label: 'Disabled Tag',
    disabled: true,
    value: 'disabled-tag-value',
  },
  render: args => ({
    components: { FzTag },
    setup() {
      return { args, onDelete: action('delete') }
    },
    template: '<FzTag v-bind="args" @delete="onDelete" />',
  }),
}

export const NotClearable: Story = {
  args: {
    label: 'Not Clearable',
    clearable: false,
    value: 'not-clearable-tag-value',
  },
  render: args => ({
    components: { FzTag },
    setup() {
      return { args, onDelete: action('delete') }
    },
    template: '<FzTag v-bind="args" @delete="onDelete" />',
  }),
}

export const LongLabel: Story = {
  args: {
    label: 'This is a very long tag label that should eventually truncate',
    value: 'long-label-tag-value',
  },
  decorators: [() => ({ template: '<div style="width: 150px;"><story/></div>' })],
  render: args => ({
    components: { FzTag },
    setup() {
      return { args, onDelete: action('delete') }
    },
    template: '<FzTag v-bind="args" @delete="onDelete" />',
  }),
}
