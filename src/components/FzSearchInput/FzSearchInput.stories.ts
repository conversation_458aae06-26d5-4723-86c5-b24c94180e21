import type { Meta, StoryObj } from '@storybook/vue3'

import { ref } from 'vue'
import FzSearchInput from './FzSearchInput.vue'

const meta: Meta<typeof FzSearchInput> = {
  title: 'Components/FzInput/FzSearchInput',
  component: FzSearchInput,
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['outline', 'underline'],
      description: 'Input style variant',
      table: {
        defaultValue: { summary: 'outline' },
      },
    },
    clearable: {
      control: 'boolean',
      description: 'Clear the input if there is a value',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    maxlength: {
      control: 'text',
      description: 'Number of characters allowed',
      table: {
        defaultValue: { summary: '250' },
      },
    },
    placeholder: {
      control: 'text',
      description: 'Input placeholder',
      table: {
        defaultValue: { summary: '' },
      },
    },
  },
  args: {
    modelValue: 'Text',
  },
}

export default meta
type Story = StoryObj<typeof FzSearchInput>

export const Default: Story = {
  args: {
    variant: 'outline',
  },
}

export const Underline: Story = {
  args: {
    variant: 'underline',
  },
}

export const Clearable: Story = {
  args: {
    variant: 'outline',
    clearable: true,
  },
}

export const MaxLength: Story = {
  args: {
    variant: 'outline',
    maxlength: 5,
  },
}

export const Submit: Story = {
  render: args => ({
    components: { FzSearchInput },
    setup() {
      const searchText = ref('')
      const lastAction = ref('')

      const handleSearch = () => {
        lastAction.value = `Submitted: ${searchText.value}`
      }

      return { args, searchText, lastAction, handleSearch }
    },
    template: `
      <div>
        <FzSearchInput 
          v-model="searchText"
          v-bind="args"
          @submit="handleSearch"
        />
        <div style="margin-top: 16px;">
          <ol>
            <li>Type something and press Enter</li>
            <li>Click on search icon</li>
          </ol>
          <p v-if="lastAction" style="color: green;">Last action: {{ lastAction }}</p>
        </div>
      </div>
    `,
  }),
}
