<script setup lang="ts">
import type { InputVariant } from '../FzInput/FzInput.vue'
import { FzIcon } from '../FzIcon'
import { FzInput } from '../FzInput'

withDefaults(
  defineProps<{
    variant?: InputVariant
    clearable?: boolean
    maxlength?: number
    placeholder?: string
  }>(),
  {
    variant: 'outline',
    clearable: false,
    maxlength: 250,
  },
)

const emit = defineEmits<{
  (e: 'submit'): void
}>()

function onSumbit() {
  emit('submit')
}

const model = defineModel<string>()

function onEnter(): void {
  onSumbit()
}

function onSearch() {
  onSumbit()
}
</script>

<template>
  <FzInput
    v-model="model"
    :clearable="clearable"
    :variant="variant"
    :maxlength="maxlength"
    :placeholder="placeholder"
    @enter="onEnter"
  >
    <template #prependIcon>
      <FzIcon
        clickable
        name="search"
        size="md"
        @click="onSearch"
      />
    </template>
  </FzInput>
</template>
