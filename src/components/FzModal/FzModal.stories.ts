import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import { FzButton } from '../FzButton'
import { FzModal } from './index'

const meta: Meta<typeof FzModal> = {
  title: 'Components/FzModal',
  component: FzModal,
  tags: ['autodocs'],
  argTypes: {
    modelValue: {
      control: 'boolean',
      description: 'Controls the visibility of the modal.',
    },
    title: {
      control: 'text',
      description: 'The title of the modal.',
    },
    hideCloseButton: {
      control: 'boolean',
      description: 'Whether to hide the close button.',
    },
    persistent: {
      control: 'boolean',
      description: 'Whether the modal is persistent (cannot be closed by clicking outside or pressing Escape).',
    },
    width: {
      control: 'text',
      description: 'The width of the modal (e.g., "500px", "80%").',
    },
    // Slots
    default: {
      control: 'text',
      description: 'The main content of the modal.',
    },
    footer: {
      control: 'text',
      description: 'Content for the modal footer (e.g., action buttons).',
    },
  },
  args: {
    modelValue: false,
    title: 'Modal Title',
    hideCloseButton: false,
    persistent: false,
    width: '520px',
  },
}

export default meta

type Story = StoryObj<typeof FzModal>

export const Default: Story = {
  render: args => ({
    components: { FzModal, FzButton },
    setup() {
      const isModalOpen = ref(args.modelValue)
      return { args, isModalOpen }
    },
    template: `
      <div>
        <FzButton @click="isModalOpen = true">Открыть модальное окно</FzButton>
        <FzModal v-bind="args" v-model="isModalOpen">
          <div>Текст модального окна</div>
          <template #footer>
            <FzButton variant="ghost" @click="isModalOpen = false">Сбросить</FzButton>
            <FzButton variant="success" @click="isModalOpen = false">Сохранить</FzButton>
          </template>
        </FzModal>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    title: 'Заголовок модального окна в две строки для примера',
  },
}

export const Persistent: Story = {
  render: args => ({
    components: { FzModal, FzButton },
    setup() {
      const isModalOpen = ref(args.modelValue)
      return { args, isModalOpen }
    },
    template: `
      <div>
        <FzButton @click="isModalOpen = true">Открыть постоянное модальное окно</FzButton>
        <FzModal v-bind="args" v-model="isModalOpen">
          <p>Это постоянное модальное окно. Оно не может быть закрыто путем нажатия вне его или нажатия клавиши Escape.</p>
           <template #footer>
            <FzButton variant="ghost" @click="isModalOpen = false">Закрыть</FzButton>
          </template>
        </FzModal>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    title: 'Постоянное модальное окно',
    persistent: true,
    hideCloseButton: true,
  },
}

export const CustomWidth: Story = {
  render: args => ({
    components: { FzModal, FzButton },
    setup() {
      const isModalOpen = ref(args.modelValue)
      return { args, isModalOpen }
    },
    template: `
      <div>
        <FzButton @click="isModalOpen = true">Открыть модальное окно с пользовательской шириной</FzButton>
        <FzModal v-bind="args" v-model="isModalOpen">
          <p>Это модальное окно с пользовательской шириной.</p>
           <template #footer>
            <FzButton variant="secondary" @click="isModalOpen = false">Закрыть</FzButton>
          </template>
        </FzModal>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    title: 'Модальное окно с пользовательской шириной',
    width: '800px',
  },
}
