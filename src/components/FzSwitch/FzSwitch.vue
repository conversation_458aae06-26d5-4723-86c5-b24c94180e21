<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue'
import { FzIcon } from '../FzIcon'
import { FzTooltip } from '../FzTooltip'

const props = withDefaults(defineProps<{
  disabled?: boolean
  label?: string
  tooltip?: string
  id?: string
}>(), {
  disabled: false,
})

const model = defineModel<boolean>()

const fzSwitchClasses = computed(() => [
  'fz-switch-block',
  {
    'fz-switch--disabled': props.disabled,
  },
])

const uid = getCurrentInstance()?.uid
const switchId = computed(() => props.id || `fz-checkbox-${uid}`)
</script>

<template>
  <div :class="fzSwitchClasses">
    <label class="fz-switch">
      <input
        :id="switchId"
        v-model="model"
        :disabled="disabled"
        type="checkbox"
        class="fz-switch-input"
      >
      <span class="fz-switch-slider" />
    </label>
    <label v-if="label" class="fz-switch__text typography-control-text" :for="switchId">
      <span>{{ label }}</span>
      <span v-if="tooltip" class="fz-switch__tooltip">
        <FzTooltip :content="tooltip">
          <FzIcon name="info" size="md" />
        </FzTooltip>
      </span>
    </label>
  </div>
</template>

<style>
.fz-switch-block{
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-6);
}

.fz-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.fz-switch .fz-switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.fz-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: 0 7px 64px 0 #00000012;
  border-radius: 12px;
  background-color: var(--fz-color-surface-secondary);
  transition: var(--fz-transition-duration);
}

.fz-switch-slider:before {
  box-shadow: 0 7px 64px 0 #00000012;
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  border-radius: 50%;
  background-color: var(--fz-color-surface-light);
  transition: var(--fz-transition-duration);
}

.fz-switch-input:checked + .fz-switch-slider {
  background-color: var(--fz-color-surface-brand-primary);
}

.fz-switch-input:checked + .fz-switch-slider:before {
  transform: translateX(20px);
}

.fz-switch--disabled{
  opacity: 50%;
}

.fz-switch__text{
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-2);
  color: var(--fz-color-text-primary);
}

.fz-switch__tooltip{
  color: var(--fz-color-border-icon-input);
}
</style>
