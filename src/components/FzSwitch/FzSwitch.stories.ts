import type { Meta, StoryObj } from '@storybook/vue3'

import FzSwitch from './FzSwitch.vue'

const meta: Meta<typeof FzSwitch> = {
  title: 'Components/FzSwitch',
  component: FzSwitch,
  tags: ['autodocs'],
  argTypes: {
    disabled: {
      control: 'boolean',
      description: 'disabled switch',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    label: {
      control: 'text',
      description: 'text switch',
    },
    tooltip: {
      control: 'text',
      description: 'tooltip text',
    },
    id: {
      control: 'text',
      description: 'switch id',
    },
  },
  args: {
    modelValue: false,
  },
}

export default meta
type Story = StoryObj<typeof FzSwitch>

export const Default: Story = {
  args: {
    modelValue: false,
  },
}

export const On: Story = {
  args: {
    modelValue: true,
  },
}

export const Disabled: Story = {
  args: {
    disabled: true,
  },
}

export const Label: Story = {
  args: {
    modelValue: true,
    label: 'Text',
  },
}

export const Tooltip: Story = {
  args: {
    modelValue: true,
    label: 'Text',
    tooltip: 'Text tooltip',
  },
}
