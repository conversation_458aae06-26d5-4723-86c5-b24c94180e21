import type { Meta, StoryObj } from '@storybook/vue3'
import { maskito } from '@maskito/vue'

import { getCurrentInstance } from 'vue'
import FzMaskedInput from './FzMaskedInput.vue'

const meta: Meta<typeof FzMaskedInput> = {
  title: 'Components/FzInput/FzMaskedInput',
  component: FzMaskedInput,
  tags: ['autodocs'],
  argTypes: {
    mask: {
      control: 'select',
      options: ['phone', 'mail'],
      description: 'Input mask',
      table: {
        defaultValue: { summary: 'phone' },
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof FzMaskedInput>

function setupStory(args: any) {
  const app = getCurrentInstance()?.appContext.app
  app?.directive('maskito', maskito)

  return { args }
}

export const Default: Story = {
  render: (args: any) => ({
    components: { FzMaskedInput },
    setup: () => setupStory(args),
    template: '<FzMaskedInput  v-bind="args" />',
  }),
  args: {
  },
}

export const MaskPhone: Story = {
  render: (args: any) => ({
    components: { FzMaskedInput },
    setup: () => setupStory(args),
    template: '<FzMaskedInput  v-bind="args" />',
  }),
  args: {
    mask: 'phone',
  },
}

export const MaskMail: Story = {
  render: (args: any) => ({
    components: { FzMaskedInput },
    setup: () => setupStory(args),
    template: '<FzMaskedInput  v-bind="args" />',
  }),
  args: {
    mask: 'mail',
  },
}
