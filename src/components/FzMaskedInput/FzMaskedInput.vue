<script setup lang="ts">
/**
 * FzMaskedInput - обёртка над FzInput с применением маски Maskito
 */
import type { MaskitoOptions } from '@maskito/core'
import type { ComponentPublicInstance } from 'vue'
import type { InputVariant } from '../FzInput/FzInput.vue'
import { Maskito } from '@maskito/core'
import {
  maskitoAddOnFocusPlugin,
  maskitoCaretGuard,
  maskitoPrefixPostprocessorGenerator,
  maskitoRemoveOnBlurPlugin,
} from '@maskito/kit'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { FzInput } from '../FzInput'

// Пропсы компонента
const props = withDefaults(
  defineProps<{
    variant?: InputVariant
    disabled?: boolean
    clearable?: boolean
    maxlength?: number
    isInvalid?: boolean
    isSuccess?: boolean
    mask?: InputMask
  }>(),
  {
    variant: 'outline',
    disabled: false,
    clearable: false,
    maxlength: 250,
    isInvalid: false,
    isSuccess: false,
    mask: 'phone',
  },
)

type InputMask = 'phone' | 'mail'

// v-model для значения
const model = defineModel<string>()
// флаг валидации email
const isInvalidEmail = ref(false)

// Генерирует опции Maskito или undefined
/**
 * @returns {MaskitoOptions|undefined} Опции для Maskito
 */
const maskOptions = computed<MaskitoOptions | undefined>(() => {
  return props.mask === 'phone' ? phoneMaskOptions() : undefined
})

// Плейсхолдер под маску
const maskPlaceholder = computed(() =>
  props.mask === 'phone'
    ? '+7 (___) ___ ____'
    : props.mask === 'mail'
      ? '<EMAIL>'
      : '',
)

/**
 * Опции маски телефона для Maskito
 */
function phoneMaskOptions(): MaskitoOptions {
  return {
    plugins: [
      maskitoAddOnFocusPlugin('+7 '),
      maskitoRemoveOnBlurPlugin('+7 '),
      maskitoCaretGuard((value, [from, to]) => [
        from === to ? '+7 '.length : 0,
        value.length,
      ]),
    ],
    postprocessors: [maskitoPrefixPostprocessorGenerator('+7 ')],
    preprocessors: [completePhonePreprocessor()],
    mask: ['+', '7', ' ', '(', /\d/, /\d/, /\d/, ')', ' ', /\d/, /\d/, /\d/, ' ', /\d/, /\d/, /\d/, /\d/],
  }
}

/**
 * Препроцессор для корректировки вставки номера телефона
 */
function completePhonePreprocessor() {
  const trimPrefix = (value: string) => value.replace(/^(\+?7?\s?8?)\s?/, '')
  const countDigits = (value: string) => value.replaceAll(/\D/g, '').length

  return ({ elementState, data }: { elementState: any, data: any }) => {
    const { value, selection } = elementState

    return {
      elementState: {
        selection,
        value: countDigits(value) > 11 ? trimPrefix(value) : value,
      },
      data: countDigits(data) >= 11 ? trimPrefix(data) : data,
    }
  }
}

/**
 * Валидация email по простому regex
 */
function validateEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@][^.@]*\.[^\s@]+$/.test(email)
}

/**
 * Обработчик blur для проверки email
 */
function onBlur() {
  if (props.mask === 'mail') {
    isInvalidEmail.value = !!model.value && !validateEmail(model.value)
  }
}

// Ссылка на FzInput для доступа к DOM input
const inputRef = ref<ComponentPublicInstance | null>(null)
let maskInstance: Maskito | null = null

/**
 * Находит DOM input внутри FzInput
 */
function findInput(): HTMLInputElement | null {
  const comp = inputRef.value
  if (!comp)
    return null
  const el = (comp as any).$el as HTMLElement
  return el.querySelector('input')
}

/**
 * Инициализирует Maskito
 */
function initMask(option: MaskitoOptions) {
  const input = findInput()
  if (input)
    maskInstance = new Maskito(input, option)
}

/**
 * Уничтожает Maskito-инстанс
 */
function destroyMask() {
  maskInstance?.destroy()
  maskInstance = null
}

// Инициализация после монтирования
onMounted(() => {
  nextTick(() => {
    const opt = maskOptions.value
    if (opt)
      initMask(opt)
  })
})

// Пересоздание при изменении опций
watch(maskOptions, (opt) => {
  nextTick(() => {
    destroyMask()
    if (opt)
      initMask(opt)
  })
})

// Очистка при размонтировании
onBeforeUnmount(() => {
  destroyMask()
})
</script>

<template>
  <FzInput
    ref="inputRef"
    v-model="model"
    :variant="variant"
    :disabled="disabled"
    :clearable="clearable"
    :maxlength="maxlength"
    :is-invalid="isInvalid || isInvalidEmail"
    :is-success="isSuccess"
    :placeholder="maskPlaceholder"
    @blur="onBlur"
  />
</template>
