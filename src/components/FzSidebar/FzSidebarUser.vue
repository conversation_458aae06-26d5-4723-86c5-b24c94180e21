<script setup lang="ts">
import { computed } from 'vue'
import FzIcon from '../FzIcon'

interface Props {
  name: string
  role?: string
  avatar?: string
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  role: '',
  avatar: '',
  collapsed: false,
})

const userClasses = computed(() => [
  'fz-sidebar-user',
  {
    'fz-sidebar-user--collapsed': props.collapsed,
  },
])
</script>

<template>
  <div :class="userClasses">
    <div class="fz-sidebar-user__avatar">
      <img
        v-if="avatar"
        :src="avatar"
        :alt="name"
        class="fz-sidebar-user__avatar-img"
      >
      <div v-else class="fz-sidebar-user__avatar-fallback">
        <FzIcon name="user" size="lg" />
      </div>
    </div>
    <div v-if="!collapsed" class="fz-sidebar-user__info">
      <div class="fz-sidebar-user__name">
        {{ name }}
      </div>
      <div v-if="role" class="fz-sidebar-user__role">
        {{ role }}
      </div>
    </div>
  </div>
</template>

<style>
.fz-sidebar-user {
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-4);
  padding: var(--fz-spacing-8) 0 var(--fz-spacing-8) var(--fz-spacing-2);
}

.fz-sidebar-user--collapsed {
  justify-content: center;
  padding-top: var(--fz-spacing-6);
  padding-bottom: var(--fz-spacing-6);
  padding-left: 0;
}

.fz-sidebar-user__avatar {
  flex-shrink: 0;
  width: 30px;
  height: 30px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 0;
  background: var(--fz-color-neutral-18);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fz-sidebar-user__avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.fz-sidebar-user__avatar-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: transparent;
  color: var(--fz-color-neutral-10);
  font-size: var(--fz-font-size-xxs);
  font-weight: var(--fz-font-weight-medium);
  letter-spacing: 0.02em;
}

.fz-sidebar-user__info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.fz-sidebar-user__name {
  font-size: var(--fz-font-size-xxs);
  font-weight: var(--fz-font-weight-medium);
  color: var(--fz-color-brand-12);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.fz-sidebar-user__role {
  font-size: var(--fz-font-size-xxs);
  font-weight: var(--fz-font-weight-regular);
  color: var(--fz-color-neutral-10);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}
</style>
