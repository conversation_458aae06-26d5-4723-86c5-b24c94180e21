import type { Meta, StoryObj } from '@storybook/vue3'
import type { SidebarItem } from './index'
import { ref } from 'vue'
import { FzSidebar } from './index'

const sidebarItems: SidebarItem[] = [
  { id: 'companies', icon: 'companies', label: 'Компании' },
  { id: 'users', icon: 'users', label: 'Пользователи' },
  { id: 'coupons', icon: 'coupons', label: 'Купоны' },
  { id: 'reports', icon: 'reports', label: 'Отчеты' },
  { id: 'jur', icon: 'jur', label: 'Юр. лица' },
]

const meta = {
  title: 'Components/FzSidebar',
  component: FzSidebar,
  tags: ['autodocs'],
  argTypes: {
    items: {
      control: 'object',
      description: 'Array of sidebar items',
    },
    activeItemId: {
      control: 'text',
      description: 'ID of the active item',
    },
    collapsed: {
      control: 'boolean',
      description: 'Whether the sidebar is collapsed',
    },
    title: {
      control: 'text',
      description: 'Sidebar title',
    },
  },
  args: {
    items: sidebarItems,
    activeItemId: 'polls',
    collapsed: false,
    title: 'ФОКУЗ',
    userAvatar: '', // Example image URL
    // Default story uses default userName, userRole, and no userAvatar
  },
} satisfies Meta<typeof FzSidebar>

export default meta
type Story = StoryObj<typeof meta>

export const Superadmin: Story = {
  args: {
    userAvatar: '',
    userName: 'Superadmin',
    userRole: 'Суперадминистратор',
    items: sidebarItems,
    activeItemId: 'polls',
    collapsed: false,
    title: 'ФОКУЗ',
  },
  render: args => ({
    components: { FzSidebar },
    setup() {
      const activeItem = ref(args.activeItemId || 'polls')
      const collapsed = ref(args.collapsed)
      function handleItemClick(itemId: string) {
        activeItem.value = itemId
      }
      return {
        args,
        activeItem,
        collapsed,
        handleItemClick,
      }
    },
    template: `
      <div class="fz:h-screen fz:flex fz:bg-surface-background">
        <FzSidebar
          :items="args.items"
          :active-item-id="activeItem"
          v-model:collapsed="collapsed"
          :title="args.title"
          :user-name="args.userName"
          :user-role="args.userRole"
          :user-avatar="args.userAvatar"
          @item-click="handleItemClick"
        />
        <div class="fz:p-4">
          <p>Active item: {{ activeItem }}</p>
          <p>Collapsed: {{ collapsed }}</p>
          <p>User Name: {{ args.userName }}</p>
          <p>User Role: {{ args.userRole }}</p>
          <p>User Avatar: {{ args.userAvatar }}</p>
        </div>
      </div>
    `,
  }),
}

export const Admin: Story = {
  args: {
    userAvatar: 'https://i.pravatar.cc/80?img=2',
    userName: 'Админ',
    userRole: 'Администратор',
    items: [
      { id: 'users', icon: 'users', label: 'Пользователи' },
      { id: 'coupons', icon: 'coupons', label: 'Купоны' },
      { id: 'reports', icon: 'reports', label: 'Отчеты' },
    ],
    activeItemId: 'users',
    collapsed: false,
    title: 'ФОКУЗ',
  },
  render: args => ({
    components: { FzSidebar },
    setup() {
      const activeItem = ref(args.activeItemId || 'users')
      const collapsed = ref(args.collapsed)
      function handleItemClick(itemId: string) {
        activeItem.value = itemId
      }
      return {
        args,
        activeItem,
        collapsed,
        handleItemClick,
      }
    },
    template: `
      <div class="fz:h-screen fz:flex fz:bg-surface-background">
        <FzSidebar
          :items="args.items"
          :active-item-id="activeItem"
          v-model:collapsed="collapsed"
          :title="args.title"
          :user-name="args.userName"
          :user-role="args.userRole"
          :user-avatar="args.userAvatar"
          @item-click="handleItemClick"
        />
        <div class="fz:p-4">
          <p>Active item: {{ activeItem }}</p>
          <p>Collapsed: {{ collapsed }}</p>
          <p>User Name: {{ args.userName }}</p>
          <p>User Role: {{ args.userRole }}</p>
          <p>User Avatar: {{ args.userAvatar }}</p>
        </div>
      </div>
    `,
  }),
}
