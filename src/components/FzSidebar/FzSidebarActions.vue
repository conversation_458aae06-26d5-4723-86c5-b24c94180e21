<script setup lang="ts">
import { computed } from 'vue'
import { FzButton } from '../FzButton'
import { FzTooltip } from '../FzTooltip'

interface Props {
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
})

const emit = defineEmits<{
  (e: 'chatClick'): void
  (e: 'helpClick'): void
}>()

const actionsClasses = computed(() => [
  'fz-sidebar-actions',
  {
    'fz-sidebar-actions--collapsed': props.collapsed,
  },
])
</script>

<template>
  <div :class="actionsClasses">
    <FzTooltip content="Задать вопрос оператору" align="start">
      <FzButton
        icon="answers"
        variant="primary"
        :icon-only="true"
        @click="emit('chatClick')"
      >
        Чат
      </FzButton>
    </FzTooltip>
    <FzTooltip content="Помощь" align="start">
      <FzButton
        icon="help-action"
        variant="secondary"
        :icon-only="true"
        @click="emit('helpClick')"
      >
        Помощь
      </FzButton>
    </FzTooltip>
  </div>
</template>

<style>
.fz-sidebar-actions {
  display: flex;
  gap: var(--fz-spacing-6);
}

.fz-sidebar-actions--collapsed {
  flex-direction: column;
  align-items: center;
}
</style>
