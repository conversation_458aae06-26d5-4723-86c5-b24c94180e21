<script setup lang="ts">
import { computed } from 'vue'
import { FzIcon } from '../FzIcon'

interface Props {
  icon: string // Icon name (e.g., 'polls', 'reports')
  label: string // Menu item text
  active?: boolean // Whether the item is active
  collapsed?: boolean // Whether the parent sidebar is collapsed
}

const props = withDefaults(defineProps<Props>(), {
  active: false,
  collapsed: false,
})

const emit = defineEmits<{
  (e: 'click'): void
}>()

const itemClasses = computed(() => [
  'fz-sidebar-item',
  {
    'fz-sidebar-item--active': props.active,
    'fz-sidebar-item--collapsed': props.collapsed,
  },
])
</script>

<template>
  <button
    :class="itemClasses"
    type="button"
    @click="emit('click')"
  >
    <span class="fz-sidebar-item__icon">
      <FzIcon :name="icon" size="lg" />
    </span>
    <span class="fz-sidebar-item__label">
      {{ label }}
    </span>
  </button>
</template>

<style>
.fz-sidebar-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--fz-spacing-3) var(--fz-spacing-3);
  border: none;
  background: transparent;
  cursor: pointer;
  text-align: left;
  transition: background-color var(--fz-transition-duration), color var(--fz-transition-duration), opacity var(--fz-transition-duration);
  color: var(--fz-color-text-secondary);
  border-radius: var(--fz-radius-xs);
  min-height: 36px;
}

.fz-sidebar-item:not(.fz-sidebar-item--active):hover {
  color: var(--fz-color-text-primary);
}

.fz-sidebar-item--active {
  background-color: var(--fz-color-surface-light);
  color: var(--fz-color-text-primary);
}

.fz-sidebar-item__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.fz-sidebar-item--collapsed .fz-sidebar-item__icon {
  /* margin-right: 0; */
}

.fz-sidebar-item__label {
  font: var(--fz-font-button-medium);
  white-space: nowrap;
  transition: opacity var(--fz-transition-duration);
}

.fz-sidebar-item--collapsed .fz-sidebar-item__label {
  opacity: 0;
}
</style>
