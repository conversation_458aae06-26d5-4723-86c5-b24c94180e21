<script setup lang="ts">
import type { DropdownItem } from '../FzDropdownMenu/FzDropdownMenu.vue'
import { computed } from 'vue'
import { FzButton } from '../FzButton'
import FzDropdownMenu from '../FzDropdownMenu/FzDropdownMenu.vue'
import { FzLogo } from '../FzLogo'
import FzSidebarActions from './FzSidebarActions.vue'
import FzSidebarItem from './FzSidebarItem.vue'
import FzSidebarUser from './FzSidebarUser.vue'

export interface SidebarItem {
  id: string
  icon: string
  label: string
}

export interface Props {
  items: SidebarItem[]
  activeItemId?: string
  collapsed?: boolean
  title?: string
  userName?: string
  userRole?: string
  userAvatar?: string
}

const props = withDefaults(defineProps<Props>(), {
  activeItemId: '',
  collapsed: false,
  title: 'ФОКУЗ',
  userName: 'Анатолий Александров',
  userRole: 'Администратор',
  userAvatar: '',
})

const emit = defineEmits<{
  (e: 'update:collapsed', value: boolean): void
  (e: 'itemClick', itemId: string): void
  (e: 'helpClick'): void
  (e: 'chatClick'): void
  (e: 'logout'): void
}>()

const isCollapsed = computed({
  get: () => props.collapsed,
  set: value => emit('update:collapsed', value),
})

function toggleCollapsed() {
  isCollapsed.value = !isCollapsed.value
}

function handleItemClick(itemId: string) {
  emit('itemClick', itemId)
}

function handleHelpClick() {
  emit('helpClick')
}

function handleChatClick() {
  emit('chatClick')
}

const userMenuItems: DropdownItem[] = [
  { label: 'Выход', value: 'logout', icon: 'leftmenu' },
]

function handleUserMenuSelect(itemValue: any) {
  if (itemValue === 'logout') {
    emit('logout')
  }
}

const sidebarClasses = computed(() => [
  'fz-sidebar',
  {
    'fz-sidebar--collapsed': isCollapsed.value,
  },
])

const collapseIcon = computed(() => isCollapsed.value ? 'burgermenu' : 'arrowleft')
</script>

<template>
  <aside :class="sidebarClasses">
    <div class="fz-sidebar__header fz:pl-2 fz:-mb-4">
      <FzLogo
        :text="title"
        class="fz-sidebar__logo"
        :class="{ 'fz-sidebar__logo--collapsed': isCollapsed }"
      />
      <FzButton
        :icon="collapseIcon"
        variant="link"
        icon-only
        class="fz-sidebar__toggle"
        @click="toggleCollapsed"
      />
    </div>

    <div class="fz-sidebar__content">
      <nav class="fz-sidebar__nav">
        <FzSidebarItem
          v-for="item in items"
          :key="item.id"
          :icon="item.icon"
          :label="item.label"
          :active="item.id === activeItemId"
          :collapsed="isCollapsed"
          @click="handleItemClick(item.id)"
        />
      </nav>
    </div>

    <div class="fz-sidebar__footer">
      <div class="fz:border-t-1 fz:border-b-1 fz:border-t-border-divider fz:border-b-border-divider">
        <FzDropdownMenu
          :items="userMenuItems"
          :as-child="true"
          side="bottom"
          align="start"
          content-class="fz-sidebar__user-dropdown-content"
          :show-arrow="false"
          @select="handleUserMenuSelect"
        >
          <button type="button" class="fz-sidebar__user-trigger">
            <FzSidebarUser
              :name="userName"
              :role="userRole"
              :avatar="userAvatar"
              :collapsed="isCollapsed"
            />
          </button>
        </FzDropdownMenu>
      </div>

      <div class="fz-sidebar__actions-container">
        <FzSidebarActions
          :collapsed="isCollapsed"
          @help-click="handleHelpClick"
          @chat-click="handleChatClick"
        />
      </div>
    </div>
  </aside>
</template>

<style>
.fz-sidebar {
  display: flex;
  flex-direction: column;
  width: 188px;
  box-sizing: border-box;
  height: 100%;
  background-color: var(--fz-color-surface-primary);
  border-right: 1px solid var(--fz-color-border-light);
  transition: width 0.3s;
  padding-left: var(--fz-spacing-8);
  padding-right: var(--fz-spacing-8);
  padding-top: var(--fz-spacing-8);
  padding-bottom: var(--fz-spacing-8);
  gap: var(--fz-spacing-8);
  overflow: hidden;
}

.fz-sidebar--collapsed {
  width: 68px;
}

.fz-sidebar--collapsed {
  gap: var(--fz-spacing-6);
}

.fz-sidebar__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 36px;
  border-bottom: 1px solid var(--fz-color-border-light);
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.fz-sidebar--collapsed .fz-sidebar__header {
  justify-content: center;
  padding: 0;
}

.fz-sidebar__logo {
  flex: 0 0 auto;
  transition: opacity 0.1s var(--fz-transition-timing-function);
  opacity: 1;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.fz-sidebar__logo--collapsed {
  opacity: 0;
}

.fz-sidebar__toggle {
  min-width: 32px;
  height: 32px;
  margin-left: auto;
}

.fz-sidebar__content {
  gap: var(--fz-spacing-8);
}

.fz-sidebar--collapsed .fz-sidebar__content {
  gap: var(--fz-spacing-6);
}

.fz-sidebar__nav {
  display: flex;
  flex-direction: column;
  gap: var(--fz-spacing-2);
}

.fz-sidebar__footer {
  display: flex;
  flex-direction: column;
  border-top: 1px solid var(--fz-color-border-light);
  gap: var(--fz-spacing-8);
}

.fz-sidebar--collapsed .fz-sidebar__footer {
  gap: var(--fz-spacing-6);
}

.fz-sidebar__user-trigger {
  all: unset;
  cursor: pointer;
  transition: opacity 0.3s var(--fz-transition-timing-function);
  width: 100%;
  display: block;
  transform: translateZ(0);
}

.fz-sidebar__user-trigger:hover {
  opacity: 0.8;
}

.fz-sidebar__user-trigger:focus-visible {
  outline: solid var(--fz-color-surface-brand-primary) 2px;
}

.fz-sidebar__actions-container {
  padding-left: var(--fz-spacing-2);
}

.fz-sidebar--collapsed .fz-sidebar__actions-container {
  padding-left: 0;
}
</style>
