<script setup lang="ts">
import { computed } from 'vue'

type FadeUpTransitionName = 'fade-up' | 'fade-up--small' | 'fade-up--large' | 'fade' | 'scale-fade'

const props = withDefaults(
  defineProps<{
    name?: FadeUpTransitionName // We'll add more transitions later
    duration?: number // Duration in ms
    mode?: 'in-out' | 'out-in'
  }>(),
  {
    name: 'fade-up',
    duration: 300,
    mode: undefined,
  },
)

const transitionName = computed(() => {
  // For fade-up variants, use the base name
  if (props.name.startsWith('fade-up')) {
    return 'fz-transition-fade-up'
  }
  return `fz-transition-${props.name}`
})

const transitionStyle = computed(() => {
  // Set default values
  const style = {
    '--fz-transition-duration': props.duration ? `${props.duration}ms` : '300ms',
    '--fz-transition-y-distance': '10px',
  }

  // Adjust values based on transition name
  if (props.name === 'fade-up--small') {
    style['--fz-transition-y-distance'] = '4px'
  }
  else if (props.name === 'fade-up--large') {
    style['--fz-transition-y-distance'] = '20px'
  }
  else if (props.name === 'fade') {
    style['--fz-transition-y-distance'] = '0px'
  }

  return style
})
</script>

<template>
  <Transition
    :name="transitionName"
    :mode="mode"
    :css="true"
    :style="transitionStyle"
  >
    <slot />
  </Transition>
</template>

<style>
/* Base transition styles */
.fz-transition-fade-up-enter-active,
.fz-transition-fade-up-leave-active,
.fz-transition-fade-enter-active,
.fz-transition-fade-leave-active {
  transition-property: opacity, transform;
  transition-duration: var(--fz-transition-duration, 300ms);
  transition-timing-function: var(--fz-transition-timing-function, ease-in-out);
}

/* Fade-up transition */
.fz-transition-fade-up-enter-from,
.fz-transition-fade-up-leave-to {
  opacity: 0;
  transform: translateY(var(--fz-transition-y-distance, 10px));
}

/* Fade transition */
.fz-transition-fade-enter-from,
.fz-transition-fade-leave-to {
  opacity: 0;
}

/* Scale-fade transition*/
.fz-transition-scale-fade-enter-active,
.fz-transition-scale-fade-leave-active {
  transition: all var(--fz-transition-duration) var(--fz-transition-timing-function-bounce);
}

.fz-transition-scale-fade-enter-from,
.fz-transition-scale-fade-leave-to {
  opacity: 0;
  transform: scale(0.3);
}

.fz-transition-scale-fade-enter-to,
.fz-transition-scale-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}
</style>
