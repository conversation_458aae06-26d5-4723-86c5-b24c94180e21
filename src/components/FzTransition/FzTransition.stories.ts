import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import { FzButton } from '../FzButton'
import FzTransition from './FzTransition.vue'

const meta: Meta<typeof FzTransition> = {
  title: 'Components/FzTransition',
  component: FzTransition,
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'select',
      options: ['fade-up', 'fade', 'fade-up--small', 'fade-up--large'],
      description: 'Predefined transition name',
      table: {
        defaultValue: { summary: 'fade-up' },
      },
    },
    duration: {
      control: 'number',
      description: 'Transition duration in milliseconds',
      table: {
        defaultValue: { summary: '300' },
      },
    },
    mode: {
      control: 'select',
      options: ['in-out', 'out-in', undefined],
      description: 'Transition mode',
    },
  },
  args: {
    name: 'fade-up',
    duration: 300,
  },
}

export default meta
type Story = StoryObj<typeof FzTransition>

export const FadeUp: Story = {
  render: args => ({
    components: { FzTransition, FzButton },
    setup() {
      const show = ref(true)
      const toggleShow = () => {
        show.value = !show.value
      }
      return { show, toggleShow, args }
    },
    template: `
      <div class="fz:p-4">
        <FzButton @click="toggleShow" class="fz:mb-4">Toggle Element</FzButton>
        <div class="fz:border fz:rounded fz:p-4">
          <FzTransition v-bind="args">
            <div v-if="show" class="fz:p-4 fz:bg-gray-100 fz:rounded">
              <p>This content will fade up when entering and leaving</p>
            </div>
          </FzTransition>
        </div>
      </div>
    `,
  }),
}

export const Fade: Story = {
  args: {
    name: 'fade',
  },
  render: args => ({
    components: { FzTransition, FzButton },
    setup() {
      const show = ref(true)
      const toggleShow = () => {
        show.value = !show.value
      }
      return { show, toggleShow, args }
    },
    template: `
      <div class="fz:p-4">
        <FzButton @click="toggleShow" class="fz:mb-4">Toggle Element</FzButton>
        <div class="fz:border fz:rounded fz:p-4">
          <FzTransition v-bind="args">
            <div v-if="show" class="fz:p-4 fz:bg-gray-100 fz:rounded">
              <p>This content will fade when entering and leaving</p>
            </div>
          </FzTransition>
        </div>
      </div>
    `,
  }),
}

export const FadeUpSmall: Story = {
  args: {
    name: 'fade-up--small',
  },
  render: args => ({
    components: { FzTransition, FzButton },
    setup() {
      const show = ref(true)
      const toggleShow = () => {
        show.value = !show.value
      }
      return { show, toggleShow, args }
    },
    template: `
      <div class="fz:p-4">
        <FzButton @click="toggleShow" class="fz:mb-4">Toggle Element</FzButton>
        <div class="fz:border fz:rounded fz:p-4">
          <FzTransition v-bind="args">
            <div v-if="show" class="fz:p-4 fz:bg-gray-100 fz:rounded">
              <p>This content will fade up with a small displacement</p>
            </div>
          </FzTransition>
        </div>
      </div>
    `,
  }),
}

export const FadeUpLarge: Story = {
  args: {
    name: 'fade-up--large',
  },
  render: args => ({
    components: { FzTransition, FzButton },
    setup() {
      const show = ref(true)
      const toggleShow = () => {
        show.value = !show.value
      }
      return { show, toggleShow, args }
    },
    template: `
      <div class="fz:p-4">
        <FzButton @click="toggleShow" class="fz:mb-4">Toggle Element</FzButton>
        <div class="fz:border fz:rounded fz:p-4">
          <FzTransition v-bind="args">
            <div v-if="show" class="fz:p-4 fz:bg-gray-100 fz:rounded">
              <p>This content will fade up with a large displacement</p>
            </div>
          </FzTransition>
        </div>
      </div>
    `,
  }),
}

export const TransitionGallery: Story = {
  render: () => ({
    components: { FzTransition, FzButton },
    setup() {
      const shows = ref({
        'fade-up': true,
        'fade': true,
        'fade-up--small': true,
        'fade-up--large': true,
      })

      const toggleShow = (name: keyof typeof shows.value) => {
        shows.value[name] = !shows.value[name]
      }

      return { shows, toggleShow }
    },
    template: `
      <div class="fz:p-4 fz:space-y-8">
        <div class="fz:space-y-2">
          <h3 class="fz:font-medium">fade-up (default)</h3>
          <FzButton @click="toggleShow('fade-up')" class="fz:mb-2">Toggle</FzButton>
          <div class="fz:border fz:rounded fz:p-4">
            <FzTransition name="fade-up">
              <div v-if="shows['fade-up']" class="fz:p-4 fz:bg-gray-100 fz:rounded">
                <p>Standard fade up (10px)</p>
              </div>
            </FzTransition>
          </div>
        </div>
        
        <div class="fz:space-y-2">
          <h3 class="fz:font-medium">fade</h3>
          <FzButton @click="toggleShow('fade')" class="fz:mb-2">Toggle</FzButton>
          <div class="fz:border fz:rounded fz:p-4">
            <FzTransition name="fade">
              <div v-if="shows['fade']" class="fz:p-4 fz:bg-gray-100 fz:rounded">
                <p>Fade only (no movement)</p>
              </div>
            </FzTransition>
          </div>
        </div>
        
        <div class="fz:space-y-2">
          <h3 class="fz:font-medium">fade-up--small</h3>
          <FzButton @click="toggleShow('fade-up--small')" class="fz:mb-2">Toggle</FzButton>
          <div class="fz:border fz:rounded fz:p-4">
            <FzTransition name="fade-up--small">
              <div v-if="shows['fade-up--small']" class="fz:p-4 fz:bg-gray-100 fz:rounded">
                <p>Small fade up (4px)</p>
              </div>
            </FzTransition>
          </div>
        </div>
        
        <div class="fz:space-y-2">
          <h3 class="fz:font-medium">fade-up--large</h3>
          <FzButton @click="toggleShow('fade-up--large')" class="fz:mb-2">Toggle</FzButton>
          <div class="fz:border fz:rounded fz:p-4">
            <FzTransition name="fade-up--large">
              <div v-if="shows['fade-up--large']" class="fz:p-4 fz:bg-gray-100 fz:rounded">
                <p>Large fade up (20px)</p>
              </div>
            </FzTransition>
          </div>
        </div>
      </div>
    `,
  }),
}
