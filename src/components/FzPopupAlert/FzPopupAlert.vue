<script setup lang="ts">
import { computed, onUnmounted, ref, watch } from 'vue'
import { FzIcon } from '../FzIcon'
import { FzTransition } from '../FzTransition'

const props = withDefaults(defineProps<{
  isInvalid?: boolean
  textInvalid?: string
  isSuccess?: boolean
  textSuccess?: string
  timeOut?: number
}>(), {
  isInvalid: false,
  isSuccess: false,
  textInvalid: 'Ошибка',
  textSuccess: 'Успешно',
  timeOut: 3,
})

type Timeout = ReturnType<typeof setTimeout>

const model = defineModel<boolean>({ default: false })
const timeoutId = ref<Timeout | null>(null)

const statusMessageClass = computed(() => [
  'fz-popup-action',
  {
    'fz-popup-action--success': props.isSuccess,
    'fz-popup-action--invalid': props.isInvalid,
  },
])

function closeModal() {
  model.value = false
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
    timeoutId.value = null
  }
}

function startTimeout() {
  if (props.timeOut > 0) {
    timeoutId.value = setTimeout(() => {
      closeModal()
    }, props.timeOut * 1000)
  }
}

watch(model, (newValue) => {
  if (newValue) {
    startTimeout()
  }
  else if (timeoutId.value) {
    clearTimeout(timeoutId.value)
    timeoutId.value = null
  }
})

onUnmounted(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value)
  }
})
</script>

<template>
  <teleport to="body">
    <FzTransition name="fade">
      <div v-if="model && (isInvalid || isSuccess)" :class="statusMessageClass">
        <div v-if="isInvalid || isSuccess" class="fz-popup-action__block">
          <span v-if="isInvalid">{{ textInvalid }}</span>
          <span v-if="isSuccess">{{ textSuccess }}</span>
        </div>
        <span class="fz-popup-action__icon" @click="closeModal">
          <FzIcon name="x" />
        </span>
      </div>
    </FzTransition>
  </teleport>
</template>

<style>
.fz-popup-action{
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--fz-spacing-8);
  font: var(--fz-font-modal-text);
  border-radius: var(--fz-radius-md);
  box-shadow: var(-fz--shadow-floating);
  padding: var(--fz-spacing-12) var(--fz-spacing-8) var(--fz-spacing-12) var(--fz-spacing-12);
  max-width: 300px;
}

.fz-popup-action__block{
  max-width: 228px;
}

.fz-popup-action__icon{
  cursor: pointer;
  color: var(--fz-color-border-icon);
}

.fz-popup-action--success{
  background: var(--fz-color-surface-success-primary);
  color: var(--fz-color-text-success);
}

.fz-popup-action--invalid{
  background: var(--fz-color-surface-critical-primary);
  color: var(--fz-color-text-critical);
}
</style>
