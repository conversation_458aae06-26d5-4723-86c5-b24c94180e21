import type { Meta, StoryObj } from '@storybook/vue3'

import { ref } from 'vue'
import { FzButton } from '../FzButton'
import FzPopupAlert from './FzPopupAlert.vue'

const meta: Meta<typeof FzPopupAlert> = {
  title: 'Components/FzPopupAlert',
  component: FzPopupAlert,
  tags: ['autodocs'],
  argTypes: {
    isInvalid: {
      control: 'boolean',
      description: 'visible invalid pop-up',
    },
    isSuccess: {
      control: 'boolean',
      description: 'visible success pop-up',
    },
    textInvalid: {
      control: 'text',
      description: 'Text invalid',
    },
    textSuccess: {
      control: 'text',
      description: 'Text success',
    },
    timeOut: {
      control: 'number',
      description: 'How long to hide',
    },
  },
  args: {
  },
}

export default meta
type Story = StoryObj<typeof FzPopupAlert>

export const Default: Story = {
  render: args => ({
    components: { FzPopupAlert, FzButton },
    setup() {
      const isPopUpOpen = ref(args.modelValue)
      return { args, isPopUpOpen }
    },
    template: `
      <div>
        <FzButton @click="isPopUpOpen = true">Открыть pop-up</FzButton>
        <FzPopupAlert v-bind="args" v-model="isPopUpOpen"/>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    isSuccess: true,
  },
}

export const isInvalid: Story = {
  render: args => ({
    components: { FzPopupAlert, FzButton },
    setup() {
      const isPopUpOpen = ref(args.modelValue)
      return { args, isPopUpOpen }
    },
    template: `
      <div>
        <FzButton @click="isPopUpOpen = true">Открыть pop-up</FzButton>
        <FzPopupAlert v-bind="args" v-model="isPopUpOpen"/>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    isInvalid: true,
  },
}

export const textSuccess: Story = {
  render: args => ({
    components: { FzPopupAlert, FzButton },
    setup() {
      const isPopUpOpen = ref(args.modelValue)
      return { args, isPopUpOpen }
    },
    template: `
      <div>
        <FzButton @click="isPopUpOpen = true">Открыть pop-up</FzButton>
        <FzPopupAlert v-bind="args" v-model="isPopUpOpen"/>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    isSuccess: true,
    textSuccess: 'Текст уведомления об успешной операции',
  },
}

export const textInvalid: Story = {
  render: args => ({
    components: { FzPopupAlert, FzButton },
    setup() {
      const isPopUpOpen = ref(args.modelValue)
      return { args, isPopUpOpen }
    },
    template: `
      <div>
        <FzButton @click="isPopUpOpen = true">Открыть pop-up</FzButton>
        <FzPopupAlert v-bind="args" v-model="isPopUpOpen"/>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    isInvalid: true,
    textInvalid: 'Текст уведомления для ошибки',
  },
}

export const timeOut: Story = {
  render: args => ({
    components: { FzPopupAlert, FzButton },
    setup() {
      const isPopUpOpen = ref(args.modelValue)
      return { args, isPopUpOpen }
    },
    template: `
      <div>
        <FzButton @click="isPopUpOpen = true">Открыть pop-up</FzButton>
        <FzPopupAlert v-bind="args" v-model="isPopUpOpen"/>
      </div>
    `,
  }),
  args: {
    modelValue: false,
    isSuccess: true,
    timeOut: 1,
  },
}
