import type { Meta, StoryObj } from '@storybook/vue3'
import { ref } from 'vue'
import { getIconNames } from '../../util/helpers'
import FzButton from './FzButton.vue'

// Get all available icons
const iconNames = getIconNames()

const meta: Meta<typeof FzButton> = {
  title: 'Components/FzButton',
  component: FzButton,
  tags: ['autodocs'],
  argTypes: {
    default: {
      control: 'text',
      description: 'Button text',
      table: {
        defaultValue: { summary: 'Click me' },
      },
    },
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'critical', 'success', 'ghost', 'link'],
      description: 'Button style variant',
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    disabled: {
      control: 'boolean',
      description: 'Disables the button',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    icon: {
      control: 'select',
      options: ['', ...iconNames],
      description: 'Icon name to display (without .svg extension)',
    },
    iconPosition: {
      control: 'radio',
      options: ['left', 'right'],
      description: 'Position of the icon relative to button text',
      table: {
        defaultValue: { summary: 'left' },
      },
    },
    iconClass: {
      control: 'object',
      description: 'CSS classes to apply to the icon container',
    },
    iconOnly: {
      control: 'boolean',
      description: 'Whether the button is an icon only button',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    loading: {
      control: 'boolean',
      description: 'Shows a loader and disables the button',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'Size of the button',
      table: {
        defaultValue: { summary: 'medium' },
      },
    },
  },
  args: {
    default: 'Создать рассылку',
  },
}

export default meta
type Story = StoryObj<typeof FzButton>

export const Default: Story = {
  args: {
    icon: '',
  },
}

export const Secondary: Story = {
  args: {
    variant: 'secondary',
  },
}

export const WithIcon: Story = {
  args: {
    icon: 'plus',
  },
}

export const IconRight: Story = {
  args: {
    icon: 'plus',
    iconPosition: 'right',
    variant: 'secondary',
    default: 'Опубликовать',
  },
}

export const IconOnly: Story = {
  args: {
    icon: 'upload',
    iconOnly: true,
  },
}

export const IconOnlySuccess: Story = {
  args: {
    icon: 'apply',
    iconOnly: true,
    variant: 'success',
  },
}

export const Disabled: Story = {
  args: {
    disabled: true,
  },
}

export const CriticalButton: Story = {
  args: {
    variant: 'critical',
    default: 'Удалить',
    icon: 'x',
  },
}

export const SuccessButton: Story = {
  args: {
    variant: 'success',
    default: 'Сохранить',
    icon: 'apply',
  },
}

// Additional icon examples
export const SearchButton: Story = {
  args: {
    default: 'Поиск',
    icon: 'search',
  },
}

export const AddButton: Story = {
  args: {
    default: 'Добавить',
    icon: 'plus',
  },
}

export const EditButton: Story = {
  args: {
    variant: 'secondary',
    default: 'Редактировать',
    icon: 'edit',
  },
}

export const GhostButton: Story = {
  args: {
    variant: 'ghost',
    default: 'Ghost',
  },
}

export const LinkButton: Story = {
  args: {
    variant: 'link',
    default: 'Link Button',
  },
}

export const LinkButtonWithIcon: Story = {
  args: {
    variant: 'link',
    default: 'Link With Icon',
    icon: 'link',
  },
}

export const IconGallery: Story = {
  render: args => ({
    components: { FzButton },
    setup() {
      return {
        iconNames,
        args,
      }
    },
    template: `
      <div class="fz:grid fz:grid-cols-5 fz:gap-4">
        <div 
          v-for="icon in iconNames" 
          :key="icon" 
          class="fz:flex fz:flex-col fz:items-center fz:gap-1"
        >
          <FzButton v-bind="args" :icon="icon" iconOnly />
          <span class="fz:text-xs fz:text-gray-600">{{ icon }}</span>
        </div>
      </div>
    `,
  }),
}

export const Loading: Story = {
  args: {
    variant: 'success',
    default: 'Опубликовать',
  },
  render: args => ({
    components: { FzButton },
    setup() {
      const loading = ref(false)
      return {
        loading,
        args,
      }
    },
    template: `
      <div class="fz:flex fz:flex-col fz:gap-4">
        <FzButton @click="loading = !loading" v-bind="args" :loading="loading" size="small" class="fz:self-start">{{ 'Опубликовать' }}</FzButton>
        <FzButton @click="loading = !loading" v-bind="args" :loading="loading" size="medium" class="fz:self-start">{{ 'Опубликовать' }}</FzButton>
        <FzButton @click="loading = !loading" v-bind="args" :loading="loading" size="large" class="fz:self-start">{{ 'Опубликовать' }}</FzButton>
      </div>
    `,
  }),
}

export const LoadingIconOnly: Story = {
  args: {
    loading: true,
    icon: 'save',
    iconOnly: true,
  },
}

export const ButtonSizesRightArrow: Story = {
  render: () => ({
    components: { FzButton },
    template: `
      <div class="fz:flex fz:flex-col fz:gap-6">
        <div class="fz:flex fz:flex-row fz:items-center fz:gap-4">
          <FzButton size="small" icon="arrowdown" iconPosition="right">Опубликовать</FzButton>
          <FzButton size="medium" icon="arrowdown" iconPosition="right">Опубликовать</FzButton>
          <FzButton size="large" icon="arrowdown" iconPosition="right">Опубликовать</FzButton>
        </div>
        <div class="fz:h-1 fz:w-full fz:bg-gray-200"></div>
        <div class="fz:flex fz:flex-row fz:items-center fz:gap-4">
          <FzButton size="small" icon="plus" iconPosition="left" variant="secondary">Добавить</FzButton>
          <FzButton size="medium" icon="plus" iconPosition="left" variant="secondary">Добавить</FzButton>
          <FzButton size="large" icon="plus" iconPosition="left" variant="secondary">Добавить</FzButton>
        </div>
      </div>
    `,
  }),
}
