<script setup lang="ts">
import { computed, useSlots } from 'vue'
import { FzIcon } from '../FzIcon'
import FzLoader from '../FzLoader'
import FzTransition from '../FzTransition'

const props = withDefaults(
  defineProps<{
    variant?: ButtonVariant
    size?: ButtonSize
    disabled?: boolean
    loading?: boolean
    icon?: string // Icon name (e.g., 'save', 'trash') - NO .svg extension
    iconPosition?: IconPosition
    iconClass?: string[]
    iconOnly?: boolean
  }>(),
  {
    variant: 'primary',
    size: 'medium',
    disabled: false,
    loading: false,
    icon: undefined,
    iconPosition: 'left',
    iconOnly: false,
  },
)

const slots = useSlots()

// @NOTE: 'secondary' – Серая кнопка с черным текстом
type ButtonVariant = 'primary' | 'secondary' | 'critical' | 'success' | 'ghost' | 'link'
type ButtonSize = 'large' | 'medium' | 'small'
type IconPosition = 'left' | 'right'

const buttonClasses = computed(() => [
  'fz-button',
  `fz-button--${props.variant}`,
  `fz-button--${props.size}`,
  {
    'fz-button--disabled': props.disabled,
    'fz-button--loading': props.loading,
    'fz-button--icon-only': props.iconOnly,
    [`fz-button--icon-${props.iconPosition}`]: props.icon && !props.iconOnly,
  },
])
</script>

<template>
  <button :class="buttonClasses" :disabled="props.disabled" type="button">
    <div class="fz-button__content" :class="{ 'fz-opacity-0': props.loading }">
      <span v-if="props.icon && props.iconPosition === 'left'" class="fz-button__icon" :class="props.iconClass">
        <FzIcon :name="props.icon" />
      </span>
      <span v-if="slots.default && !props.iconOnly" class="fz-button__label">
        <slot />
      </span>
      <span v-if="props.icon && props.iconPosition === 'right'" class="fz-button__icon" :class="props.iconClass">
        <FzIcon :name="props.icon" />
      </span>
    </div>
    <FzTransition name="fade">
      <div v-if="props.loading" class="fz-button__loader-container">
        <FzLoader size="small" />
      </div>
    </FzTransition>
  </button>
</template>

<style>
.fz-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: 6px; /* Fixed 6px border-radius for all variants */
  transition: background-color var(--fz-transition-duration) var(--fz-transition-timing-function),
    border-color var(--fz-transition-duration) var(--fz-transition-timing-function),
    color var(--fz-transition-duration) var(--fz-transition-timing-function),
    opacity var(--fz-transition-duration) var(--fz-transition-timing-function),
    box-shadow var(--fz-transition-duration) var(--fz-transition-timing-function);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

/* Remove the local ring variable definitions and use theme variables instead */

.fz-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 var(--fz-ring-offset-width) var(--fz-ring-offset-color);
}

.fz-button--disabled {
  cursor: not-allowed;
}

.fz-button--loading {
  cursor: progress;
}

/* Button content wrapper */
.fz-button__content {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: inherit;
  transition: opacity var(--fz-transition-duration) var(--fz-transition-timing-function);
}

/* Utility class for opacity */
.fz-opacity-0 {
  opacity: 0;
}

/* Loader container */
.fz-button__loader-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  font-size: 1em;
}

.fz-button__loader-container .fz-loader {
  color: currentColor;
}

/* Adjust loader size based on button size */
.fz-button--large .fz-button__loader-container {
  font-size: 18px;
}
.fz-button--medium .fz-button__loader-container {
  font-size: 16px;
}
.fz-button--small .fz-button__loader-container {
  font-size: 14px;
}

/* Button sizes */
.fz-button--large {
  min-height: 48px;
  padding: 0 24px;
  gap: var(--fz-spacing-6);
  font: var(--fz-font-button-large);
}

.fz-button--medium {
  min-height: 36px;
  padding: 0 20px;
  gap: var(--fz-spacing-6);
  font: var(--fz-font-button-medium);
}

.fz-button--small {
  min-height: 30px;
  padding: 0 16px;
  gap: var(--fz-spacing-4);
  font: var(--fz-font-button-small);
}

/* Adjust padding when icons are present */
.fz-button--large.fz-button--icon-left {
  padding-left: 20px;
}

.fz-button--large.fz-button--icon-right {
  padding-right: 20px;
}

.fz-button--medium.fz-button--icon-left {
  padding-left: 16px;
}

.fz-button--medium.fz-button--icon-right {
  padding-right: 16px;
}

.fz-button--small.fz-button--icon-left {
  padding-left: 12px;
}

.fz-button--small.fz-button--icon-right {
  padding-right: 12px;
}

.fz-button__icon {
  display: flex;
  align-items: center;
  transition: color var(--fz-transition-duration) var(--fz-transition-timing-function);
}

/* Icon only styles */
.fz-button--icon-only.fz-button--large {
  width: 48px;
  height: 48px;
  padding: 0;
}

.fz-button--icon-only.fz-button--medium {
  width: 36px;
  height: 36px;
  padding: 0;
}

.fz-button--icon-only.fz-button--small {
  width: 30px;
  height: 30px;
  padding: 0;
}

.fz-button--icon-only .fz-button__icon {
  margin: 0;
}

.fz-button--large.fz-button--icon-only .fz-button__icon {
  width: 18px;
  height: 18px;
}

.fz-button--medium.fz-button--icon-only .fz-button__icon {
  width: 16px;
  height: 16px;
}

.fz-button--small.fz-button--icon-only .fz-button__icon {
  width: 14px;
  height: 14px;
}

/* Variants using CSS variables */
/* Primary (Blue) */
.fz-button--primary {
  background-color: var(--fz-color-surface-brand-primary);
  color: #ffffff;
}

.fz-button--primary:not(.fz-button--disabled, .fz-button--loading):hover {
  background-color: var(--fz-color-surface-brand-primary-hover);
}

.fz-button--primary:not(.fz-button--disabled, .fz-button--loading):active {
  background-color: var(--fz-color-surface-brand-primary-clicked);
}

.fz-button--primary:not(.fz-button--disabled, .fz-button--loading):focus-visible {
  box-shadow: 0 0 0 var(--fz-ring-width) var(--fz-color-border-button-primary-focused);
}

.fz-button--primary.fz-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Success (Green) */
.fz-button--success {
  background-color: var(--fz-color-surface-success-primary);
  color: var(--fz-color-text-success);
}

.fz-button--success:not(.fz-button--disabled, .fz-button--loading):hover {
  background-color: var(--fz-color-surface-success-primary-hover);
}

.fz-button--success:not(.fz-button--disabled, .fz-button--loading):active {
  background-color: var(--fz-color-surface-success-primary-clicked);
}

.fz-button--success:not(.fz-button--disabled, .fz-button--loading):focus-visible {
  box-shadow: 0 0 0 var(--fz-ring-width) var(--fz-color-border-button-success-focused);
}

.fz-button--success.fz-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Secondary (Gray) */
.fz-button--secondary {
  background-color: var(--fz-color-surface-secondary);
  color: var(--fz-color-text-primary);
}

.fz-button--secondary .fz-button__icon {
  color: var(--fz-color-border-icon);
}

.fz-button--secondary:not(.fz-button--disabled, .fz-button--loading):hover {
  background-color: var(--fz-color-surface-secondary-hover);
}

.fz-button--secondary:not(.fz-button--disabled, .fz-button--loading):active {
  background-color: var(--fz-color-surface-secondary-clicked);
}

.fz-button--secondary:not(.fz-button--disabled, .fz-button--loading):focus-visible {
  box-shadow: 0 0 0 var(--fz-ring-width) var(--fz-color-border-button-secondary-focused);
}

.fz-button--secondary.fz-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Critical (Red) */
.fz-button--critical {
  background-color: var(--fz-color-surface-critical-primary);
  color: var(--fz-color-text-critical)
}

.fz-button--critical:not(.fz-button--disabled, .fz-button--loading):hover {
  background-color: var(--fz-color-surface-critical-primary-hover);
}

.fz-button--critical:not(.fz-button--disabled, .fz-button--loading):active {
  background-color: var(--fz-color-surface-critical-primary-clicked);
}

.fz-button--critical:not(.fz-button--disabled, .fz-button--loading):focus-visible {
  box-shadow: 0 0 0 var(--fz-ring-width) var(--fz-color-border-button-critical-focused);
}

.fz-button--critical.fz-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Ghost (Transparent) */
.fz-button--ghost {
  background-color: transparent;
  color: var(--fz-color-text-link);
}

.fz-button--ghost:not(.fz-button--disabled, .fz-button--loading):hover {
  background-color: var(--fz-color-surface-ghost-hover);
}

.fz-button--ghost:not(.fz-button--disabled, .fz-button--loading):active {
  background-color: var(--fz-color-surface-ghost-clicked);
}

.fz-button--ghost:not(.fz-button--disabled, .fz-button--loading):focus-visible {
  box-shadow: 0 0 0 var(--fz-ring-width) var(--fz-color-border-button-secondary-focused);
}

.fz-button--ghost .fz-button__icon {
  color: var(--fz-color-border-icon);
}

/* Link (Transparent with dark gray text/icon, icon darkens on hover) */
.fz-button--link {
  background-color: transparent;
  color: var(--fz-color-border-icon); /* Text color as icon color */
}

.fz-button--link:not(.fz-button--disabled, .fz-button--loading):hover {
  color: var(--fz-color-text-primary); /* Darker icon on hover */
}

.fz-button--link:not(.fz-button--disabled, .fz-button--loading):active {
  background-color: var(--fz-color-surface-ghost-clicked); /* Same active background as ghost */
}

.fz-button--link:not(.fz-button--disabled, .fz-button--loading):focus-visible {
  box-shadow: 0 0 0 var(--fz-ring-width) var(--fz-color-border-button-secondary-focused);
}

.fz-button--link.fz-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
