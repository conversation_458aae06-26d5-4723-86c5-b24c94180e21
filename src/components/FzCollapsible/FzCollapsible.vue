<script setup lang="ts">
import type { CollapsibleRootProps } from 'reka-ui'
import {
  CollapsibleContent,
  CollapsibleRoot,
  CollapsibleTrigger,
  useForwardPropsEmits,
} from 'reka-ui'
// useForwardPropsEmits is not strictly necessary if we define props and emits explicitly
// but can be useful if we want to pass all props through without redefining them.
// For clarity and adherence to the reka-ui examples, let's manage props explicitly.

// Define the props for FzCollapsible.
// We can extend CollapsibleRootProps if we want to pass them all through,
// or pick specific ones.
interface FzCollapsibleProps extends CollapsibleRootProps {}

const props = defineProps<FzCollapsibleProps>()

// Define emits if FzCollapsible needs to emit events,
// especially for v-model integration on the 'open' state.
const emits = defineEmits<{
  (e: 'update:open', value: boolean): void
}>()

// Forward props and emits
const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <CollapsibleRoot v-bind="forwarded" class="fz-collapsible">
    <CollapsibleTrigger as-child>
      <slot name="trigger" />
    </CollapsibleTrigger>
    <CollapsibleContent class="fz-collapsible__content">
      <slot name="content" />
      <!-- Fallback content if no slot is provided, or additional fixed content -->
    </CollapsibleContent>
  </CollapsibleRoot>
</template>

<style>
.fz-collapsible__content {
  overflow: hidden;
}

/* Animation classes based on reka-ui documentation example */
.fz-collapsible__content[data-state='open'] {
  animation: fz-slide-down 300ms ease-out;
}

.fz-collapsible__content[data-state='closed'] {
  animation: fz-slide-up 300ms ease-out;
}

@keyframes fz-slide-down {
  from {
    height: 0;
  }
  to {
    /* This CSS variable is provided by reka-ui's CollapsibleContent */
    height: var(--reka-collapsible-content-height);
  }
}

@keyframes fz-slide-up {
  from {
    /* This CSS variable is provided by reka-ui's CollapsibleContent */
    height: var(--reka-collapsible-content-height);
  }
  to {
    height: 0;
  }
}
</style>
