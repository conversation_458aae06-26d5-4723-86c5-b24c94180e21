import type { Meta, StoryObj } from '@storybook/vue3'
import { FzCollapsible } from '.' // Assuming FzCollapsible is exported from index.ts

const meta: Meta<typeof FzCollapsible> = {
  title: 'Components/FzCollapsible',
  component: FzCollapsible,
  tags: ['autodocs'],
  argTypes: {
    open: { control: 'boolean' },
    defaultOpen: { control: 'boolean' },
    disabled: { control: 'boolean' },
    unmountOnHide: { control: 'boolean' },
    // Slots are not typically controlled via argTypes in this manner for Storybook
    // but we can document them.
  },
  parameters: {
    slots: {
      trigger: {
        description: 'Slot for the collapsible trigger mechanism.',
        template: '<button>Toggle</button>',
      },
      content: {
        description: 'Slot for the collapsible content.',
        template: '<p>Collapsible content goes here.</p>',
      },
    },
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    defaultOpen: false,
  },
  render: args => ({
    components: { FzCollapsible },
    setup() {
      return { args }
    },
    template: `
      <FzCollapsible v-bind="args">
        <template #trigger>
          <button 
            style="background-color: #eee; padding: 10px; border: 1px solid #ccc; width: 100%; text-align: left;"
          >
            Click to Toggle Content (Default)
          </button>
        </template>
        <template #content>
          <div style="padding: 20px; border: 1px solid #eee; border-top: none;">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
            <p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
          </div>
        </template>
      </FzCollapsible>
    `,
  }),
}

export const InitiallyOpen: Story = {
  args: {
    defaultOpen: true,
  },
  render: args => ({
    components: { FzCollapsible },
    setup() {
      return { args }
    },
    template: `
      <FzCollapsible v-bind="args">
        <template #trigger>
          <button 
            style="background-color: #eef; padding: 10px; border: 1px solid #ccf; width: 100%; text-align: left;"
          >
            Click to Toggle Content (Initially Open)
          </button>
        </template>
        <template #content>
          <div style="padding: 20px; border: 1px solid #eef; border-top: none;">
            <p>This content is visible by default.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
          </div>
        </template>
      </FzCollapsible>
    `,
  }),
}

export const Controlled: Story = {
  args: {
    // 'open' will be controlled by Storybook controls
  },
  render: args => ({
    components: { FzCollapsible },
    setup() {
      // For controlled components, Storybook's args will manage the 'open' prop.
      // We can also use a local ref if we want to demonstrate v-model type behavior within the story itself,
      // but for simple prop control, args is sufficient.
      return { args }
    },
    template: `
      <FzCollapsible v-bind="args">
        <template #trigger>
          <button 
            style="background-color: #efe; padding: 10px; border: 1px solid #cfc; width: 100%; text-align: left;"
          >
            Click to Toggle Content (Controlled by Storybook Args)
          </button>
        </template>
        <template #content>
          <div style="padding: 20px; border: 1px solid #efe; border-top: none;">
            <p>This component is controlled by the 'open' arg in Storybook controls.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
          </div>
        </template>
      </FzCollapsible>
    `,
  }),
}

export const Disabled: Story = {
  args: {
    disabled: true,
    defaultOpen: false,
  },
  render: args => ({
    components: { FzCollapsible },
    setup() {
      return { args }
    },
    template: `
      <FzCollapsible v-bind="args">
        <template #trigger>
          <button 
            style="background-color: #ddd; padding: 10px; border: 1px solid #bbb; width: 100%; text-align: left; cursor: not-allowed;"
          >
            Cannot Toggle (Disabled)
          </button>
        </template>
        <template #content>
          <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
            <p>This content is not accessible because the collapsible is disabled.</p>
          </div>
        </template>
      </FzCollapsible>
    `,
  }),
}

export const UnmountOnHide: Story = {
  args: {
    defaultOpen: false,
    unmountOnHide: true,
  },
  render: args => ({
    components: { FzCollapsible },
    setup() {
      return { args }
    },
    template: `
      <FzCollapsible v-bind="args">
        <template #trigger>
          <button 
            style="background-color: #fee; padding: 10px; border: 1px solid #fcc; width: 100%; text-align: left;"
          >
            Toggle (Content unmounts when hidden)
          </button>
        </template>
        <template #content>
          <div style="padding: 20px; border: 1px solid #fee; border-top: none;">
            <p>This content will be removed from the DOM when collapsed.</p>
            <p>Inspect the DOM to see the difference.</p>
          </div>
        </template>
      </FzCollapsible>
    `,
  }),
}
