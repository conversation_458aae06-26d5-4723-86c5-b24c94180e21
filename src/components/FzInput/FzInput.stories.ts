import type { <PERSON>a, StoryObj } from '@storybook/vue3'

import FzIcon from '../FzIcon'
import FzInput from './FzInput.vue'

const meta: Meta<typeof FzInput> = {
  title: 'Components/FzInput/FzInput',
  component: FzInput,
  tags: ['autodocs'],
  argTypes: {
    placeholder: {
      control: 'text',
      description: 'Input placeholder',
      table: {
        defaultValue: { summary: '' },
      },
    },
    type: {
      control: 'select',
      options: ['text', 'password'],
      description: 'Input type',
      table: {
        defaultValue: { summary: 'text' },
      },
    },
    variant: {
      control: 'select',
      options: ['outline', 'underline'],
      description: 'Input style variant',
      table: {
        defaultValue: { summary: 'outline' },
      },
    },
    disabled: {
      control: 'boolean',
      description: 'Disables the input',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    clearable: {
      control: 'boolean',
      description: 'Clear the input if there is a value',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    counter: {
      control: 'boolean',
      description: 'Display the number of characters allowed',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    maxlength: {
      control: 'number',
      description: 'Number of characters allowed',
      table: {
        defaultValue: { summary: '250' },
      },
    },
    isInvalid: {
      control: 'boolean',
      description: 'validation error',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    isSuccess: {
      control: 'boolean',
      description: 'text success',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    autosize: {
      control: 'boolean',
      description: 'Adapt input width to its content',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
  },
  args: {
    modelValue: 'Text',
  },
}

export default meta
type Story = StoryObj<typeof FzInput>

export const Default: Story = {
  args: {
    variant: 'outline',
  },
}

export const Underline: Story = {
  args: {
    variant: 'underline',
  },
}

export const Disabled: Story = {
  args: {
    variant: 'outline',
    disabled: true,
  },
}

export const Clearable: Story = {
  args: {
    variant: 'outline',
    clearable: true,
  },
}

export const Counter: Story = {
  args: {
    maxlength: 5,
    counter: true,
  },
  render: args => ({
    components: { FzInput, FzIcon },
    setup() {
      return { args }
    },
    template: `
        <FzInput placeholder="Text" v-bind="args" style="width: 240px" />
        <FzInput placeholder="Text" style="width: 240px; margin-top: 5px" />
    `,
  }),
}

export const MaxLength: Story = {
  args: {
    variant: 'outline',
    maxlength: 5,
  },
}

export const isInvalid: Story = {
  args: {
    variant: 'outline',
    isInvalid: true,
  },
}

export const isSuccess: Story = {
  args: {
    variant: 'outline',
    isSuccess: true,
  },
}

export const Autosize: Story = {
  name: 'Autosize',
  render: () => ({
    components: { FzInput, FzIcon },
    template: `
      <div style="display: flex; gap: 16px; width: 100%; flex-wrap: wrap;">
        <FzInput variant="underline" :autosize="true" placeholder="First name">
          <template #prependIcon>
            <FzIcon name="profile" size="md" />
          </template>
        </FzInput>
        <FzInput variant="underline" :autosize="true" placeholder="Last name">
          <template #appendIcon>
            <FzIcon name="apply" size="md" />
          </template>
        </FzInput>
        <FzInput variant="underline" :autosize="true" placeholder="Email">
          <template #prependIcon>
            <FzIcon name="answers" size="md" />
          </template>
        </FzInput>
        <div style="width: 100%; display: flex; gap: 16px; flex-wrap: wrap;">
        <FzInput variant="outline" :autosize="true" placeholder="City">
          <template #prependIcon>
            <FzIcon name="apply" size="md" />
          </template>
        </FzInput>
        <FzInput variant="outline" :autosize="true" placeholder="Country">
          <template #appendIcon>
            <FzIcon name="profile" size="md" />
            </template>
          </FzInput>
        </div>
      </div>
    `,
  }),
}

export const prependIcon: Story = {
  render: () => ({
    components: { FzInput, FzIcon },
    template: `
      <FzInput placeholder="Text">
        <template #prependIcon>
          <FzIcon name="profile" size="md" />
        </template>
      </FzInput>
    `,
  }),
}

export const appendIcon: Story = {
  render: () => ({
    components: { FzInput, FzIcon },
    template: `
      <FzInput placeholder="Text">
        <template #appendIcon>
          <FzIcon name="profile" size="md" />
        </template>
      </FzInput>
    `,
  }),
}
