<script setup lang="ts">
import { computed, onMounted, ref, useSlots, watch } from 'vue'
import { FzIcon } from '../FzIcon'

const props = withDefaults(
  defineProps<{
    variant?: InputVariant
    type?: InputType
    disabled?: boolean
    clearable?: boolean
    counter?: boolean
    maxlength?: number
    isInvalid?: boolean
    isSuccess?: boolean
    placeholder?: string
    autosize?: boolean
  }>(),
  {
    variant: 'outline',
    type: 'text',
    disabled: false,
    clearable: false,
    counter: false,
    maxlength: 250,
    isInvalid: false,
    isSuccess: false,
    autosize: false,
  },
)

const emit = defineEmits<{
  (e: 'enter', value: Event): void
  (e: 'focus', value: Event): void
  (e: 'blur', value: Event): void
}>()

const slots = useSlots()

const modelInput = defineModel<string>({ default: '' })

// Используем contenteditable span для авто-растущего инпута. У стандартных инпутов нет простого способа реализовать такое поведение.
// @see https://css-tricks.com/auto-growing-inputs-textareas/
const editableSpan = ref<HTMLSpanElement | null>(null)

function onContentEditableInput(e: Event): void {
  const target = e.target as HTMLSpanElement
  modelInput.value = target.textContent || ''
}

watch(modelInput, (newValue) => {
  if (editableSpan.value && editableSpan.value.textContent !== newValue) {
    editableSpan.value.textContent = newValue
  }
})

onMounted(() => {
  if (editableSpan.value) {
    editableSpan.value.textContent = modelInput.value
  }
})

// @NOTE: 'underline' – input с подчеркнутой линией
export type InputVariant = 'underline' | 'outline'
export type InputType = 'text' | 'password'

const inputClassesBlock = computed(() => [
  'fz-input',
  `fz-input--${props.variant}`,
  {
    'fz-input--disabled': props.disabled,
    'fz-input--invalid': props.isInvalid && !props.isSuccess,
    'fz-input--success': props.isSuccess && !props.isInvalid && props.variant !== 'underline',
    'fz-input--editable': props.autosize,
  },
])

const inputClasses = computed(() => [
  {
    'fz-input--disabled': props.disabled,
    'fz-input--has-icon': (props.counter || props.clearable || props.isSuccess || slots.appendIcon) && props.variant !== 'underline',
  },
])

const inputPrependIcons = computed(() => [
  `fz-input__prepend-icons--${props.variant}`,
  {
    'fz-input__prepend-icons': slots.prependIcon,
  },
])

function onClearable(): void {
  modelInput.value = ''
}

const count = computed(() => {
  return props.maxlength - (modelInput.value?.length || 0)
})

function onEnter(e: Event): void {
  emit('enter', e)
}

function onFocus(e: Event): void {
  emit('focus', e)
}

function onBlur(e: Event): void {
  emit('blur', e)
}
</script>

<template>
  <div :class="inputClassesBlock">
    <div v-if="slots.prependIcon" :class="inputPrependIcons">
      <slot name="prependIcon" />
    </div>
    <span
      v-if="props.autosize"
      ref="editableSpan"
      class="fz-input__field fz-input__field--editable"
      :class="inputClasses"
      contenteditable="true"
      role="textbox"
      :aria-disabled="disabled"
      :placeholder="placeholder"
      :maxlength="maxlength"
      @input="onContentEditableInput"
      @keydown.enter.prevent="onEnter"
      @focus="onFocus"
      @blur="onBlur"
    />
    <input
      v-else
      v-model="modelInput"
      :type="type"
      :placeholder="placeholder"
      :maxlength="maxlength"
      class="fz-input__field"
      :class="inputClasses"
      aria-hidden="false"
      :disabled="disabled"
      @keydown.enter="onEnter"
      @focus="onFocus"
      @blur="onBlur"
    >
    <div
      v-if="counter || clearable || isSuccess || slots.appendIcon"
      class="fz-input__icons"
      :class="{ 'fz-input__icons--outline': props.variant !== 'underline' }"
    >
      <div v-if="counter && props.variant !== 'underline'" class="fc-input--count typography-caption">
        {{ count }}
      </div>
      <span
        v-if="isSuccess && props.variant !== 'underline'"
        class="fz-input--success"
      >
        <FzIcon
          name="v"
          size="md"
        />
      </span>
      <span
        v-if="clearable && modelInput.length > 0 && !isSuccess"
        class="fz-input--clearable"
      >
        <FzIcon
          name="x"
          size="md"
          clickable
          @click="onClearable"
        />
      </span>
      <span v-if="slots.appendIcon" class="fz-input__append-icon">
        <slot name="appendIcon" />
      </span>
    </div>
  </div>
</template>

<style>
.fz-input {
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-4);
  box-sizing: border-box;
  min-width: 240px;
  width: 100%;
  overflow: hidden;
  transition: border-color var(--fz-transition-duration);
}

.fz-input__field{
  border: 0;
  outline: none;
  width: 100%;
  padding: 0;
  color: var(--fz-color-text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.fz-input__field::placeholder{
  font-weight: var(--fz-font-weight-regular);
  color: var(--fz-color-text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 100%;
}

.fz-input:focus-within {
  border-color: var(--fz-color-border-input-focus);
}

.fz-input--underline {
  height: 30px;
  font-size: var(--fz-font-size-xs);
  border-bottom: 1px solid var(--fz-color-border-input);
}

.fz-input--underline .fz-input__field{
  box-sizing: border-box;
  padding: var(--fz-spacing-4) 0;
}

.fz-input--outline {
  font-size: var(--fz-font-size-sm);
  border: 1px solid var(--fz-color-border-input);
  border-radius: var(--fz-radius-sm);
  height: 48px;
}

.fz-input--outline .fz-input__field{
  box-sizing: border-box;
  padding: var(--fz-spacing-8) var(--fz-spacing-6) var(--fz-spacing-8) var(--fz-spacing-8);
}

.fz-input--editable {
  width: auto;
}

.fz-input--editable .fz-input__field {
  width: auto;
  flex-grow: 1;
}

.fz-input__prepend-icons{
  display: flex;
  color: var(--fz-color-text-tertiary);

}

.fz-input__prepend-icons--outline{
  padding: var(--fz-spacing-8) 0 var(--fz-spacing-8) var(--fz-spacing-6);
}

.fz-input__prepend-icons--underline{
  padding: var(--fz-spacing-4) 0;
}

.fz-input--outline .fz-input--has-icon{
  padding: var(--fz-spacing-8) 0 var(--fz-spacing-8) var(--fz-spacing-8);
}

.fz-input__prepend-icons--outline ~ .fz-input__field{
  padding: var(--fz-spacing-8) var(--fz-spacing-6) var(--fz-spacing-8) 0;
}

.fz-input__prepend-icons--outline ~ .fz-input--has-icon {
  padding: var(--fz-spacing-8) 0;
}

.fz-input--success{
  border-color: var(--fz-color-border-icon-success);
}

.fz-input--invalid{
  border-color: var(--fz-color-border-icon-critical);
}

.fz-input--disabled{
  border-color: var(--fz-color-border-input);
  background: var(--fz-color-surface-disabled);
}

.fz-input__icons{
  transition: color var(--fz-transition-duration) var(--fz-transition-timing-function),
  opacity var(--fz-transition-duration) var(--fz-transition-timing-function);
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-3);
}

.fz-input__icons--outline{
  padding: var(--fz-spacing-8) var(--fz-spacing-6) var(--fz-spacing-8) 0;
}

.fz-input__icons .fz-input--clearable{
  display: flex;
  color: var(--fz-color-text-critical) ;
}

.fz-input__icons .fz-input--success{
  display: flex;
  color: var(--fz-color-border-icon-success);
}

.fz-input__icons .fc-input--count{
  color: var(--fz-color-text-tertiary);
}

.fz-input__append-icon{
  display: flex;
  color: var(--fz-color-text-tertiary);
}

/* Placeholder for contenteditable span */
.fz-input__field--editable[contenteditable="true"]:empty:before {
  content: attr(placeholder);
  font-weight: var(--fz-font-weight-regular);
  color: var(--fz-color-text-secondary);
}
</style>
