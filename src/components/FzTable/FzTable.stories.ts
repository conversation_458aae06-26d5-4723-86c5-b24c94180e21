import type { <PERSON>a, StoryObj } from '@storybook/vue3'
import type { FzTableColumn, FzTableColumnFiltersState, FzTableProps, FzTableSortingState } from './index'
import { h, ref } from 'vue'
import FzSidesheet from '../FzSidesheet/FzSidesheet.vue'
import FzTable from './FzTable.vue'

const meta = {
  components: { FzSidesheet },
  title: 'Components/FzTable',
  component: FzTable,
  tags: ['autodocs'],
  argTypes: {
    data: {
      control: 'object',
      description: 'Table data',
    },
    columns: {
      control: 'object',
      description: 'Table columns configuration',
    },
    stickyHeader: {
      control: 'boolean',
      description: 'Enable sticky header',
    },
    loading: {
      control: 'boolean',
      description: 'Loading state (shows overlay)',
    },
    paginationLoading: {
      control: 'boolean',
      description: 'Pagination loading state (shows loading row)',
    },
    optionBtn: {
      control: 'boolean',
      description: 'Fixed action button',
    },
    noResultsText: {
      control: 'text',
      description: 'Text to display when no data',
    },
    hasMore: {
      control: 'boolean',
      description: 'Whether there are more items to load',
      defaultValue: true,
    },
  },
  args: {
    serverMode: false,
  },
} as Meta<FzTableProps>

export default meta
type Story = StoryObj<typeof meta>

// Mock data
const mockUsers = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', age: 32 },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', age: 28 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 45 },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', age: 11 },
]

const baseColumns: FzTableColumn[] = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    size: 200,
    sorted: true,
    filter: true,
    placeholderFilter: 'NameP',
    cell: (props: any) => h('strong', {}, props.getValue()),
  },
  {
    id: 'email',
    header: 'Email',
    accessorKey: 'email',
    size: 250,
    sorted: true,
    filter: true,
    placeholderFilter: 'emailP',
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    cell: (props: any) => {
      const status = props.getValue()
      return h('span', {
        class: `status-badge status-${status}`,
      }, status.charAt(0).toUpperCase() + status.slice(1))
    },
  },
  {
    id: 'age',
    header: 'Age',
    accessorKey: 'age',
    size: 80,
    sorted: true,
    filter: true,
    placeholderFilter: 'ageP',
  },
]

function generateLargeDataset(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `User ${i + 1}`,
    email: `user${i + 1}@example.com`,
    status: i % 2 === 0 ? 'active' : 'inactive',
    age: 20 + (i % 30),
  }))
}

const allData = generateLargeDataset(500)
const initialData = allData.slice(0, 50)

export const Default: Story = {
  args: {
    data: mockUsers,
    columns: baseColumns,
  },
}

export const WithMasks: Story = {
  args: {
    data: [
      { id: 1, name: 'John Doe', phone: '9998887766', birthDate: '01011990' },
      { id: 2, name: 'Jane Smith', phone: '9998887767', birthDate: '02021991' },
    ],
    columns: [
      {
        id: 'name',
        header: 'Name',
        accessorKey: 'name',
        size: 200,
        filter: true,
      },
      {
        id: 'phone',
        header: 'Phone',
        accessorKey: 'phone',
        size: 250,
        filter: true,
        mask: 'phone',
      },
      {
        id: 'birthDate',
        header: 'Birth Date',
        accessorKey: 'birthDate',
        size: 150,
        filter: true,
        mask: 'date',
      },
    ] as FzTableColumn[],
  },
}

export const Empty: Story = {
  args: {
    data: [],
    columns: baseColumns,
    noResultsText: 'Совпадений не найдено',
  },
}

export const Loading: Story = {
  args: {
    data: [],
    columns: baseColumns,
    loading: true,
  },
}

export const OptionBtn: Story = {
  args: {
    data: mockUsers,
    optionBtn: true,
  },
  render: args => ({
    components: { FzTable, FzSidesheet },
    setup() {
      const baseColumnsScroll = [
        {
          id: 'name',
          header: 'Name',
          accessorKey: 'name',
          size: 200,
          sorted: true,
          filter: true,
          placeholderFilter: 'NameP',
          cell: (props: any) => h('strong', {}, props.getValue()),
        },
        {
          id: 'email',
          header: 'Email',
          accessorKey: 'email',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email1',
          header: 'Email2',
          accessorKey: 'email1',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email2',
          header: 'Email3',
          accessorKey: 'email2',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email3',
          header: 'Email4',
          accessorKey: 'email23',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email211',
          header: 'Email5',
          accessorKey: 'email211',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email222',
          header: 'Email6',
          accessorKey: 'email222',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email2323',
          header: 'Email7',
          accessorKey: 'email2323',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email244',
          header: 'Email8',
          accessorKey: 'email2444',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'status',
          header: 'Status',
          accessorKey: 'status',
          cell: (props: any) => {
            const status = props.getValue()
            return h('span', {
              class: `status-badge status-${status}`,
            }, status.charAt(0).toUpperCase() + status.slice(1))
          },
        },
        {
          id: 'age',
          header: 'Age',
          accessorKey: 'age',
          size: 80,
          sorted: true,
          filter: true,
          placeholderFilter: 'ageP',
        },
      ]
      const edit = ref(false)
      const user = ref(false)

      return { args, baseColumnsScroll, edit, user }
    },
    template: `
      <FzSidesheet v-model="edit" title="edit"/>
      <FzSidesheet v-model="user" title="user"/>
      <FzTable 
        v-bind="args"
        :columns="baseColumnsScroll"
        @editBtn="edit = true"
        @userAuthorization="user = true"
      />
    `,
  }),
}

export const StickyHeader: Story = {
  args: {
    data: mockUsers,
    columns: baseColumns,
    stickyHeader: true,
  },
  render: args => ({
    components: { FzTable },
    setup() {
      return { args }
    },
    template: `
      <div style="height: 300px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; margin-bottom: 20px;">
        Block before table (300px height)
      </div>
      <FzTable v-bind="args" />
      <div style="height: 1000px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; margin-top: 20px;">
        Block after table (1000px height)
      </div>
    `,
  }),
}

export const WithCustomSlots: Story = {
  args: {
    data: [],
    columns: baseColumns,
  },
  render: args => ({
    components: { FzTable },
    setup() {
      const handleRowClick = () => {
      }

      return { args, handleRowClick }
    },
    template: `
      <FzTable 
        v-bind="args" 
        @row-click="handleRowClick"
      >
        <template #empty>
          <div style="padding: 40px; text-align: center; color: #666;">
            Custom empty state message
          </div>
        </template>
        
        <template #loading>
          <div style="padding: 40px; text-align: center;">
            <div style="margin-bottom: 10px;">Loading data...</div>
            <div style="width: 100%; height: 4px; background: #f0f0f0;">
              <div style="width: 50%; height: 100%; background: #2196F3; animation: progress 2s ease-in-out infinite;"></div>
            </div>
          </div>
        </template>
      </FzTable>
      
      <style>
        @keyframes progress {
          0% { margin-left: 0; margin-right: 100%; }
          50% { margin-left: 25%; margin-right: 0; }
          100% { margin-left: 100%; margin-right: 0; }
        }
      </style>
    `,
  }),
}

export const WithInfiniteScroll: Story = {
  args: {
    columns: baseColumns,
    data: initialData,
    loading: false,
    paginationLoading: false,
    hasMore: true,
    noResultsText: 'No more data',
  },
  render: args => ({
    components: { FzTable },
    setup() {
      const data = ref([...initialData])
      const paginationLoading = ref(false)
      const hasMore = ref(true)
      const pageSize = 50
      const totalItems = 500 // Общее количество доступных элементов

      // Функция для загрузки дополнительных данных
      const loadMoreData = () => {
        if (!hasMore.value || paginationLoading.value)
          return

        paginationLoading.value = true

        // Имитация загрузки с API
        setTimeout(() => {
          const startIndex = data.value.length
          const endIndex = Math.min(startIndex + pageSize, totalItems)

          // Генерируем новые данные
          const newData = Array.from({ length: endIndex - startIndex }, (_, i) => ({
            id: startIndex + i + 1,
            name: `User ${startIndex + i + 1}`,
            email: `user${startIndex + i + 1}@example.com`,
            status: (startIndex + i) % 2 === 0 ? 'active' : 'inactive',
            age: 20 + ((startIndex + i) % 30),
          }))

          data.value = [...data.value, ...newData]
          hasMore.value = data.value.length < totalItems
          paginationLoading.value = false
        }, 1000)
      }

      // Функция для сброса данных
      const resetData = () => {
        data.value = [...initialData]
        hasMore.value = true
      }

      return {
        args,
        data,
        paginationLoading,
        hasMore,
        loadMoreData,
        resetData,
      }
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 16px;">
        <div style="display: flex; gap: 8px;">
          <button @click="resetData">Reset Data</button>
        </div>
        
          <FzTable
              v-bind="args"
              :data="data"
              :pagination-loading="paginationLoading"
              :has-more="hasMore"
              @load-more="loadMoreData"
          />

        <div style="color: #666;">
          Loaded: {{ data.length }} items | Has more: {{ hasMore }}
        </div>
      </div>
    `,
  }),
}

export const WithExternalColumnControl: Story = {
  args: {
    columns: baseColumns,
    data: mockUsers,
  },
  render: args => ({
    components: { FzTable },
    setup() {
      const tableRef = ref()
      const columns = ref([...args.columns])
      const columnVisibility = ref<Record<string, boolean>>(
        columns.value.reduce((acc, col) => {
          const id = col.id ?? String(col.accessorKey)
          acc[id] = col.visible ?? true
          return acc
        }, {} as Record<string, boolean>),
      )

      const toggleColumn = (columnId: string) => {
        columnVisibility.value[columnId] = !columnVisibility.value[columnId]
        tableRef.value?.toggleColumnVisibility(columnId)
      }

      return {
        args,
        columns,
        tableRef,
        columnVisibility,
        toggleColumn,
      }
    },
    template: `
      <div>
        <div style="margin-bottom: 16px; display: flex; flex-wrap: wrap; gap: 8px;">
          <div v-for="col in columns" :key="col.id ?? String(col.accessorKey)" style="display: flex; align-items: center; gap: 4px;">
            <input 
              type="checkbox" 
              :id="'col-' + (col.id ?? String(col.accessorKey))"
              :checked="columnVisibility[col.id ?? String(col.accessorKey)]"
              @change="toggleColumn(col.id ?? String(col.accessorKey))"
            >
            <label :for="'col-' + (col.id ?? String(col.accessorKey))">
              {{ typeof col.header === 'string' ? col.header : col.id }}
            </label>
          </div>
        </div>
        
        <FzTable 
          ref="tableRef"
          v-bind="args"
          :columns="columns"
        />
      </div>
    `,
  }),
}

export const OpenSidesheet: Story = {
  args: {},
  render: args => ({
    components: { FzTable, FzSidesheet },
    setup() {
      const mockUsersLink = [
        { id: 1, name: 'John Doe', link: { href: 'https://google.com', text: 'Перейти в Google' }, status: 'active', age: 32 },
        { id: 2, name: 'Jane Smith', link: 'https://yandex.ru', status: 'inactive', age: 28 },
        { id: 3, name: 'Bob Johnson1', link: 'https://foquz.ru', status: 'active', age: 45 },
      ]

      const baseColumnsLink = [
        {
          id: 'name',
          header: 'Name',
          accessorKey: 'name',
          size: 200,
          sorted: true,
          filter: true,
          placeholderFilter: 'NameP',
          cell: (props: any) => h('strong', {}, props.getValue()),
        },
        {
          id: 'link',
          header: 'Link',
          accessorKey: 'link',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'LinkP',
        },
        {
          id: 'status',
          header: 'Status',
          accessorKey: 'status',
          cell: (props: any) => {
            const status = props.getValue()
            return h('span', {
              class: `status-badge status-${status}`,
            }, status.charAt(0).toUpperCase() + status.slice(1))
          },
        },
        {
          id: 'age',
          header: 'Age',
          accessorKey: 'age',
          size: 80,
          sorted: true,
          filter: true,
          placeholderFilter: 'ageP',
        },
      ]

      const openSidesheet = ref(false)
      const userInfo = ref()

      const onOpenSidesheet = (e: { row: any }) => {
        openSidesheet.value = true
        userInfo.value = e.row
      }

      return { args, openSidesheet, onOpenSidesheet, userInfo, mockUsersLink, baseColumnsLink }
    },
    template: `
      <FzSidesheet v-model="openSidesheet" title="User">
        {{userInfo}}
      </FzSidesheet>
      <FzTable 
        :data="mockUsersLink"
        :columns="baseColumnsLink"
        v-bind="args" 
        @rowClick="onOpenSidesheet"
      />
    `,
  }),
}

export const HorizontalScroll: Story = {
  args: {
    data: mockUsers,
    columns: [],
  },
  render: args => ({
    components: { FzTable },
    setup() {
      const baseColumnsScroll = [
        {
          id: 'name',
          header: 'Name',
          accessorKey: 'name',
          size: 200,
          sorted: true,
          filter: true,
          placeholderFilter: 'NameP',
          cell: (props: any) => h('strong', {}, props.getValue()),
        },
        {
          id: 'email',
          header: 'Email',
          accessorKey: 'email',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email1',
          header: 'Email2',
          accessorKey: 'email1',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email2',
          header: 'Email3',
          accessorKey: 'email2',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email3',
          header: 'Email4',
          accessorKey: 'email23',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email211',
          header: 'Email5',
          accessorKey: 'email211',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email222',
          header: 'Email6',
          accessorKey: 'email222',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email2323',
          header: 'Email7',
          accessorKey: 'email2323',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'email244',
          header: 'Email8',
          accessorKey: 'email2444',
          size: 250,
          sorted: true,
          filter: true,
          placeholderFilter: 'emailP',
        },
        {
          id: 'status',
          header: 'Status',
          accessorKey: 'status',
          cell: (props: any) => {
            const status = props.getValue()
            return h('span', {
              class: `status-badge status-${status}`,
            }, status.charAt(0).toUpperCase() + status.slice(1))
          },
        },
        {
          id: 'status1',
          header: 'Status',
          accessorKey: 'status',
          cell: (props: any) => {
            const status = props.getValue()
            return h('span', {
              class: `status-badge status-${status}`,
            }, status.charAt(0).toUpperCase() + status.slice(1))
          },
        },
        {
          id: 'age',
          header: 'Age',
          accessorKey: 'age',
          size: 80,
          sorted: true,
          filter: true,
          placeholderFilter: 'ageP',
        },
      ]
      return { args, baseColumnsScroll }
    },
    template: `
      <FzTable 
        v-bind="args"
        :columns="baseColumnsScroll"
      />
    `,
  }),
}

export const ServerMode: Story = {
  args: {
    columns: baseColumns,
    serverMode: true,
    hasMore: true,
    stickyHeader: true,
  },
  render: args => ({
    components: { FzTable },
    setup() {
      const allData = generateLargeDataset(1000)
      const loading = ref(false)
      const paginationLoading = ref(false)
      const sorting = ref<FzTableSortingState>([])
      const columnFilters = ref<FzTableColumnFiltersState>([])
      const tableData = ref<any[]>([])
      const hasMore = ref(true)
      const pageSize = 50
      const currentPage = ref(0)

      const fetchData = (page: number = 0) => {
        const isInitialLoad = page === 0
        if (isInitialLoad) {
          loading.value = true
        }
        else {
          paginationLoading.value = true
        }

        return new Promise<{ data: any[], hasMore: boolean }>((resolve) => {
          setTimeout(() => {
            let filteredData = [...allData]

            // Apply column filters
            if (columnFilters.value.length > 0) {
              columnFilters.value.forEach((filter) => {
                const { id, value } = filter
                if (typeof value === 'string') {
                  filteredData = filteredData.filter(row =>
                    String(row[id as keyof typeof row]).toLowerCase().includes(value.toLowerCase()),
                  )
                }
              })
            }

            // Apply sorting
            if (sorting.value.length > 0) {
              const sort = sorting.value[0]
              filteredData.sort((a, b) => {
                const aValue = a[sort.id as keyof typeof a]
                const bValue = b[sort.id as keyof typeof b]
                if (aValue < bValue)
                  return sort.desc ? 1 : -1
                if (aValue > bValue)
                  return sort.desc ? -1 : 1
                return 0
              })
            }

            const start = page * pageSize
            const end = start + pageSize
            const data = filteredData.slice(start, end)
            const hasMoreData = end < filteredData.length

            loading.value = false
            paginationLoading.value = false
            resolve({ data, hasMore: hasMoreData })
          }, 2000)
        })
      }

      const loadInitialData = async () => {
        currentPage.value = 0
        const { data, hasMore: hasMoreData } = await fetchData(0)
        tableData.value = data
        hasMore.value = hasMoreData
      }

      const loadMoreData = async () => {
        if (!hasMore.value || loading.value || paginationLoading.value)
          return

        currentPage.value += 1
        const { data, hasMore: hasMoreData } = await fetchData(currentPage.value)
        tableData.value = [...tableData.value, ...data]
        hasMore.value = hasMoreData
      }

      const onUpdateSorting = (newSorting: FzTableSortingState) => {
        sorting.value = newSorting
        loadInitialData()
      }

      const onUpdateFilters = (newFilters: FzTableColumnFiltersState) => {
        columnFilters.value = newFilters
        loadInitialData()
      }

      loadInitialData()

      return {
        args,
        tableData,
        loading,
        paginationLoading,
        sorting,
        columnFilters,
        hasMore,
        onUpdateSorting,
        onUpdateFilters,
        loadMoreData,
      }
    },
    template: `
      <FzTable
        v-bind="args"
        :data="tableData"
        :loading="loading"
        :pagination-loading="paginationLoading"
        :sorting="sorting"
        :column-filters="columnFilters"
        :has-more="hasMore"
        @update:sorting="onUpdateSorting"
        @update:column-filters="onUpdateFilters"
        @load-more="loadMoreData"
      />
    `,
  }),
}
