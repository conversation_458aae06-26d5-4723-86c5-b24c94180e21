<script setup lang="ts">
import type { Column, ColumnDef, ColumnFiltersState, Header, SortingState } from '@tanstack/vue-table'
import type { InputMask } from '../../composables/useMask'
import { Maskito } from '@maskito/core'
import { FlexRender, getCoreRowModel, getFilteredRowModel, getSortedRowModel, useVueTable } from '@tanstack/vue-table'
import { computed, h, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { getMaskPlaceholder, useMask } from '../../composables/useMask'
import { FzCustomScrollbar } from '../FzCustomScrollbar'
import { FzIcon } from '../FzIcon'
import { FzLoader } from '../FzLoader'
import { FzTooltip } from '../FzTooltip'

export interface FzTableProps {
  data: any[]
  columns: FzTableColumn[]
  stickyHeader?: boolean
  loading?: boolean
  paginationLoading?: boolean
  noResultsText?: string
  hasMore?: boolean
  optionBtn?: boolean
  serverMode?: boolean
  sorting?: SortingState
  columnFilters?: ColumnFiltersState
  rowCount?: number
}

export interface FzTableColumn<TData = any, TValue = any> {
  id?: string
  header?: string | ((props: { column: any }) => any)
  sorted?: boolean
  filter?: boolean
  filterValue?: string
  placeholderFilter?: string
  mask?: InputMask
  visible?: boolean
  accessorKey?: keyof TData | string
  accessorFn?: (row: TData) => TValue
  cell?: (props: {
    row: TData
    getValue: () => TValue
    column: any
    table: any
  }) => any
  size?: number
  minSize?: number
  maxSize?: number
  flexGrow?: number
  cellClass?: string
  meta?: {
    info?: string
  }
}

interface CustomColumnMeta {
  filterMeta?: {
    filter?: boolean
    filterValue?: string
    mask?: InputMask
  }
  placeholderFilter?: string
  cellClass?: string
  flexGrow?: number
}

type FzColumnDef<TData> = ColumnDef<TData> & CustomColumnMeta

const props = withDefaults(defineProps<FzTableProps>(), {
  stickyHeader: false,
  loading: false,
  paginationLoading: false,
  hasMore: false,
  noResultsText: 'Совпадений не найдено',
  serverMode: false,
  rowCount: -1,
})

const emit = defineEmits<{
  (e: 'rowClick', payload: { row: any, event: MouseEvent }): void
  (e: 'update:sorting', sorting: SortingState): void
  (e: 'update:columnFilters', filters: ColumnFiltersState): void
  (e: 'loadMore'): void
  (e: 'editBtn', id: number): void
  (e: 'userAuthorization', id: number): void
}>()

const minWidth = '80px'
const filterInputs = ref<Record<string, HTMLInputElement>>({})
const tempFilterValues = ref<Record<string, string>>({})
const maskInstances = ref<Record<string, Maskito>>({})

const isScrolled = ref(false)
const showScrollButtons = ref({
  left: false,
  right: false,
})

const tableVisibleWidth = ref(0)
const tableScrollLeft = ref(0)
const isLoadingTriggered = ref(false)
const sorting = ref<SortingState>(props.sorting ?? [])
const columnFilters = ref<ColumnFiltersState>(props.columnFilters ?? [])
const isHorizontallyScrolled = ref(false)
const visibleColumns = ref<Record<string, boolean>>({})
const isServerLoading = ref(false)
const showPaginationLoadingRow = computed(() => {
  return props.paginationLoading
})

const tableContainer = ref<HTMLElement | null>(null)
const headerWrapper = ref<HTMLElement | null>(null)
const isHeaderStuck = ref(false)
const bodyScrollbar = ref<InstanceType<typeof FzCustomScrollbar> | null>(null)
const scrollContentRef = ref<HTMLElement | null>(null)
const scrollAnimationFrame = ref<number | null>(null)
const stickyHeaderOffset = ref(0)
const initialHeaderTop = ref(0)

// Computed property to determine if we should show server loading overlay
const showLoadingOverlay = computed(() => {
  return props.loading
})

function handlePageScroll() {
  if (!headerWrapper.value)
    return

  const headerRect = headerWrapper.value.getBoundingClientRect()
  isHeaderStuck.value = headerRect.top <= 0

  // Calculate the offset when header becomes sticky
  if (props.stickyHeader && isHeaderStuck.value) {
    // Calculate how much we've scrolled past the initial header position
    const scrolledPastHeader = Math.max(0, window.scrollY - initialHeaderTop.value)
    stickyHeaderOffset.value = scrolledPastHeader
  }
  else {
    stickyHeaderOffset.value = 0
  }
}

function handleScroll() {
  if (props.loading || !props.hasMore || isLoadingTriggered.value)
    return

  // Добавляем debounce
  if (scrollAnimationFrame.value) {
    cancelAnimationFrame(scrollAnimationFrame.value)
  }

  scrollAnimationFrame.value = requestAnimationFrame(() => {
    const { scrollY, innerHeight } = window
    const { scrollHeight } = document.documentElement
    const threshold = 100
    const isAtBottom = scrollY + innerHeight >= scrollHeight - threshold

    if (isAtBottom) {
      isLoadingTriggered.value = true
      emit('loadMore')
    }
  })
}

watch(() => props.data, () => {
  isLoadingTriggered.value = false
  if (props.serverMode) {
    isServerLoading.value = false
  }

  nextTick(() => {
    setTimeout(() => {
      checkScrollButtons()
    }, 50)
  })
})

watch(() => props.sorting, (newSorting) => {
  sorting.value = newSorting ?? []
})

watch(() => props.columnFilters, (newFilters) => {
  columnFilters.value = newFilters ?? []
})

watch(() => props.columns, (columns) => {
  columns.forEach((col) => {
    const colId = col.id ?? String(col.accessorKey)
    if (visibleColumns.value[colId] === undefined) {
      visibleColumns.value[colId] = col.visible ?? true
    }
  })
}, { immediate: true })

const filteredColumns = computed(() => {
  return props.columns.filter((col) => {
    const colId = col.id ?? String(col.accessorKey)
    return visibleColumns.value[colId] !== false
  })
})

function isLink(value: any): boolean {
  if (!value)
    return false
  if (typeof value === 'string') {
    return value.startsWith('http://') || value.startsWith('https://')
  }
  if (typeof value === 'object' && value.href) {
    return true
  }
  return false
}

function getLink(value: any): { href: string, target?: string } {
  if (typeof value === 'string') {
    return { href: value, target: '_blank' }
  }
  if (typeof value === 'object' && value.href) {
    return {
      href: value.href,
      target: value.target || '_blank',
    }
  }
  return { href: '', target: '_self' }
}

function toggleColumnVisibility(columnId: string) {
  visibleColumns.value[columnId] = !visibleColumns.value[columnId]
}

const tableColumns = computed<FzColumnDef<any>[]>(() => {
  return filteredColumns.value.map(column => ({
    id: column.id ?? String(column.accessorKey),
    accessorKey: column.accessorKey as string,
    accessorFn: column.accessorFn,
    header: column.header,
    cell: column.cell ?? defaultCellRenderer,
    size: column.size,
    minSize: column.minSize,
    maxSize: column.maxSize,
    flexGrow: column.flexGrow,
    cellClass: column.cellClass,
    meta: column.meta,
    enableSorting: column.sorted === true,
    filterMeta: {
      filter: column.filter,
      filterValue: column.filterValue ?? '',
      mask: column.mask,
    },
    placeholderFilter: column.placeholderFilter ?? getMaskPlaceholder(column.mask),
    filterFn: column.filter ? 'includesString' : undefined,
  }))
})

const table = useVueTable({
  get data() {
    return props.data
  },
  get columns() {
    return tableColumns.value
  },
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  manualPagination: props.serverMode,
  manualSorting: props.serverMode,
  manualFiltering: props.serverMode,
  enableMultiSort: false,
  enableSortingRemoval: true,
  get rowCount() {
    return props.rowCount
  },
  onColumnFiltersChange: (updater) => {
    const newFilters = typeof updater === 'function' ? updater(columnFilters.value) : updater
    columnFilters.value = newFilters
    if (props.serverMode) {
      isServerLoading.value = true
    }
    emit('update:columnFilters', newFilters)
  },
  onSortingChange: (updater) => {
    const newSorting = typeof updater === 'function' ? updater(sorting.value) : updater
    sorting.value = newSorting
    if (props.serverMode) {
      isServerLoading.value = true
    }
    emit('update:sorting', newSorting)
  },
  state: {
    get sorting() {
      return sorting.value
    },
    get columnFilters() {
      return columnFilters.value
    },
  },
})

const isEmpty = computed(() => {
  return table.getRowModel().rows.length === 0
})

function handleRowClick(row: any, event: MouseEvent) {
  const clickedElement = event.target as HTMLElement
  const isLinkClick = clickedElement.tagName === 'A' || clickedElement.closest('a')

  if (!isLinkClick) {
    emit('rowClick', { row, event })
  }
}

function handleTableScroll() {
  if (scrollContentRef.value) {
    isScrolled.value = scrollContentRef.value.scrollTop > 0
    isHorizontallyScrolled.value = scrollContentRef.value.scrollLeft > 0
    tableScrollLeft.value = scrollContentRef.value.scrollLeft
    checkScrollButtons()
  }

  // Обновляем видимую ширину таблицы
  if (tableContainer.value) {
    tableVisibleWidth.value = tableContainer.value.clientWidth
  }
}

function defaultCellRenderer(props: { getValue: () => any }) {
  const value = props.getValue()
  if (isLink(value)) {
    const link = getLink(value)
    let displayText = link.href

    if (typeof value === 'object' && value.text) {
      displayText = value.text
    }
    else {
      displayText = displayText.replace(/^https?:\/\//, '')
    }

    return h('a', {
      href: link.href,
      target: link.target,
      onClick: (e: Event) => e.stopPropagation(),
    }, displayText)
  }
  return value
}

function onEnterFilter(header: Column<any>) {
  const columnId = header.id
  const filterValue = tempFilterValues.value[columnId] || ''

  const existingFilterIndex = columnFilters.value.findIndex(f => f.id === columnId)

  if (existingFilterIndex > -1) {
    if (filterValue) {
      columnFilters.value[existingFilterIndex].value = filterValue
    }
    else {
      columnFilters.value.splice(existingFilterIndex, 1)
    }
  }
  else if (filterValue) {
    columnFilters.value.push({ id: columnId, value: filterValue })
  }

  if (props.serverMode) {
    isServerLoading.value = true
  }
  emit('update:columnFilters', [...columnFilters.value])
}

function checkScrollButtons() {
  if (!scrollContentRef.value)
    return

  showScrollButtons.value = {
    left: scrollContentRef.value.scrollLeft > 0,
    right: scrollContentRef.value.scrollLeft
      < scrollContentRef.value.scrollWidth - scrollContentRef.value.clientWidth,
  }
}

function getColumnOffsets(): number[] {
  if (!tableContainer.value)
    return []

  const headerCells = tableContainer.value.querySelectorAll<HTMLElement>('thead th')
  const offsets: number[] = []
  let cumulativeWidth = 0

  headerCells.forEach((cell) => {
    offsets.push(cumulativeWidth)
    cumulativeWidth += (cell as HTMLElement).offsetWidth
  })

  return offsets
}

function getNextScrollPosition(direction: 'left' | 'right'): number {
  if (!scrollContentRef.value)
    return 0

  const currentScroll = scrollContentRef.value.scrollLeft
  const columnOffsets = getColumnOffsets()

  if (direction === 'right') {
    // Находим первый столбец, который еще не полностью виден справа
    const nextColumnIndex = columnOffsets.findIndex(offset => offset > currentScroll)
    return nextColumnIndex >= 0
      ? columnOffsets[nextColumnIndex]
      : columnOffsets[columnOffsets.length - 1]
  }
  else {
    // Находим последний столбец, который еще не полностью виден слева
    const visibleColumns = columnOffsets.filter(offset => offset < currentScroll)
    return visibleColumns.length > 0
      ? visibleColumns[visibleColumns.length - 1]
      : 0
  }
}

function scrollTable(direction: 'left' | 'right') {
  if (!scrollContentRef.value)
    return

  const targetPosition = getNextScrollPosition(direction)

  scrollContentRef.value.scrollTo({
    left: targetPosition,
    behavior: 'smooth',
  })

  // Обновляем состояние кнопок после завершения анимации
  setTimeout(() => {
    checkScrollButtons()
  }, 300)
}

function updateTableVisibleWidth() {
  if (tableContainer.value) {
    tableVisibleWidth.value = tableContainer.value.clientWidth
  }
}

function initMasks() {
  for (const columnId in filterInputs.value) {
    const input = filterInputs.value[columnId]
    const column = table.getAllColumns().find(c => c.id === columnId)
    const maskOptions = useMask((column?.columnDef as any).filterMeta?.mask)

    if (input && maskOptions) {
      const mask = new Maskito(input, maskOptions)
      maskInstances.value[columnId] = mask
    }
  }
}

function destroyMasks() {
  for (const columnId in maskInstances.value) {
    maskInstances.value[columnId].destroy()
  }
  maskInstances.value = {}
}

onMounted(() => {
  // Capture initial header position for sticky offset calculation
  if (headerWrapper.value && props.stickyHeader) {
    initialHeaderTop.value = headerWrapper.value.getBoundingClientRect().top + window.scrollY
  }

  setTimeout(() => {
    if (bodyScrollbar.value?.simplebarInstance) {
      scrollContentRef.value = bodyScrollbar.value.simplebarInstance.getScrollElement()
      if (scrollContentRef.value) {
        scrollContentRef.value.addEventListener('scroll', handleTableScroll)
        checkScrollButtons()
      }
    }
  }, 100)

  nextTick(() => {
    setTimeout(() => {
      checkScrollButtons()
      initMasks()
    }, 150)
  })

  window.addEventListener('scroll', handlePageScroll)
  updateTableVisibleWidth()
})

watch(() => props.hasMore, (newVal) => {
  if (newVal) {
    window.addEventListener('scroll', handleScroll)
  }
}, { immediate: true })

watch(() => props.loading, (newVal) => {
  if (!newVal) {
    setTimeout(() => {
      updateTableVisibleWidth()
      checkScrollButtons()
    }, 100)
  }
}, { immediate: true })

onUnmounted(() => {
  if (scrollContentRef.value) {
    scrollContentRef.value.removeEventListener('scroll', handleTableScroll)
  }

  window.removeEventListener('scroll', handlePageScroll)
  window.removeEventListener('scroll', handleScroll)

  if (scrollAnimationFrame.value) {
    cancelAnimationFrame(scrollAnimationFrame.value)
  }
  destroyMasks()
})

// Функция для обновления ширины инпута
function updateInputWidth(columnId: string, value: string) {
  nextTick(() => {
    const input = filterInputs.value[columnId]
    if (!input)
      return

    if (!value) {
      input.style.width = minWidth
      return
    }

    // Создаем временный элемент для точного измерения
    const tempSpan = document.createElement('span')
    tempSpan.style.visibility = 'hidden'
    tempSpan.style.whiteSpace = 'pre'
    tempSpan.style.font = getComputedStyle(input).font
    tempSpan.textContent = value

    document.body.appendChild(tempSpan)
    const textWidth = tempSpan.getBoundingClientRect().width
    document.body.removeChild(tempSpan)

    input.style.width = `${Math.max(Number.parseInt(minWidth), textWidth + 5)}px`
  })
}

function handleFilterInput(header: Header<any, any>, e: Event) {
  const target = e.target as HTMLInputElement
  const value = target.value
  const columnId = header.column.id

  // Сохраняем временное значение, но не применяем фильтр
  tempFilterValues.value[columnId] = value
  updateInputWidth(columnId, value)
}

function clearFilter(header: Header<any, any>) {
  const columnId = header.column.id
  // Очищаем и временное значение, и фильтр
  tempFilterValues.value[columnId] = ''
  ;(header.column.columnDef as any).filterMeta.filterValue = ''
  header.column.setFilterValue('')
  updateInputWidth(columnId, '')
}

defineExpose({
  toggleColumnVisibility,
})
</script>

<template>
  <div class="fz-table-block" :class="{ 'fz-table-block--empty': isEmpty }">
    <div
      v-if="showScrollButtons.left || showScrollButtons.right"
      class="fz-scroll-buttons-wrapper"
    >
      <transition name="fz-fade">
        <button
          v-if="showScrollButtons.left"
          class="fz-scroll-button fz-scroll-button--left"
          @click="scrollTable('left')"
        >
          <FzIcon name="arrowleft" />
        </button>
      </transition>
      <transition name="fz-fade">
        <button
          v-if="showScrollButtons.right"
          class="fz-scroll-button fz-scroll-button--right"
          @click="scrollTable('right')"
        >
          <FzIcon name="arrowright" />
        </button>
      </transition>
    </div>
    <div
      ref="tableContainer"
      class="fz-table-container"
    >
      <div
        ref="headerWrapper"
        class="fz-table__header-wrapper"
        :class="{ 'fz-table__header-wrapper--sticky-shadow': stickyHeader && isHeaderStuck }"
      >
        <table
          class="fz-table fz-table--header"
          :style="{
            transform: `translateX(-${tableScrollLeft}px)`,
          }"
        >
          <thead
            :class="{
              'fz-table__head--sticky': stickyHeader,
            }"
            class="fz-table__head"
          >
            <tr v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id">
              <th
                v-for="header in headerGroup.headers"
                :key="header.id"
                :colspan="header.colSpan"
                :style="{
                  width: `${header.getSize()}px`,
                  minWidth: `${header.column.columnDef.minSize}px`,
                  maxWidth: `${header.column.columnDef.maxSize}px`,
                  flexGrow: (header.column.columnDef as any).flexGrow ?? 1,
                }"
                class="fz-table__head-row"
              >
                <div class="fz-table__header-block">
                  <div
                    class="fz-table__header-block-name"
                    @click="header.column.getToggleSortingHandler()?.($event)"
                  >
                    <FlexRender
                      v-if="!header.isPlaceholder"
                      :render="header.column.columnDef.header"
                      :props="header.getContext()"
                    />
                    <span v-if="header.column.getCanSort()" class="fz-table__sort-icon">
                      <span v-if="header.column.getIsSorted() === 'asc'" class="fz-table__sort">
                        <FzIcon name="arrowup-table" />
                      </span>
                      <span v-else-if="header.column.getIsSorted() === 'desc'" class="fz-table__sort">
                        <FzIcon name="arrowdown-table" />
                      </span>
                      <span v-else class="fz-table__sort--none">
                        <FzIcon name="arrowdown-table" />
                      </span>
                    </span>
                    <transition name="fz-fade">
                      <span
                        v-if="tempFilterValues[header.column.id] || (header.column.columnDef as any).filterMeta.filterValue"
                        class="fz-table__header-icon--filter"
                      >
                        <FzIcon name="filter-table" size="sm" />
                      </span>
                    </transition>
                  </div>
                  <div
                    v-if="(header.column.columnDef as any).filterMeta?.filter"
                    class="fz-table__filter-input"
                  >
                    <input
                      :ref="(el) => { if (el) filterInputs[header.column.id] = el as HTMLInputElement }"
                      :value="tempFilterValues[header.column.id]"
                      :placeholder="(header.column.columnDef as any).placeholderFilter"
                      :style="{ minWidth }"
                      @input="(e: Event) => handleFilterInput(header, e)"
                      @keydown.enter="onEnterFilter(header.column)"
                    >
                    <transition name="fade">
                      <span
                        v-if="tempFilterValues[header.column.id] || (header.column.columnDef as any).filterMeta.filterValue"
                        class="fz-table__filter-input__icon"
                      >
                        <FzIcon
                          name="x-small"
                          clickable
                          @click="() => clearFilter(header)"
                        />
                      </span>
                    </transition>
                  </div>
                </div>
              </th>
              <th
                v-if="optionBtn"
                class="fz-table__option-column-header"
                style="width: 80px"
              >
                <div :class="{ 'fz-table__shadow--left': isHorizontallyScrolled }" />
                <div class="fz-table__header-block" />
              </th>
            </tr>
          </thead>
        </table>
      </div>
      <div
        v-if="showLoadingOverlay"
        class="fz-table__loading-overlay"
        :style="{
          '--sticky-header-offset': `${stickyHeaderOffset}px`,
        }"
      >
        <div class="fz-table__loading-content">
          <FzLoader />
        </div>
      </div>
      <FzCustomScrollbar ref="bodyScrollbar" class="fz-table__body-wrapper">
        <div class="fz-table-wrapper">
          <table class="fz-table fz-table--body">
            <tbody :class="{ 'fz-table__body--loading': showLoadingOverlay }">
              <template v-if="!loading && data.length > 0 || table.getRowModel().rows.length > 0">
                <tr
                  v-for="row in table.getRowModel().rows"
                  :key="row.id"
                  class="fz-table__data-row"
                  :style="{
                    '--scroll-left': `${tableScrollLeft}px`,
                    '--table-visible-width': `${tableVisibleWidth}px`,
                  }"
                  @click="handleRowClick(row.original, $event)"
                >
                  <td
                    v-for="cell in row.getVisibleCells()"
                    :key="cell.id"
                    :class="(cell.column.columnDef as any).cellClass"
                    :style="{
                      width: `${cell.column.getSize()}px`,
                      minWidth: `${cell.column.columnDef.minSize}px`,
                      maxWidth: `${cell.column.columnDef.maxSize}px`,
                      flexGrow: (cell.column.columnDef as any).flexGrow ?? 1,
                    }"
                  >
                    <FlexRender
                      :render="cell.column.columnDef.cell ?? (() => cell.getValue())"
                      :props="cell.getContext()"
                    />
                  </td>

                  <td
                    v-if="optionBtn"
                    class="fz-table__option-column-cell"
                  >
                    <div :class="{ 'fz-table__shadow--left': isHorizontallyScrolled }" />
                    <div class="fz-table__option-btn-block">
                      <slot name="optionBtn" :row="row">
                        <FzTooltip content="Авторизоваться пользователем" position="left">
                          <span class="fz-table__option-btn-icon">
                            <FzIcon name="user" clickable @click.stop="emit('userAuthorization', row.original.id)" />
                          </span>
                        </FzTooltip>
                        <FzTooltip content="Редактировать" position="left">
                          <span class="fz-table__option-btn-icon">
                            <FzIcon name="edit" clickable @click.stop="emit('editBtn', row.original.id)" />
                          </span>
                        </FzTooltip>
                      </slot>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </FzCustomScrollbar>
    </div>
    <template v-if="isEmpty && !loading">
      <div class="fz-table__empty-row">
        <div class="fz-table__empty-content">
          <slot name="empty">
            {{ noResultsText }}
          </slot>
        </div>
      </div>
    </template>
    <template v-else-if="showPaginationLoadingRow && hasMore">
      <div class="fz-table__loading-row">
        <div class="fz-table__loading-content">
          <slot name="loading">
            <div class="fz-table__loading-content-block">
              <FzLoader />
            </div>
          </slot>
        </div>
      </div>
    </template>
  </div>
</template>

<style>
.fz-table-block{
  position: relative;
  scroll-behavior: smooth;
  width: 100%;
  height: 100%;
  --fz-table-header-height: 60px;
}

/* .fz-table-block--empty .fz-table__body-wrapper {
  display: none;
} */

.fz-table-container {
  position: relative;
  width: 100%;
  scroll-behavior: smooth;
  display: flex;
  flex-direction: column;
}

.fz-table__header-wrapper {
  overflow: hidden;
  top: 0;
  z-index: 30;
  background-color: var(--fz-color-surface-light);
}

.fz-table__body-wrapper {
  flex-grow: 1;
}

.fz-table__header-wrapper--sticky-shadow {
  position: sticky;
  box-shadow: var(--fz-shadow-dropdown);
  transition: box-shadow var(--fz-transition-duration) var(--fz-transition-timing-function);
}

.fz-table__header-wrapper--sticky-shadow thead {
  border-color: transparent !important;
}

.fz-table-wrapper {
  position: relative;
}

.fz-table {
  display: grid;
  width: 100%;
}

.fz-table thead, .fz-table tbody {
  display: grid;
}

.fz-table--header {
  z-index: 11;
  position: relative;
}

.fz-table--header thead {
  border-bottom: 2px solid var(--fz-color-surface-background);
  margin: 0 var(--fz-spacing-10);
}

.fz-table--body tbody {
  padding: 0 var(--fz-spacing-10) var(--fz-spacing-7) var(--fz-spacing-10);
}

.fz-table th {
  position: relative;
  user-select: none;
  padding: var(--fz-spacing-6) var(--fz-spacing-4);
  background-color: var(--fz-color-surface-light);
}

.fz-table td {
  text-align: left;
  overflow: hidden;
  align-items: center;
  display: flex;
  border-bottom: 1px solid var(--fz-color-surface-background);
  padding: var(--fz-spacing-6) var(--fz-spacing-4);
  font: var(--fz-font-caption);
  color: var(--fz-color-text-primary);
}

.fz-table__header-block{
  display: flex;
  flex-direction: column;
  gap: var(--fz-spacing-2);
}

.fz-table__header-block-name{
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-2);
  font: var(--fz-font-caption-bold);
  color: var(--fz-color-text-primary);
  cursor: pointer;
}

.fz-table__filter-input{
  display: flex;
  align-items: center;
}

.fz-table__filter-input input{
  font: var(--fz-font-caption);
  color: var(--fz-color-text-primary);
  border: 0;
  padding: 0;
  outline: none;
  margin: 0;
  height: 16px;
  box-sizing: border-box;
  min-width: v-bind(minWidth);
  width: auto;
}

.fz-table__filter-input input::placeholder{
  font: var(--fz-font-caption);
  color: var(--fz-color-text-tertiary);
}

.fz-table__filter-input__icon{
  display: flex;
  color: var(--fz-color-border-icon-input);
}

.fz-table th .fz-table__sort-icon {
  color: var(--fz-color-border-icon-input);
  display: inline-block;
}

.fz-table__sort{
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--fz-color-text-link);
}

.fz-table__sort--none{
  display: flex;
  align-items: center;
  justify-content: center;
}

.fz-table__header-icon--filter{
  display: flex;
  color: var(--fz-color-text-link);
}

.fz-table__head tr{
  display: flex;
}

.fz-table__head th{
  flex: 1 0 auto;
}

.fz-table .fz-table__data-row {
  display: flex;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.fz-table .fz-table__data-row td{
  flex: 1 0 auto;
}

.fz-table .fz-table__data-row:hover:before {
  content: '';
  position: absolute;
  background: var(--fz-color-surface-brand-tablestring-hover);
  top: 0;
  bottom: 0;
  left: -10px;
  width: calc(var(--table-visible-width, 100%) - 20px);
  border-radius: var(--fz-spacing-4);
  z-index: -1;
  pointer-events: none;
  transform: translateX(var(--scroll-left, 0px));
}

.fz-table .fz-table__data-row td a {
  text-decoration: none;
  color: var(--fz-color-text-link);
}

.fz-table .fz-table__data-row td a:hover {
  text-decoration: underline;
  color: var(--fz-color-text-link-hover);
}

.fz-table__empty-row,
.fz-table__loading-row {
  overflow: hidden;
}

.fz-table__loading-content,
.fz-table__empty-content {
  width: 100%;
  text-align: center;
  padding: var(--fz-spacing-8);
  color: var(--fz-color-text-secondary);
  font: var(--fz-font-control-text-medium)
}

.fz-table__empty-content {
  margin-top: 1px;
}

.fz-table__loading-content{
  display: flex;
  justify-content: center;
}

/* Стили для индикатора загрузки дополнительных данных */
.fz-table__loading-more-row td {
  text-align: center;
  padding: var(--fz-spacing-4) !important;
  background-color: var(--fz-color-surface-light);
}

.fz-table__loading-more-content {
  display: flex;
  justify-content: center;
  align-items: center;
}
.fz-scroll-buttons-wrapper {
  position: sticky;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  padding: 0 16px;
  z-index: 20;
  height: 0;
}

.fz-scroll-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: auto;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 100px;
  padding: var(--fz-spacing-3);
  background: var(--fz-color-surface-light);
  box-shadow: var(--fz-shadow-floating);
  border: none;
  color: var(--fz-color-text-secondary);
  transition: var(--fz-transition-duration);
}

.fz-scroll-button:hover {
  color: var(--fz-color-text-primary);
}

.fz-scroll-button--left {
  left: -8px;
}

.fz-scroll-button--right {
  right: 12px;
}

.fz-fade-enter-active,
.fz-fade-leave-active {
  transition: opacity var(--fz-transition-duration-faster);
}

.fz-fade-enter-from,
.fz-fade-leave-to {
  opacity: 0;
}

.fz-table th.fz-table__option-column-header {
  background-color: var(--fz-color-surface-light);
  z-index: 10;
  flex: 0 0 auto;
  width: 80px;
}

.fz-table td.fz-table__option-column-cell {
  overflow: visible !important;
  position: sticky;
  right: 0;
  background-color: var(--fz-color-surface-light);
  z-index: 9;
  flex: 0 0 auto;
  width: 80px;
}

.fz-table .fz-table__data-row:hover .fz-table__option-column-cell{
  background: var(--fz-color-surface-brand-tablestring-hover);
}

  /* Тень для закрепленного столбца */
.fz-table__shadow--left {
  top: 0;
  z-index: 30;
  left: -10px;
  height: 100%;
  width: 10px;
  position: absolute;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%);
}

.fz-table .fz-table__data-row:hover .fz-table__shadow--left{
  display: none;
}

.fz-table__option-btn-block{
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-8);
  padding: 0 var(--fz-spacing-4);
}

.fz-table__option-btn-icon{
  display: flex;
  color: var(--fz-color-text-secondary);
  transition: color var(--fz-transition-duration);
  cursor: pointer;
}

.fz-table__option-btn-icon:hover{
  color: var(--fz-color-text-primary);
}

.fz-table__loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 15;
}

.fz-table__loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--fz-color-surface-light);
  margin-top: var(--fz-table-header-height);
  transform: translateY(var(--sticky-header-offset, 0px));
}

.fz-table-block--empty .fz-table__loading-content {
  margin-top: 0;
}

.fz-table-block--empty .fz-table__loading-overlay {
  position: relative;
}

.fz-table__loading-row .fz-table__loading-content {
  margin-top: 0;
}

/* Reduce opacity of rows during server loading */
.fz-table tbody.fz-table__body--loading .fz-table__data-row {
  opacity: 0;
  transition: opacity var(--fz-transition-duration-faster);
}
</style>
