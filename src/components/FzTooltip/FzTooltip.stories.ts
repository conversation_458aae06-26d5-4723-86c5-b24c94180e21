import type { Meta, StoryObj } from '@storybook/vue3'
import { FzButton } from '../FzButton'
import { FzIcon } from '../FzIcon'
import { FzTooltip } from './index'

const meta = {
  title: 'Components/FzTooltip',
  component: FzTooltip,
  tags: ['autodocs'],
  argTypes: {
    content: {
      control: 'text',
      description: 'The content of the tooltip',
    },
    position: {
      control: 'select',
      options: ['top', 'right', 'bottom', 'left'],
      description: 'The position of the tooltip relative to the trigger',
    },
    align: {
      control: 'select',
      options: ['start', 'center', 'end'],
      description: 'The alignment of the tooltip',
    },
    trigger: {
      control: 'select',
      options: ['hover', 'click', 'focus'],
      description: 'The event that triggers the tooltip',
    },
    delayDuration: {
      control: 'number',
      description: 'The delay before showing the tooltip (in ms)',
    },
    sideOffset: {
      control: 'number',
      description: 'The offset from the trigger element (in px)',
    },
    textAlign: {
      control: 'select',
      options: ['left', 'center', 'right'],
      description: 'The alignment of the text content',
    },
  },
  args: {
    content: 'This is a tooltip',
    position: 'top',
    align: 'center',
    trigger: 'hover',
    delayDuration: 0,
    sideOffset: 8,
    textAlign: 'center',
  },
} satisfies Meta<typeof FzTooltip>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:justify-center">
        <FzTooltip v-bind="args">
          <FzButton>Hover me</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}

export const WithIcon: Story = {
  render: args => ({
    components: { FzTooltip, FzIcon },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:justify-center">
        <FzTooltip v-bind="args">
          <FzIcon name="info" clickable />
        </FzTooltip>
      </div>
    `,
  }),
}

export const Positions: Story = {
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:grid fz:grid-cols-3 fz:gap-4 fz:place-items-center">
        <div></div>
        <FzTooltip content="Top tooltip" position="top">
          <FzButton>Top</FzButton>
        </FzTooltip>
        <div></div>
        
        <FzTooltip content="Left tooltip" position="left">
          <FzButton>Left</FzButton>
        </FzTooltip>
        
        <div></div>
        
        <FzTooltip content="Right tooltip" position="right">
          <FzButton>Right</FzButton>
        </FzTooltip>
        
        <div></div>
        <FzTooltip content="Bottom tooltip" position="bottom">
          <FzButton>Bottom</FzButton>
        </FzTooltip>
        <div></div>
      </div>
    `,
  }),
}

export const Alignments: Story = {
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:flex-col fz:gap-4 fz:items-center">
        <FzTooltip content="Start aligned tooltip" position="top" align="start">
          <FzButton>Align Start</FzButton>
        </FzTooltip>
        
        <FzTooltip content="Center aligned tooltip" position="top" align="center">
          <FzButton>Align Center</FzButton>
        </FzTooltip>
        
        <FzTooltip content="End aligned tooltip" position="top" align="end">
          <FzButton>Align End</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}

export const WithDelay: Story = {
  args: {
    content: 'This tooltip has a 500ms delay',
    delayDuration: 500,
  },
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:justify-center">
        <FzTooltip v-bind="args">
          <FzButton>Hover me (500ms delay)</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}

export const LongContent: Story = {
  args: {
    content: 'This is a tooltip with a very long content that should wrap to multiple lines. It demonstrates how the tooltip handles longer text content and maintains readability.',
  },
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:justify-center">
        <FzTooltip v-bind="args">
          <FzButton>Hover me (long content)</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}

export const ClickTrigger: Story = {
  args: {
    content: 'This tooltip is triggered by click',
    trigger: 'click',
  },
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:justify-center">
        <FzTooltip v-bind="args">
          <FzButton>Click me</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}

export const FocusTrigger: Story = {
  render: args => ({
    components: { FzTooltip },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:justify-center">
        <FzTooltip content="This tooltip is triggered by focus" trigger="focus">
          <input 
            type="text" 
            placeholder="Focus me" 
            class="fz:border fz:border-solid fz:border-gray-300 fz:rounded fz:p-2"
          />
        </FzTooltip>
      </div>
    `,
  }),
}

export const TextAlign: Story = {
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:flex-col fz:gap-4 fz:items-center">
        <FzTooltip content="Left aligned text." position="top" align="center" textAlign="left">
          <FzButton>Text Left</FzButton>
        </FzTooltip>
        
        <FzTooltip content="Center aligned text." position="top" align="center" textAlign="center">
          <FzButton>Text Center</FzButton>
        </FzTooltip>
        
        <FzTooltip content="Right aligned text. This content is a bit longer to demonstrate how it behaves with more text." position="top" align="center" textAlign="right">
          <FzButton>Text Right</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}

export const SlotContent: Story = {
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:flex-col fz:gap-4 fz:items-center">
        <FzTooltip position="top">
          <template #content>
            <div style="display: flex; align-items: center; gap: 4px;">
              <span style="font-weight: bold; color: #22c55e;">Parent Category</span>
              <span style="opacity: 0.7;"> > </span>
              <span>Child Item</span>
            </div>
          </template>
          <FzButton>Styled Path Tooltip</FzButton>
        </FzTooltip>
        
        <FzTooltip position="top">
          <template #content>
            <div>
              <div style="font-weight: bold; margin-bottom: 4px;">Complex Content</div>
              <div style="font-size: 0.875em; opacity: 0.8;">
                This tooltip contains<br/>
                <strong>HTML content</strong> with<br/>
                <em>multiple lines</em>
              </div>
            </div>
          </template>
          <FzButton>Rich HTML Content</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}

export const DisabledTooltip: Story = {
  render: args => ({
    components: { FzTooltip, FzButton },
    setup() {
      return { args }
    },
    template: `
      <div class="fz:p-16 fz:flex fz:flex-col fz:gap-4 fz:items-center">
        <FzTooltip content="This tooltip is enabled" position="top">
          <FzButton>Enabled Tooltip</FzButton>
        </FzTooltip>
        
        <FzTooltip content="This tooltip is disabled" position="top" :disabled="true">
          <FzButton>Disabled Tooltip</FzButton>
        </FzTooltip>
        
        <FzTooltip position="top" :disabled="false">
          <template #content>
            <div style="display: flex; align-items: center; gap: 4px;">
              <span style="font-weight: bold;">Enabled</span>
              <span style="opacity: 0.7;"> > </span>
              <span>With Slot Content</span>
            </div>
          </template>
          <FzButton>Enabled with Slot</FzButton>
        </FzTooltip>
        
        <FzTooltip position="top" :disabled="true">
          <template #content>
            <div>This slot content won't show</div>
          </template>
          <FzButton>Disabled with Slot</FzButton>
        </FzTooltip>
      </div>
    `,
  }),
}
