<script setup lang="ts">
import {
  <PERSON>ltipArrow,
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
} from 'reka-ui'
import { computed, ref, watch } from 'vue'

defineOptions({ name: 'FzTooltip' })

const props = withDefaults(defineProps<Props>(), {
  position: 'top',
  align: 'center',
  trigger: 'hover',
  delayDuration: 100,
  sideOffset: 6,
  hasArrow: true,
  textAlign: 'center',
  disabled: false,
})

export interface Props {
  content?: string
  position?: 'top' | 'right' | 'bottom' | 'left'
  align?: 'start' | 'center' | 'end'
  trigger?: 'hover' | 'click' | 'focus'
  delayDuration?: number
  sideOffset?: number
  hasArrow?: boolean
  textAlign?: 'left' | 'center' | 'right'
  disabled?: boolean
}

const side = computed(() => props.position)
const align = computed(() => props.align)
const triggerType = computed(() => props.trigger)
const delay = computed(() => props.delayDuration)
const offset = computed(() => props.sideOffset)
const hasArrow = computed(() => props.hasArrow)
const textAlign = computed(() => props.textAlign)

const isTooltipOpen = ref(false)
const isClickTrigger = computed(() => props.trigger === 'click')

// Close tooltip if trigger type changes from click to something else
watch(isClickTrigger, (isClick) => {
  if (!isClick) {
    isTooltipOpen.value = false
  }
})

function handleClick() {
  if (isClickTrigger.value) {
    isTooltipOpen.value = !isTooltipOpen.value
  }
}
</script>

<template>
  <TooltipProvider v-if="!disabled" :delay-duration="delay">
    <TooltipRoot :open="isClickTrigger ? isTooltipOpen : undefined">
      <TooltipTrigger as-child @click="handleClick">
        <slot />
      </TooltipTrigger>

      <TooltipPortal>
        <TooltipContent
          class="fz-tooltip"
          :side="side"
          :align="align"
          :side-offset="offset"
          :data-trigger="triggerType"
          :style="{ '--fz-tooltip-text-align': textAlign }"
        >
          <div class="fz-tooltip__content-wrapper">
            <div class="fz-tooltip__content">
              <slot name="content">
                <span v-if="content">{{ content }}</span>
              </slot>
            </div>
            <TooltipArrow v-if="hasArrow" class="fz-tooltip__arrow" :width="12" :height="6" />
          </div>
        </TooltipContent>
      </TooltipPortal>
    </TooltipRoot>
  </TooltipProvider>
  <template v-else>
    <slot />
  </template>
</template>

<style>
.fz-tooltip {
  z-index: 150;
  animation-duration: var(--fz-transition-duration);
  animation-timing-function: var(--fz-transition-timing-function);
  transform-origin: var(--reka-tooltip-content-transform-origin, center);
  will-change: transform;
}

.fz-tooltip__content-wrapper {
  display: flex;
  align-items: center;
  gap: var(--fz-spacing-2);
  background: var(--fz-color-surface-light);
  color: var(--fz-color-text-primary);
  border-radius: var(--fz-radius-md);
  box-shadow: var(--fz-shadow-dropdown);
  padding: var(--fz-spacing-6) var(--fz-spacing-8);
  font: var(--fz-font-hint);
  min-width: 120px;
  max-width: 240px;
  width: auto;
  box-sizing: border-box;
  text-align: var(--fz-tooltip-text-align, center);
}

.fz-tooltip[data-side="top"] {
  animation-name: slideUp;
}
.fz-tooltip[data-side="bottom"] {
  animation-name: slideDown;
}
.fz-tooltip[data-side="left"] {
  animation-name: slideLeft;
}
.fz-tooltip[data-side="right"] {
  animation-name: slideRight;
}
.fz-tooltip__content {
  display: block;
  word-break: break-word;
  width: 100%;
}

.fz-tooltip__content * {
  font: inherit;
  color: inherit;
}
.fz-tooltip__arrow {
  fill: var(--fz-color-surface-light);
}

.fz-tooltip[data-side="left"] .fz-tooltip__arrow {
  margin-right: 12px;
}

.fz-tooltip[data-side="right"] .fz-tooltip__arrow {
  margin-left: 12px;
}

.fz-tooltip[data-side="left"] .fz-tooltip__arrow {
  transform: translateX(50%);
}

.fz-tooltip[data-side="right"] .fz-tooltip__arrow {
  transform: translateX(-50%);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(4px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-4px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
