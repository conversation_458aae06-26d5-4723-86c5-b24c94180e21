import type { Meta, StoryObj } from '@storybook/vue3'
import <PERSON>z<PERSON><PERSON> from './FzLogo.vue'

const meta = {
  title: 'Components/FzLogo',
  component: FzLogo,
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: 'color',
      description: 'Color of the logo',
    },
  },
  args: {
    color: 'currentColor',
  },
} satisfies Meta<typeof FzLogo>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => ({
    components: { FzLogo },
    setup() {
      return { args }
    },
    template: '<FzLogo v-bind="args" />',
  }),
}

export const ColoredLogo: Story = {
  args: {
    color: '#4167f1',
  },
}
