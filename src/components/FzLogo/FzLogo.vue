<script setup lang="ts">
export interface Props {
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  color: 'currentColor',
})
</script>

<template>
  <div class="fz-logo" aria-label="ФОКУЗ" :style="{ color: props.color }">
    <svg class="fz-logo__svg" xmlns="http://www.w3.org/2000/svg" width="75" height="16" fill="none" viewBox="0 0 75 16">
      <path fill="currentColor" d="M37.484 15.155a.5.5 0 0 1-.368-.143.498.498 0 0 1-.144-.368V1.353a.5.5 0 0 1 .144-.369.5.5 0 0 1 .368-.143h2.235a.5.5 0 0 1 .369.143.497.497 0 0 1 .143.369v5.112h1.517l3.177-5.235a.772.772 0 0 1 .287-.266.835.835 0 0 1 .472-.123h2.28c.138 0 .24.048.308.143a.392.392 0 0 1 .123.287.44.44 0 0 1-.082.245L44.48 7.732l4.223 6.728a.51.51 0 0 1 .04.225.473.473 0 0 1-.143.348.418.418 0 0 1-.307.122H45.93c-.205 0-.355-.04-.451-.122a3.111 3.111 0 0 1-.226-.246l-3.382-5.419h-1.64v5.276c0 .15-.047.273-.143.368a.5.5 0 0 1-.37.143h-2.234Z" />
      <path fill="currentColor" fill-rule="evenodd" d="M27.286 15.527c4.31 0 7.492-3.062 7.492-7.575 0-4.573-3.183-7.454-7.492-7.454-4.29 0-7.473 2.88-7.473 7.454 0 4.513 3.184 7.575 7.473 7.575Zm0-12.105c2.338 0 3.954 1.937 3.954 4.549 0 2.695-1.636 4.632-3.954 4.632-2.318 0-3.934-1.937-3.934-4.632 0-2.612 1.597-4.55 3.934-4.55Z" clip-rule="evenodd" />
      <path fill="currentColor" d="M69.044 15.5c-.983 0-1.836-.109-2.559-.327-.723-.217-1.317-.51-1.78-.878a3.963 3.963 0 0 1-1.065-1.245 4.125 4.125 0 0 1-.45-1.41.333.333 0 0 1 .122-.306.49.49 0 0 1 .328-.122h2.21c.15 0 .273.034.369.102a.78.78 0 0 1 .266.286c.187.31.43.694.84.94.409.23.961.346 1.657.346.532 0 .996-.068 1.392-.204.396-.15.703-.36.921-.633.232-.272.348-.599.348-1.098 0-.6-.225-1.105-.675-1.418-.437-.313-1.044-.47-1.822-.47h-1.494a.5.5 0 0 1-.369-.143.57.57 0 0 1-.143-.388V7.044c0-.15.048-.28.143-.388a.468.468 0 0 1 .369-.164h1.433c.64 0 1.16-.129 1.555-.388.41-.272.614-.7.614-1.286 0-.327-.095-.613-.286-.858a1.8 1.8 0 0 0-.778-.592c-.328-.136-.717-.204-1.167-.204-.668 0-1.207.109-1.617.327-.41.218-.669.544-.778.98a.597.597 0 0 1-.204.286.626.626 0 0 1-.348.081h-2.293a.49.49 0 0 1-.327-.122.355.355 0 0 1-.103-.306c.041-.49.178-.967.41-1.43a3.78 3.78 0 0 1 1.044-1.245c.45-.368 1.03-.66 1.74-.878.709-.218 1.555-.327 2.538-.327 1.16 0 2.142.19 2.947.572.819.367 1.44.857 1.863 1.47a3.26 3.26 0 0 1 .655 1.96c0 .354-.048.722-.144 1.103a3.176 3.176 0 0 1-.49 1.041c-.22.313-.547.692-.983.937.518.245.92.552 1.207.92.3.367.519.761.655 1.183.137.422.205.908.205 1.357 0 .858-.239 1.725-.716 2.365-.478.64-1.167 1.143-2.068 1.51-.887.368-1.944.552-3.172.552Z" />
      <path fill="currentColor" fill-rule="evenodd" d="M17.876 7.902c0 3.985-2.774 6.69-6.513 6.69h-.868v.919a.492.492 0 0 1-.491.489H7.87a.49.49 0 0 1-.49-.49v-.918h-.868C2.775 14.592 0 11.887 0 7.902 0 3.863 2.775 1.32 6.513 1.32h.867V.491c0-.242.196-.491.49-.491h2.135c.261 0 .49.212.49.49v.83h.868c3.739 0 6.513 2.543 6.513 6.582ZM3.28 7.92c0-2.235 1.393-3.932 3.43-3.932h.67v7.938h-.67c-2.02 0-3.43-1.698-3.43-4.006Zm11.318 0c0-2.235-1.393-3.932-3.43-3.932h-.672v7.938h.672c2.02 0 3.43-1.698 3.43-4.006Z" clip-rule="evenodd" />
      <path fill="currentColor" d="m51.424 13.468.957 1.506a.587.587 0 0 0 .5.278h1.051c2.527 0 3.684-2.705 4.325-4.17.64-1.467 4.1-9.466 4.1-9.466a.802.802 0 0 0 .06-.163.502.502 0 0 0 .042-.184.368.368 0 0 0-.144-.287.389.389 0 0 0-.307-.143h-2.176a.683.683 0 0 0-.369.103.67.67 0 0 0-.225.306l-2.753 6.353-3.294-6.353a.815.815 0 0 0-.246-.306.497.497 0 0 0-.328-.103h-2.299a.452.452 0 0 0-.328.143.43.43 0 0 0-.144.307c0 .**************.184l5.114 9.53-.037.078c-.327.712-.828 1.746-2.116 1.702-1.288-.043-.608 0-1.042 0-.352 0-.55.405-.383.685Z" />
    </svg>
  </div>
</template>

<style>
.fz-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fz-logo__svg {
  display: block;
}
</style>
