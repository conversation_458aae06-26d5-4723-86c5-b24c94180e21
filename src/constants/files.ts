/**
 * Типы файлов, которые разрешены для загрузки в приложении
 * Эти форматы разрешены для загрузки и поддерживаются для предпросмотра в браузерах
 */
export const ALLOWED_FILE_TYPES = {
  image: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  video: ['mp4', 'wmv', 'mov', '3gp', 'flv', 'webm', 'avi'],
  audio: ['mp3', 'ogg', 'wav', 'm4a', 'ogg', 'aac'],
}

/**
 * Типы файлов, которые разрешены для загрузки, но не поддерживаются для предпросмотра в браузерах
 * Эти форматы можно загрузить, но они будут отображаться с сообщением "Формат не поддерживается"
 */
export const ALLOWED_BUT_NOT_SUPPORTED = {
  image: ['heic'],
  audio: ['amr'],
  video: [],
}

export const ALLOWED_IMAGE_PREVIEW_FORMATS = [
  'jpg',
  'jpeg',
  'png',
  'gif',
  'webp',
  'svg',
]
