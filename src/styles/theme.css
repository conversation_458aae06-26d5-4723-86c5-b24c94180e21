/**
 * Foquz UI Theme System
 * 
 * Typography Usage:
 * -----------------
 * You can use the font: property with our typography variables for quick styling:
 *  
 * Examples:
 * .my-heading { font: var(--fz-font-h1); }
 * .my-button { font: var(--fz-font-button-medium); }
 * .my-caption { font: var(--fz-font-caption); }
 * 
 * Available font variables:
 * --fz-font-h1
 * --fz-font-h2
 * --fz-font-h2-medium
 * --fz-font-h3
 * --fz-font-body
 * --fz-font-body-bold
 * --fz-font-body-medium
 * --fz-font-button-large
 * --fz-font-button-medium
 * --fz-font-button-small
 * --fz-font-caption
 * --fz-font-caption-bold
 * --fz-font-subtitle
 * --fz-font-label
 * --fz-font-control-text
 * --fz-font-hint
 * --fz-font-hint-medium
 * --fz-font-modal-title
 * --fz-font-question-title
 */

@theme {
  --spacing: 2px;

  --spacing-0: --spacing(0);
  --spacing-1: --spacing(1);
  --spacing-2: --spacing(2);
  --spacing-3: --spacing(3);
  --spacing-4: --spacing(4);
  --spacing-5: --spacing(5);
  --spacing-6: --spacing(6);
  --spacing-7: --spacing(7);
  --spacing-8: --spacing(8);
  --spacing-9: --spacing(9);
  --spacing-10: --spacing(10);
  --spacing-11: --spacing(11);
  --spacing-12: --spacing(12);
  --spacing-13: --spacing(13);
  --spacing-14: --spacing(14);
  --spacing-15: --spacing(15);
  --spacing-16: --spacing(16);
  --spacing-17: --spacing(17);
  --spacing-18: --spacing(18);
  --spacing-19: --spacing(19);
  --spacing-20: --spacing(20);
  --spacing-21: --spacing(21);
  --spacing-22: --spacing(22);
  --spacing-23: --spacing(23);
  --spacing-24: --spacing(24);
  --spacing-25: --spacing(25);
  --spacing-26: --spacing(26);
  --spacing-27: --spacing(27);
  --spacing-28: --spacing(28);
  --spacing-29: --spacing(29);
  --spacing-30: --spacing(30);
  --spacing-31: --spacing(31);
  --spacing-32: --spacing(32);
  --spacing-33: --spacing(33);
  --spacing-34: --spacing(34);
  --spacing-35: --spacing(35);
  --spacing-36: --spacing(36);
  --spacing-37: --spacing(37);
  --spacing-38: --spacing(38);
  --spacing-39: --spacing(39);
  --spacing-40: --spacing(40);
  --spacing-41: --spacing(41);
  --spacing-42: --spacing(42);
  --spacing-43: --spacing(43);
  --spacing-44: --spacing(44);
  --spacing-45: --spacing(45);
  --spacing-46: --spacing(46);
  --spacing-47: --spacing(47);
  --spacing-48: --spacing(48);
  --spacing-49: --spacing(49);
  --spacing-50: --spacing(50);

  --font-family-roboto: 'Roboto', sans-serif;

  --breakpoint-*: initial;
  --breakpoint-xs: 340px;
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  --breakpoint-3xl: 1920px;

  /* Deprecated typography variables - use font shorthand variables instead */
  --typography-button-large: var(--fz-font-button-large);
  --typography-button-medium: var(--fz-font-button-medium);
  --typography-button-small: var(--fz-font-button-small);

  /* Typography Font Shorthand Variables */
  --font-h1: var(--fz-font-weight-bold) var(--fz-font-size-lg)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-h2: var(--fz-font-weight-bold) var(--fz-font-size-md)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-h2-medium: var(--fz-font-weight-medium) var(--fz-font-size-md)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-h3: var(--fz-font-weight-bold) var(--fz-font-size-sm)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-body: var(--fz-font-weight-regular) var(--fz-font-size-sm)/var(--fz-line-height-normal) var(--fz-font-family-roboto);
  --font-body-bold: var(--fz-font-weight-bold) var(--fz-font-size-sm)/var(--fz-line-height-normal) var(--fz-font-family-roboto);
  --font-body-medium: var(--fz-font-weight-medium) var(--fz-font-size-sm)/var(--fz-line-height-normal) var(--fz-font-family-roboto);
  --font-button-large: var(--fz-font-weight-medium) var(--fz-font-size-sm)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-button-medium: var(--fz-font-weight-medium) var(--fz-font-size-xs)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-button-small: var(--fz-font-weight-medium) var(--fz-font-size-xxs)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-caption: var(--fz-font-weight-regular) var(--fz-font-size-xxs)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-caption-bold: var(--fz-font-weight-bold) var(--fz-font-size-xxs)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-subtitle: var(--fz-font-weight-regular) var(--fz-font-size-xs)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-label: var(--fz-font-weight-bold) var(--fz-font-size-xs)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-control-text-big: var(--fz-font-weight-regular) var(--fz-font-size-sm)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-control-text-medium: var(--fz-font-weight-regular) var(--fz-font-size-xs)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-hint: var(--fz-font-weight-regular) var(--fz-font-size-xxs)/var(--fz-line-height-normal) var(--fz-font-family-roboto);
  --font-hint-medium: var(--fz-font-weight-medium) var(--fz-font-size-xxs)/var(--fz-line-height-normal) var(--fz-font-family-roboto);
  --font-modal-title: var(--fz-font-weight-bold) var(--fz-font-size-md)/var(--fz-line-height-tight) var(--fz-font-family-roboto);
  --font-modal-text: var(--fz-font-weight-regular) var(--fz-font-size-xs)/var(--fz-line-height-normal) var(--fz-font-family-roboto);
  --font-question-title: var(--fz-font-weight-medium) var(--fz-font-size-md)/var(--fz-line-height-tight) var(--fz-font-family-roboto);

  /* Brand Colors */
  --color-brand-1: #020618;
  --color-brand-2: #030D30;
  --color-brand-3: #051347;
  --color-brand-4: #071A5F;
  --color-brand-5: #092077;
  --color-brand-6: #0A278F;
  --color-brand-7: #0C2DA6;
  --color-brand-8: #0E33BE;
  --color-brand-9: #103AD6;
  --color-brand-10: #1140EE;
  --color-brand-11: #2953EF;
  --color-brand-12: #3F65F1;
  --color-brand-13: #597AF3;
  --color-brand-14: #708DF5;
  --color-brand-15: #88A0F6;
  --color-brand-16: #A0B3F8;
  --color-brand-17: #B8C6FA;
  --color-brand-18: #CFD9FC;
  --color-brand-19: #E7ECFD;
  --color-brand-20: #F1F4FE;

  /* Neutral Colors */
  --color-neutral-1: #0A0C0F;
  --color-neutral-2: #14181F;
  --color-neutral-3: #1E232E;
  --color-neutral-4: #29303D;
  --color-neutral-5: #333B4C;
  --color-neutral-6: #3D475C;
  --color-neutral-7: #47536B;
  --color-neutral-8: #525F7A;
  --color-neutral-9: #5C6B89;
  --color-neutral-10: #667799; /* From fill_JTRMG4 */
  --color-neutral-11: #7584A3;
  --color-neutral-12: #8592AD;
  --color-neutral-13: #94A0B8;
  --color-neutral-14: #A3ADC2;
  --color-neutral-15: #B2BBCC;
  --color-neutral-16: #C2C9D6;
  --color-neutral-17: #D1D6E0;
  --color-neutral-18: #E0E4EB;
  --color-neutral-19: #EAECF1;
  --color-neutral-20: #EFF1F5;
  --color-neutral-21: #FFFFFF; /* From fill_2NDRZT */

  /* Critical Colors */
  --color-critical-1: #190101;
  --color-critical-2: #320201;
  --color-critical-3: #4B0202;
  --color-critical-4: #630303;
  --color-critical-5: #7C0403;
  --color-critical-6: #950504;
  --color-critical-7: #AE0605;
  --color-critical-8: #C70705;
  --color-critical-9: #DF0706;
  --color-critical-10: #F80807;
  --color-critical-11: #F92120;
  --color-critical-12: #FA3A38;
  --color-critical-13: #FA5251;
  --color-critical-14: #FB6B6A;
  --color-critical-15: #FC8483;
  --color-critical-16: #FC9C9C;
  --color-critical-17: #FDB5B4;
  --color-critical-18: #FECECD;
  --color-critical-19: #FEE6E6;

  /* Success Colors */
  --color-success-1: #061309;
  --color-success-2: #0D2611;
  --color-success-3: #133A19;
  --color-success-4: #194D22;
  --color-success-5: #20602A;
  --color-success-6: #267333;
  --color-success-7: #2C863B;
  --color-success-8: #339943;
  --color-success-9: #39AD4D;
  --color-success-10: #3FC054;
  --color-success-11: #52C665;
  --color-success-12: #66CC76;
  --color-success-13: #79D387;
  --color-success-14: #8CD999;
  --color-success-15: #9FDFAA;
  --color-success-16: #B2E6BB;
  --color-success-17: #C5ECCC;
  --color-success-18: #D9F2DD;
  --color-success-19: #ECF9EE;

  /* Datavis Colors */
  --color-datavis-1: #FA5251; /* Also in Critical */
  --color-datavis-2: #CAAD46;
  --color-datavis-3: #FFCC00;
  --color-datavis-4: #16CEB9;
  --color-datavis-5: #2D99FF;
  --color-datavis-6: #8400FF;

  /* Text Colors */
  --color-text-primary: var(--fz-color-neutral-4);
  --color-text-secondary: var(--fz-color-neutral-10); /* Alias for service */
  --color-text-tertiary: var(--fz-color-neutral-13); /* Alias for other_service */
  --color-text-link: var(--fz-color-brand-12);
  --color-text-link-hover: var(--fz-color-brand-11);
  --color-text-link-visited: var(--fz-color-brand-8); /* Mapped from link_clicked */
  --color-text-success: var(--fz-color-success-7);
  --color-text-critical: var(--fz-color-critical-12);
  --color-text-inverse: var(--fz-color-neutral-21);

  /* Surface Colors */
  --color-surface-light: var(--fz-color-neutral-21); /* Alias for blocks_light background */
  --color-surface-dark: var(--fz-color-neutral-4); /* Alias for blocks_dark background */
  --color-surface-background: var(--fz-color-neutral-20);
  --color-surface-disabled: var(--fz-color-neutral-20); /* Alias for disabled_inputs */
  --color-surface-secondary: var(--fz-color-neutral-18);
  --color-surface-secondary-hover: var(--fz-color-neutral-16);
  --color-surface-secondary-clicked: var(--fz-color-neutral-15);
  --color-surface-notice: var(--fz-color-brand-18);
  --color-surface-ghost: var(--fz-color-neutral-21);
  --color-surface-ghost-hover: var(--fz-color-neutral-20);
  --color-surface-ghost-clicked: var(--fz-color-neutral-18);
  --color-surface-overlay-dark: rgba(10, 12, 15, 0.5); /* Mapped from shading_sidesheet */


  /* Surface Colors - Brand */
  --color-surface-brand-primary: var(--fz-color-brand-12);
  --color-surface-brand-primary-hover: var(--fz-color-brand-11);
  --color-surface-brand-primary-clicked: var(--fz-color-brand-9); /* Matches hover in Figma */
  --color-surface-brand-tablestring-hover: var(--fz-color-brand-20); /* Matches hover in Figma */

  /* Surface Colors - Success */
  --color-surface-success-primary: var(--fz-color-success-17);
  --color-surface-success-primary-hover: var(--fz-color-success-15);
  --color-surface-success-primary-clicked: var(--fz-color-success-14);
  --color-surface-success-secondary: var(--fz-color-success-17);
  --color-surface-success-secondary-hover: var(--fz-color-success-15);
  --color-surface-success-secondary-clicked: var(--fz-color-success-14);

  /* Surface Colors - Critical */
  --color-surface-critical-primary: var(--fz-color-critical-18);
  --color-surface-critical-primary-hover: var(--fz-color-critical-16);
  --color-surface-critical-primary-clicked: var(--fz-color-critical-15);
  --color-surface-critical-secondary: var(--fz-color-critical-18);
  --color-surface-critical-secondary-hover: var(--fz-color-critical-16);
  --color-surface-critical-secondary-clicked: var(--fz-color-critical-15);

  /* Border Colors */
  --color-border-divider: var(--fz-color-neutral-18);
  --color-border-input: var(--fz-color-neutral-17);
  --color-border-input-focus: var(--fz-color-brand-12);
  --color-border-input-success: var(--fz-color-success-8); /* Alias for verified */
  --color-border-input-error: var(--fz-color-critical-12);
  --color-border-icon: var(--fz-color-neutral-10);
  --color-border-icon-active: var(--fz-color-neutral-4); /* Alias for chosen */
  --color-border-icon-success: var(--fz-color-success-8);
  --color-border-icon-critical: var(--fz-color-critical-12);
  --color-border-icon-input: var(--fz-color-neutral-14);
  --color-border-inverse: var(--fz-color-neutral-21);
  --color-border-button-primary-focused: rgba(63, 101, 241, 0.4);
  --color-border-button-secondary-focused: rgba(224, 228, 235, 0.4);
  --color-border-button-critical-focused: rgba(254, 206, 205, 0.4);
  --color-border-button-success-focused: rgba(198, 236, 204, 0.4);

  /* Shadows */
  --shadow-floating: 0px 2px 8px 0px rgba(30, 35, 46, 0.3);
  --shadow-dropdown: 0px 4px 12px 0px rgba(30, 35, 46, 0.15);
  
  /* Ring focus styles */
  --ring-offset-width: 2px;
  --ring-width: 4px;
  --ring-offset-color: var(--fz-color-surface-background);
  --ring-default-color: var(--fz-color-surface-brand-primary);
  --ring-primary-color: var(--fz-color-surface-brand-primary);
  --ring-success-color: var(--fz-color-surface-success-primary);
  --ring-secondary-color: var(--fz-color-surface-secondary);
  --ring-critical-color: var(--fz-color-surface-critical-primary);
  --ring-ghost-color: var(--fz-color-surface-ghost);

  /* Font Sizes */
  --font-size-xxs: 12px; /* From Figma: 12px */
  --font-size-xs: 14px; /* From Figma: 14px */
  --font-size-sm: 16px; /* From Figma: 16px */
  --font-size-md: 20px; /* From Figma: 20px */
  --font-size-lg: 24px; /* From Figma: 24px */
  --font-size-xl: 30px; /* From Figma: 30px */

  /* Font Weights */
  --font-weight-regular: 400; /* From Figma: 400 */
  --font-weight-medium: 500; /* From Figma: 500 */
  --font-weight-bold: 700; /* From Figma: 700 */

  /* Line Heights */
  --line-height-tight: 1.1; /* From Figma: 1.1 */
  --line-height-normal: 1.2; /* From Figma: 1.2 */
  --line-height-loose: 1.3; /* From Figma: 1.3 */

  /* Radius */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 12px;

  /* Transitions */
  --transition-duration: 0.3s;
  --transition-timing-function: ease;
  --transition-duration-faster: 0.2s;
  --transition-timing-function-bounce: cubic-bezier(0.68, -0.55, 0.27, 1.55)
}

@layer components {
  .gradient-scroll-vertical-top {
    background: linear-gradient(360deg, var(--fz-surface-light), #FFFFFF 100%);
  }

  .gradient-scroll-vertical-bottom {
    background: linear-gradient(180deg, var(--fz-surface-light), #FFFFFF 100%);
  }

  .gradient-scroll-horizontal-left {
    background: linear-gradient(90deg, var(--fz-surface-light), #FFFFFF 100%);
  }

  .gradient-scroll-horizontal-right {
    background: linear-gradient(-90deg, var(--fz-surface-light), #FFFFFF 100%);
  }

  /* Typography utility classes based on Figma */
  .typography-h1 {
    font: var(--fz-font-h1);
  }

  .typography-h2, .typography-modal-title {
    font: var(--fz-font-h2);
  }

  .typography-h2-medium, .typography-question-title {
    font: var(--fz-font-h2-medium);
  }

  .typography-h3 {
    font: var(--fz-font-h3);
  }

  .typography-body {
    font: var(--fz-font-body);
  }

  .typography-body-bold {
    font: var(--fz-font-body-bold);
  }

  .typography-body-medium {
    font: var(--fz-font-body-medium);
  }

  .typography-button-large {
    font: var(--fz-font-button-large);
  }

  .typography-button-medium {
    font: var(--fz-font-button-medium);
  }

  .typography-button-small {
    font: var(--fz-font-button-small);
  }

  .typography-caption {
    font: var(--fz-font-caption);
  }

  .typography-caption-bold {
    font: var(--fz-font-caption-bold);
  }

  .typography-subtitle {
    font: var(--fz-font-subtitle);
  }

  .typography-label {
    font: var(--fz-font-label);
  }

  .typography-control-text {
    font: var(--fz-font-control-text);
  }

  .typography-hint {
    font: var(--fz-font-hint);
  }

  .typography-hint-medium {
    font: var(--fz-font-hint-medium);
  }

  /* Dropdown animation keyframes */
  @keyframes fz-dropdown-fade-in-up {
    from { opacity: 0; transform: translateY(4px); }
    to   { opacity: 1; transform: translateY(0);    }
  }
  
  @keyframes fz-dropdown-fade-out-down {
    from { opacity: 1; transform: translateY(0);  }
    to   { opacity: 0; transform: translateY(4px);    }
  }
  
  /* Simple fade animations */
  @keyframes fz-dropdown-fade-in  { from { opacity: 0 } to { opacity: 1 } }
  @keyframes fz-dropdown-fade-out { from { opacity: 1 } to { opacity: 0 } }

  @keyframes fz-zoom-in-95 {
    from { transform: scale(0.95); opacity: 0; }
    to   { transform: scale(1); opacity: 1; }
  }

  @keyframes fz-zoom-out-95 {
    from { transform: scale(1); opacity: 1; }
    to   { transform: scale(0.95); opacity: 0; }
  }

  /* Open/closed animations targeting data-state + data-side */
  .fz-dropdown__content[data-state="open"][data-side="bottom"] {
    animation: fz-zoom-in-95 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .fz-dropdown__content[data-state="closed"][data-side="bottom"] {
    animation: fz-zoom-out-95 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .fz-dropdown__content[data-state="closed"][data-side="top"] {
    animation: fz-zoom-out-95 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .fz-dropdown__content[data-state="open"][data-side="top"] {
    animation: fz-zoom-in-95 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

}
