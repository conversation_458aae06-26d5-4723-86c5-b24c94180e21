import { ref } from 'vue'

interface UseFileLoaderOptions {
  maxSize: number
  allowedTypes: string[]
}

export function useFileLoader(options: UseFileLoaderOptions) {
  const { maxSize, allowedTypes } = options

  const file = ref<File | null>(null)
  const previewUrl = ref<string | null>(null)
  const error = ref<string | null>(null)
  const isLoading = ref<boolean>(false)

  const invalid = ref<boolean>(false)
  const success = ref<boolean>(false)

  function validate(selectedFile: File): boolean {
    error.value = null

    const isTypeValid = allowedTypes.some((type) => {
      if (type.endsWith('/*')) {
        const category = type.split('/')[0]
        return selectedFile.type.startsWith(`${category}/`)
      }

      return (
        selectedFile.type === type
        || selectedFile.name.toLowerCase().endsWith(`.${type.toLowerCase()}`)
      )
    })

    if (!isTypeValid) {
      error.value = `Недопустимый тип файла. Разрешённые типы: ${allowedTypes.join(', ')}`
      return false
    }

    if (selectedFile.size > maxSize) {
      const sizeInMB = (maxSize / (1024 * 1024)).toFixed(0)
      error.value = `Файл слишком большой. Максимальный размер: ${sizeInMB} MB`
      return false
    }

    return true
  }

  async function loadFile(selectedFile: File): Promise<boolean> {
    // Reset error state before validating new file
    error.value = null

    if (!validate(selectedFile)) {
      return false
    }

    isLoading.value = true
    file.value = selectedFile

    if (selectedFile.type.startsWith('image/')) {
      previewUrl.value = await readFileAsDataURL(selectedFile)
    }
    else {
      previewUrl.value = null
    }

    isLoading.value = false
    return true
  }

  function reset() {
    file.value = null
    previewUrl.value = null
    error.value = null
    isLoading.value = false
  }

  function readFileAsDataURL(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  function clearError() {
    error.value = null
  }

  return {
    file,
    previewUrl,
    error,
    isLoading,
    invalid,
    success,
    loadFile,
    reset,
    clearError,
  }
}
