/**
 * @file useFancybox.js
 * @description Композабл для управления галереями Fancybox в приложениях Vue 3.
 */

import type { ComputedRef } from 'vue'
import { Fancybox } from '@fancyapps/ui'
import { computed, ref, toValue } from 'vue'
import { ALLOWED_BUT_NOT_SUPPORTED } from '../constants/files'
import { getFileTypeFromFilename } from '../helpers/files'

// Helper types for Fancybox items and slides
interface FancyboxItemInput {
  src: string
  type?: string
  caption?: string | ((slide: FancyboxSlide) => string)
  html?: string | ComputedRef<string>
  customClass?: string
  options?: any // For nested options like in itemOptionsMap.video
  [key: string]: any // Allow other properties
}

interface FancyboxSlide {
  type: string
  [key: string]: any // Allow other properties
}

interface Gallery {
  items: FancyboxItemInput[]
  options: any // TODO: Use a more specific type for Fancybox options if available
}
// End Helper types

const galleries = ref<{ [key: string]: Gallery }>({
  global: { items: [], options: {} },
})

/**
 * @type {Record<string, string | string[]>}
 */
const formatToMime: Record<string, string | string[]> = {
  // Видео форматы
  mp4: 'video/mp4',
  webm: 'video/webm',
  avi: 'video/x-msvideo',
  mov: ['video/quicktime', 'video/x-quicktime', 'video/mp4'],
  wmv: 'video/x-ms-wmv',
  flv: 'video/x-flv',
  mkv: 'video/x-matroska',
  mpeg: 'video/mpeg',
  ogv: 'video/ogg',

  // Аудио форматы
  mp3: 'audio/mpeg',
  wav: 'audio/wav',
  ogg: 'audio/ogg',
  aac: 'audio/aac',
  m4a: 'audio/mp4',
  flac: 'audio/flac',
  wma: 'audio/x-ms-wma',
  opus: 'audio/opus',
}

// List of formats that are known to be unsupported in browsers
const unsupportedFormats = [
  ...ALLOWED_BUT_NOT_SUPPORTED.image,
  ...ALLOWED_BUT_NOT_SUPPORTED.audio,
]

const extensionRegex = /\.\w+$/

/**
 * Проверяет, поддерживается ли данный формат файла браузером.
 * @param {string} src - URL-адрес источника файла
 * @returns {boolean} True, если формат поддерживается, иначе false
 */
function isFormatSupported(src: string): boolean {
  const hasExtension = extensionRegex.test(src)
  if (!hasExtension) {
    return true
  }

  const format = src.split('.').pop()?.toLowerCase()

  if (!format) {
    return true
  }

  // Check if format is in the unsupported list
  if (unsupportedFormats.includes(format)) {
    return false
  }

  const fileType = getFileTypeFromFilename(src)

  // For audio and image formats, we assume they are supported if not in unsupportedFormats
  // and not in formatToMime (which contains only audio/video formats)
  if (fileType === 'audio' || fileType === 'image') {
    return true
  }

  // For video formats
  if (Object.keys(formatToMime).includes(format)) {
    const video = document.createElement('video')
    const mimeEntry = formatToMime[format as keyof typeof formatToMime]

    if (Array.isArray(mimeEntry)) {
      return mimeEntry.some(m => video.canPlayType(m) !== '')
    }

    return video.canPlayType(mimeEntry) !== ''
  }

  return false
}

/**
 * Генерирует HTML-шаблон для видео элементов.
 * @param {object} opts - Опции для видео шаблона
 * @param {string} opts.src - URL-адрес источника видео
 * @param {string} [opts.poster] - URL-адрес изображения постера для видео
 * @returns {string} HTML-шаблон для видео элемента
 */
function getVideoTpl(opts: { src: string, poster?: string }): string {
  const format = opts.src.split('.').pop()?.toLowerCase()
  if (!format || !(format in formatToMime)) {
    return '<p>Unsupported video format or error retrieving MIME type.</p>'
  }
  const mime = formatToMime[format as keyof typeof formatToMime]
  // Ensure mime is string for single source, or handle array if multiple sources are needed from one format (unlikely for <source type>)
  const mimeType = Array.isArray(mime) ? mime[0] : mime
  return `<video class="fancybox__html5video" playsinline controls controlsList="nodownload" poster="${opts.poster || ''}" preload="auto"><source src="${opts.src}" type="${mimeType}" />Извините, ваш браузер не поддерживает встроенные видео.</video>`
}

/**
 * Генерирует HTML-шаблон для неподдерживаемых форматов.
 * @param {object} opts - Опции для шаблона
 * @param {string} opts.src - URL-адрес файла
 * @returns {import('vue').ComputedRef<string>} Вычисляемый HTML-шаблон для неподдерживаемых форматов
 */
function getFormatNotSupportedTpl(opts: { src: string }): ComputedRef<string> {
  const src = opts.src
  const defaultText = 'Не удается воспроизвести файл. Формат не поддерживается'
  const defaultLinkText = 'Скачать'

  /**
   * Генерирует HTML-шаблон для неподдерживаемых форматов.
   * Обернуто в computed, чтобы можно было использовать переводы.
   */
  return computed(() => {
    return `
  <div class="custom-fancybox__not-supported">
    <div class="custom-fancybox__not-supported-inner">
      <p class="custom-fancybox__not-supported-title">${defaultText}</p>
      <a class="custom-fancybox__not-supported-link" href="${src}" download>
        <svg class="custom-fancybox__not-supported-link-icon" width="13" height="11" viewBox="0 0 13 11" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11.5 10H1.5M6.5 1V7M6.5 7L8.5 5M6.5 7L4.5 5" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
          <span class="custom-fancybox__not-supported-link-text">${defaultLinkText}</span>
        </a>
      </div>
    </div>
`
  })
}

/**
 * @type {Record<string, (opts: object) => object>}
 */
const itemOptionsMap: Record<string, (opts: FancyboxItemInput) => FancyboxItemInput> = {
  picture: (opts: FancyboxItemInput) => {
    // Check if the image format is supported
    const isSupported = isFormatSupported(opts.src)

    if (!isSupported) {
      return {
        ...opts,
        type: 'html',
        customClass: 'has-unsupported-format',
        html: toValue(getFormatNotSupportedTpl(opts)),
      }
    }

    return {
      ...opts,
      type: 'image',
      backdropClick: () => {
        return 'close'
      },
      options: {
        caption: (slide: FancyboxSlide) => slide.type,
      },
    }
  },
  image: (opts: FancyboxItemInput) => {
    // Check if the image format is supported
    const isSupported = isFormatSupported(opts.src)

    if (!isSupported) {
      return {
        ...opts,
        type: 'html',
        customClass: 'has-unsupported-format',
        html: toValue(getFormatNotSupportedTpl(opts)),
      }
    }

    return {
      ...opts,
      type: 'image',
      backdropClick: () => {
        return 'close'
      },
      options: {
        caption: (slide: FancyboxSlide) => slide.type,
      },
    }
  },
  video: (opts: FancyboxItemInput) => {
    const isYoutube = opts.src.includes('youtube.')

    if (isYoutube) {
      return {
        ...opts,
        type: 'youtube',
        options: {
          youtube: {
            autoplay: 1,
          },
        },
      }
    }

    // For non-YouTube videos
    const isSupported = isFormatSupported(opts.src)
    if (isSupported) {
      return {
        ...opts,
        type: 'html5video',
        customClass: 'has-html5video',
        html: getVideoTpl(opts),
      }
    }
    else {
      return {
        ...opts,
        type: 'html',
        customClass: 'has-unsupported-format',
        html: toValue(getFormatNotSupportedTpl(opts)),
      }
    }
  },
  audio: (opts: FancyboxItemInput) => {
    const isSupported = isFormatSupported(opts.src)
    if (isSupported) {
      return {
        ...opts,
        type: 'html',
        customClass: 'custom-fancybox-audio',
        html: `<audio controls src="${opts.src}">Your browser does not support the audio element.</audio>`,
        options: {
          caption: (slide: FancyboxSlide) => slide.type,
        },
      }
    }
    else {
      return {
        ...opts,
        type: 'html',
        customClass: 'has-unsupported-format',
        html: toValue(getFormatNotSupportedTpl(opts)),
        options: {
          caption: (slide: FancyboxSlide) => slide.type,
        },
      }
    }
  },
  default: (opts: FancyboxItemInput) => {
    // For any other types or if type is not specified
    return {
      ...opts,
      type: opts.type || 'html',
      options: {
        caption: (slide: FancyboxSlide) => slide.type,
      },
    }
  },
}

/**
 * @type {object} Опции по умолчанию для Fancybox
 */
const defaultOptions = {
  groupAll: true,
  idle: false,
  dragToClose: false,
  trapFocus: false,
  Thumbs: false,
  Slideshow: true,
  mainClass: 'custom-fancybox',
  mobile: { clickOutside: 'close' },
  backdropClick: () => {
    return 'close'
  },
  Toolbar: {
    enabled: true,
  },
  Html: {
    videoAutoplay: true,
    videoTpl: '<video class="fancybox__html5video" playsinline controls disablepictureinpicture poster="{{poster}}"><source src="{{src}}" type="{{format}}" />Извините, ваш браузер не поддерживает встроенные видео.</video>',
  },
  Carousel: {
    Navigation: {
      nextTpl: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15.4 12.97l-2.68 2.72 1.34 1.38L19 12l-4.94-5.07-1.34 1.38 2.68 2.72H5v1.94z"></path></svg>',
      prevTpl: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M11.28 15.7l-1.34 1.37L5 12l4.94-5.07 1.34 1.38-2.68 2.72H19v1.94H8.6z"></path></svg>',
    },
  },
  on: {
    'initiate': (instance: Fancybox) => {
      instance.container?.style.setProperty(
        '--fancybox-color',
        'rgba(30, 35, 46, 0.95)',
      )
    },
    'closing': (instance: Fancybox, _e: Event | any) => {
      const activeSlide = instance.carousel?.slides[instance.carousel.page]
      if (activeSlide?.el) {
        (activeSlide.el as HTMLElement)?.blur?.()
      }
    },
    'closed': (_instance: Fancybox, _e: Event | any) => {
      // Additional cleanup if needed
    },
    'Carousel.click': (_carouselInstance: any, fancyboxInstance: Fancybox, event: Event) => {
      const isFancyboxSlideClicked = (event.target as HTMLElement)?.closest('.fancybox__slide')
      const isFancyboxContentClicked = (event.target as HTMLElement)?.closest('.fancybox__content')
      if (isFancyboxSlideClicked && !isFancyboxContentClicked) {
        if (fancyboxInstance && typeof fancyboxInstance.close === 'function') {
          fancyboxInstance.close()
        }
      }
    },
    'Carousel.selectSlide': (carouselInstance: any) => {
      if (!carouselInstance.container) {
        return
      }
      const videoEl = carouselInstance.container.querySelector?.('video')
      if (videoEl && !videoEl.hasAttribute('data-foquz-html5video-replaced')) {
        const clonedVideoEl = videoEl.cloneNode(true)
        videoEl.parentNode?.replaceChild(clonedVideoEl, videoEl)
        ;(clonedVideoEl as HTMLElement).setAttribute('data-foquz-html5video-replaced', '')
      }
    },
  },
}

/**
 * Композабл для управления галереями Fancybox.
 * @param {string} [group] - Имя группы галереи
 * @param {object} [slideOptions] - Пользовательские опции для слайдов Fancybox
 * @returns {object} Объект, содержащий методы для управления галереей
 */
export function useFancybox(group = 'global', slideOptions = defaultOptions) {
  const groupName = group || 'global'
  const addedItem = ref<FancyboxItemInput | null>(null)
  const l10n = ref({})

  if (!galleries.value[groupName]) {
    galleries.value[groupName] = { items: [] as FancyboxItemInput[], options: { ...slideOptions } }
  }
  else {
    galleries.value[groupName].options = { ...galleries.value[groupName].options, ...slideOptions }
  }

  const findItemById = (id: string): FancyboxItemInput | undefined => {
    return galleries.value[groupName]?.items.find((galleryItem: FancyboxItemInput) => galleryItem.id === id)
  }

  const getItem = (id: string): FancyboxItemInput | undefined => {
    const currentItem = computed(() => findItemById(id))
    return currentItem.value
  }

  const show = (startIndex = 0) => {
    const currentGallery = galleries.value[groupName]
    const items: FancyboxItemInput[] = currentGallery.items.map((i: FancyboxItemInput) => toValue(i)).filter((i: FancyboxItemInput | undefined) => i && toValue(i.src)) as FancyboxItemInput[]

    const index = items.findIndex((item: FancyboxItemInput) => {
      const src = toValue(item.src)
      const addedSrc = toValue(addedItem.value)?.src
      const id = toValue(item.id)
      const addedId = toValue(addedItem.value)?.id

      if (src && addedSrc) {
        return src === addedSrc
      }
      else if (id && addedId) {
        return id === addedId
      }
      return false
    })
    const resolvedStartIndex = index !== -1 ? index : startIndex

    const galleryOptions = {
      ...currentGallery.options,
      startIndex: resolvedStartIndex,
      l10n: l10n.value,
    }

    const slidesToShow: FancyboxItemInput[] | NodeListOf<Element> = items.length > 0
      ? items
      : document.querySelectorAll(`[data-fancybox="${groupName}"]`)

    Fancybox.show(
      slidesToShow as any,
      galleryOptions,
    )
  }

  const addItem = (item: FancyboxItemInput) => {
    const currentItems: FancyboxItemInput[] = galleries.value[groupName].items
    const existingItemIndex = currentItems.findIndex(
      (galleryItem: FancyboxItemInput) => galleryItem.id === item.id || galleryItem.src === item.src,
    )

    const itemType = item.type || getFileTypeFromFilename(item.src) || 'default'
    const itemSpecificOptions = itemOptionsMap[itemType] || itemOptionsMap.default
    const processedItem = itemSpecificOptions(item) // Process item once

    if (existingItemIndex === -1) {
      currentItems.push(processedItem) // Add if new
    }
    else {
      currentItems.splice(existingItemIndex, 1, processedItem) // Update if existing
    }
    addedItem.value = processedItem // Store the processed (added or updated) item
  }

  const updateItem = (id: string, newItemData: Partial<FancyboxItemInput>) => {
    const itemIndex = galleries.value[groupName].items.findIndex(galleryItem => galleryItem.id === id)
    if (itemIndex !== -1) {
      const existingItem = galleries.value[groupName].items[itemIndex]
      // Merge new data. itemSpecificOptions should be re-applied if type changes.
      const updatedItem = { ...existingItem, ...newItemData }
      const itemType = updatedItem.type || getFileTypeFromFilename(updatedItem.src) || 'default'
      const itemSpecificOptions = itemOptionsMap[itemType] || itemOptionsMap.default
      galleries.value[groupName].items.splice(itemIndex, 1, itemSpecificOptions(updatedItem))
    }
  }

  return {
    /**
     * Показывает галерею Fancybox, начиная с последнего добавленного элемента.
     */
    show: () => {
      const currentGallery = galleries.value[groupName]
      const items = currentGallery.items.map(i => toValue(i)).filter(i => toValue(i.src))
      const index = items.findIndex((item) => {
        const src = toValue(item.src)
        const addedSrc = toValue(addedItem.value?.src)
        const id = toValue(item.id)
        const addedId = toValue(addedItem.value?.id)

        if (src && addedSrc) {
          return src === addedSrc
        }
        else if (id && addedId) {
          return id === addedId
        }
        return false
      })
      show(index)
    },
    /**
     * Удаляет последний добавленный элемент из галереи.
     */
    removeItem: () => {
      const currentGallery = galleries.value[groupName]
      const items = currentGallery.items.map(i => toValue(i))
      const index = items.findIndex((item) => {
        const src = toValue(item.src)
        const addedSrc = toValue(addedItem.value)?.src
        const id = toValue(item.id)
        const addedId = toValue(addedItem.value)?.id

        if (src && addedSrc) {
          return src === addedSrc
        }
        else if (id && addedId) {
          return id === addedId
        }
        return false
      })

      if (index === -1) {
        return
      }

      currentGallery.items.splice(index, 1)
    },
    addItem,
    updateItem,
    getItem,
    /**
     * Проверяет, поддерживается ли формат файла браузером.
     */
    isFormatSupported,
  }
}
