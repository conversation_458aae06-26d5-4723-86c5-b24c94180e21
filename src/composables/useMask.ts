import type { MaskitoMask, MaskitoOptions } from '@maskito/core'
import {
  maskitoAddOnFocusPlugin,
  maskitoCaretGuard,
  maskitoPrefixPostprocessorGenerator,
  maskitoRemoveOnBlurPlugin,
} from '@maskito/kit'

export type InputMask = 'phone' | 'date'

/**
 * Опции маски телефона для Maskito
 */
function phoneMaskOptions(): MaskitoOptions {
  return {
    plugins: [
      maskitoAddOnFocusPlugin('+7 '),
      maskitoRemoveOnBlurPlugin('+7 '),
      maskitoCaretGuard((value, [from, to]) => [
        from === to ? '+7 '.length : 0,
        value.length,
      ]),
    ],
    postprocessors: [maskitoPrefixPostprocessorGenerator('+7 ')],
    preprocessors: [completePhonePreprocessor()],
    mask: ['+', '7', ' ', '(', /\d/, /\d/, /\d/, ')', ' ', /\d/, /\d/, /\d/, ' ', /\d/, /\d/, /\d/, /\d/],
  }
}

/**
 * Опции маски даты для Maskito (DD.MM.YYYY)
 */
function dateMaskOptions(): MaskitoOptions {
  return {
    mask: [
      ...Array.from({ length: 2 }).fill(/\d/),
      '.',
      ...Array.from({ length: 2 }).fill(/\d/),
      '.',
      ...Array.from({ length: 4 }).fill(/\d/),
    ] as MaskitoMask,
  }
}

/**
 * Препроцессор для корректировки вставки номера телефона
 */
function completePhonePreprocessor() {
  const trimPrefix = (value: string) => value.replace(/^\+?7?\s?8?\s?/, '')
  const countDigits = (value: string) => value.replace(/\D/g, '').length

  return ({ elementState, data }: { elementState: any, data: any }) => {
    const { value, selection } = elementState

    return {
      elementState: {
        selection,
        value: countDigits(value) > 11 ? trimPrefix(value) : value,
      },
      data: countDigits(data) >= 11 ? trimPrefix(data) : data,
    }
  }
}

export function useMask(mask: InputMask) {
  if (mask === 'phone') {
    return phoneMaskOptions()
  }
  if (mask === 'date') {
    return dateMaskOptions()
  }
  return undefined
}

export function getMaskPlaceholder(mask?: InputMask) {
  if (mask === 'phone') {
    return '+7 (___) ___ ____'
  }
  if (mask === 'date') {
    return '00.00.0000'
  }
  return ''
}
