/**
 * Get all available icon names by extracting them from the assets/icons directory
 * This is a compile-time function that uses Vite's import.meta.glob
 * @returns Array of icon names without the .svg extension
 */
export function getIconNames(): string[] {
  // Use Vite's import.meta.glob to get all SVG files
  const iconFiles = import.meta.glob('../assets/icons/*.svg', { eager: true })

  // Extract icon names from file paths
  const iconNames = Object.keys(iconFiles).map((path) => {
    // Extract the filename without extension from path
    // Example: '../assets/icons/search.svg' -> 'search'
    const match = path.match(/\.\.\/assets\/icons\/(.+)\.svg/)
    return match ? match[1] : ''
  }).filter(Boolean)

  return iconNames
}
