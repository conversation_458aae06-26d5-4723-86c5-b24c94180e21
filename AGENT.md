# AGENT.md - Foquz UI Coding Guidelines

## Commands
- Build: `npm run build`
- Dev server: `npm run dev`
- Lint: `npm run lint` or `npm run lint:fix` to auto-fix issues
- Type check: `npm run type-check`
- Run tests: `npx vitest`
- Run single test file: `npx vitest src/components/FzComponent/FzComponent.spec.ts`
- Run specific test: `npx vitest -t "test description pattern"`
- Storybook: `npm run storybook`

## Code Style Guidelines
- Use TypeScript for all files
- Follow Vue 3 Composition API patterns
- Use the ESLint config from @antfu/eslint-config
- Naming conventions:
  - Components: `Fz` prefix (e.g., `FzButton`)
  - CSS variables: `--fz-` prefix
  - Tailwind classes: `fz:` prefix
- Component structure: Each component in its own directory with `.vue`, `.spec.ts`, `.stories.ts` files
- Testing: Use Vitest with Vue Test Utils
- CSS: Use Tailwind with the `fz:` prefix for utility classes
