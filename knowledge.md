# Project: foquz-ui

This document contains key information about the `foquz-ui` project.

## Overview

`foquz-ui` is a Vue 3 project built with Vite and TypeScript. It utilizes Tailwind CSS for styling and Storybook for component development and documentation.

## Key Technologies & Libraries

- **Framework:** Vue 3 (using `<script setup lang="ts">`)
- **Build Tool:** Vite
- **Language:** TypeScript (with strict mode enabled)
- **Styling:**
    - Tailwind CSS (v4.x) with a custom prefix `fz:`
    - For CSS variables, use `--fz-` prefix.
    - PostCSS
    - Custom theme variables defined in `src/styles/theme.css`
    - Fonts (FzRoboto) managed via `src/styles/fonts.css`
- **Component Library/UI:**
    - Custom components in `src/components/` (e.g., FzButton, FzSidebar, FzLogo)
    - Fancyapps UI (for Fancybox)
    - Maskito for input masking
    - Tanstack Vue Form for form management
    - Tanstack Vue Table for table components
    - Embla Carousel Vue for carousels
    - <PERSON>ka UI
    - Simplebar Vue for custom scrollbars
- **Linting:** ESLint with `@antfu/eslint-config`
- **Type Checking:** `vue-tsc`
- **Component Development:** Storybook
- **Testing (potentially):** Playwright, Vitest (devDependencies present)
- **Chromatic:** For visual testing with Storybook.

## Project Structure Highlights

- `src/main.ts`: Main application entry point. Initializes Vue app and global directives.
- `src/App.vue`: Root Vue component.
- `src/components/`: Directory for reusable Vue components.
- `src/styles/`: Contains global styles, Tailwind configuration, custom theme, and fonts.
    - `index.css`: Main CSS entry point, imports other CSS files.
    - `theme.css`: Defines CSS custom properties for the theme (colors, spacing, typography).
    - `fonts.css`: Defines `@font-face` for FzRoboto.
- `vite.config.ts`: Vite configuration file.
- `tsconfig.json`, `tsconfig.app.json`, `tsconfig.node.json`: TypeScript configuration files.
- `package.json`: Project metadata, dependencies, and scripts.

## Available Scripts (from `package.json`)

- `dev`: Starts the Vite development server. Command: `vite`
- `build`: Builds the project for production. Command: `vue-tsc -b && vite build`
- `preview`: Previews the production build locally. Command: `vite preview`
- `lint`: Lints the codebase using ESLint. Command: `eslint .`
- `lint:fix`: Lints and automatically fixes issues. Command: `eslint --fix .`
- `type-check`: Performs type checking using `vue-tsc`. Command: `vue-tsc --noEmit -p ./tsconfig.app.json`
- `storybook`: Starts the Storybook development server. Command: `storybook dev -p 6006`
- `build-storybook`: Builds the Storybook static site. Command: `storybook build`
- `chromatic`: Runs Chromatic tests. Command: `npx chromatic --project-token=chpt_7a92e7064e40caa`

## Linting and Type Checking Commands

- **Linting:** `npm run lint`
- **Type Checking:** `npm run type-check` (which executes `vue-tsc --noEmit -p ./tsconfig.app.json`)

## Notes

- Tailwind CSS classes are prefixed with `fz:`.
- Global styles and theme variables are well-defined.
- The project uses `vite-svg-loader` to import SVG files as Vue components.
- `vite-plugin-vue-tailwind-auto-reference` is used, likely for optimizing Tailwind CSS usage or auto-importing.

## Project Rules & Conventions (from .cursor/rules and .roo/rules)

- **Linting Errors:** If linting errors occur, try running `npx eslint path_to_file --fix`.
- **Reka UI Wrappers:** When wrapping Reka UI components, use `useForwardPropsEmits`, `useForwardProps`, or `useEmitAsProps` utilities from Reka UI to correctly forward props and emits.
- **Project Structure:**
    - `/src/assets`: Resources (e.g., fonts).
    - `/src/components`: Components, typically in `FzComponentName` subdirectories.
    - `/src/composables`: Vue composables.
    - `/src/styles`: Global styles.
    - `/src/config`: Configurations.
    - `/src/utils`: Utility functions.
- **Documentation:** Storybook must be used for component documentation. Each component should have a corresponding story file (e.g., `FzButton.stories.ts`).
- **Testing:**
    - Unit tests: Vitest + Vue Test Utils.
    - E2E tests: Playwright.
    - Visual regression testing: Chromatic.
- **Tech Stack Mandates:**
    - Vue 3 + TypeScript for components.
    - Vite for building.
    - Reka UI as the base component library.
    - TailwindCSS with CSS variables and PostCSS for styling.
    - ESLint with `@antfu/eslint-config`.
- **Specific Dependencies for Features:**
    - Icons: `vite-svg-loader`
    - Dates: `dayjs`
    - Forms: `tanstack-forms` (Note: `package.json` shows `@tanstack/vue-form`)
    - Tables: `tanstack-table` (Note: `package.json` shows `@tanstack/vue-table`)
    - UI components (Modals, Sidesheets, Toasts, Calendars/Date Pickers): `reka-ui`
    - Custom scrollbars: `simplebar` and `simplebar-vue`
- **Browser Support:** Modern browsers (Chrome, Firefox, Safari, Edge).
- **CSS Naming:** Follow BEM naming convention with `fz-` prefix for component classes (e.g., `fz-dialog__overlay`). Use Tailwind utility classes with `fz:` prefix for component layouts.
