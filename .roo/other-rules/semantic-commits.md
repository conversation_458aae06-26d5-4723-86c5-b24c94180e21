# semantic-commits: Semantic Commit Message Guidelines

All commit messages must follow the [Conventional Commits](https://www.conventionalcommits.org/) specification to ensure clarity and consistency in the project history.

## Commit Message Format

```
type(scope): short description
```

- **type**: The type of change. Use one of the allowed types below.
- **scope**: (optional) The area of the codebase affected (e.g., component, file, or feature name).
- **short description**: A concise summary of the change (max 72 characters, lowercase, no period).

## Allowed Types

- feat: A new feature
- fix: A bug fix
- docs: Documentation only changes
- style: Changes that do not affect meaning of the code (formatting, missing semi colons, etc)
- refactor: A code change that neither fixes a bug nor adds a feature
- perf: A code change that improves performance
- test: Adding missing tests or correcting existing tests
- build: Changes that affect the build system or external dependencies
- ci: Changes to CI configuration files and scripts
- chore: Other changes that don't modify src or test files
- revert: Reverts a previous commit

## Examples

```
feat(FzButton): add loading state to button
fix(FzCard): correct padding issue in card body
chore: update dependencies
```

## Additional Guidelines

- Use the imperative mood in the description (e.g., "add" not "added" or "adds").
- Use lowercase for the type and scope.
- Do not end the description with a period.
- Reference issues or PRs in the body if relevant (e.g., "Closes #123").
