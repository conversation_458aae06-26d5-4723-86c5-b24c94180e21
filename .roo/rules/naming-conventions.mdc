---
description: 
globs: 
alwaysApply: true
---
# Naming Conventions

- Component names must use `Fz` prefix (Example: `FzButton`)
- CSS variables must use `--fz-` prefix (Example: `--fz-min-button-height`)
- TailwindCSS classes must use `fz:` prefix for component layouts (Example: `fz:p-4`, `fz:bg-gray-50`)
- Custom CSS classes should follow BEM convention with `fz-` prefix:
  - Block: Main component (Example: `fz-card`)
  - Element: Component parts (Example: `fz-card__header`, `fz-card__body`)
  - Modifier: Component variations (Example: `fz-card--primary`, `fz-button--large`)
