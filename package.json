{"name": "@foquz/foquz-ui", "type": "module", "version": "0.0.2", "private": false, "publishConfig": {"registry": "https://doxsw.gitlab.yandexcloud.net/api/v4/projects/${CI_PROJECT_ID}/packages/npm/"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/foquz-ui.es.js"}, "./foquz-ui.css": "./dist/foquz-ui.css", "./theme.css": "./dist/theme.css"}, "module": "./dist/foquz-ui.es.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build": "npm run type-check && vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint --fix .", "type-check": "vue-tsc --noEmit -p tsconfig.app.json", "size": "size-limit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_7a92e7064e40caa"}, "dependencies": {"@fancyapps/ui": "^5.0.36", "@maskito/core": "^3.7.2", "@maskito/kit": "^3.7.2", "@maskito/phone": "^3.7.2", "@maskito/vue": "^3.7.2", "@tanstack/vue-form": "^1.7.0", "@tanstack/vue-table": "^8.21.3", "@tanstack/vue-virtual": "^3.13.9", "autoprefixer": "^10.4.21", "dayjs": "^1.11.13", "embla-carousel-vue": "^8.6.0", "postcss": "^8.5.3", "reka-ui": "^2.2.1", "simplebar-vue": "^2.4.1", "tailwindcss": "^4.1.4", "vite": "^6.3.1", "vue": "^3.5.13"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@chromatic-com/storybook": "^3.2.6", "@playwright/test": "^1.52.0", "@size-limit/preset-big-lib": "^11.2.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/vue3": "^8.6.12", "@storybook/vue3-vite": "^8.6.12", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/vite": "^4.1.4", "@types/lodash.isobject": "^3.0.9", "@types/lodash.mergewith": "^4.6.9", "@types/node": "^22.15.2", "@vitejs/plugin-vue": "^5.2.2", "@vitest/browser": "^3.1.2", "@vitest/coverage-v8": "^3.1.2", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "chromatic": "^12.0.0", "eslint": "^9.25.1", "eslint-plugin-storybook": "^0.12.0", "lightningcss": "^1.29.3", "lodash.isobject": "^3.0.2", "lodash.mergewith": "^4.6.2", "playwright": "^1.52.0", "size-limit": "^11.2.0", "storybook": "^8.6.12", "typescript": "~5.7.2", "vite-plugin-dts": "^4.5.4", "vite-plugin-vue-tailwind-auto-reference": "^1.0.2", "vite-svg-loader": "^5.1.0", "vitest": "^3.1.2", "vue-tsc": "^2.2.8"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "size-limit": [{"path": "dist/foquz-ui.es.js", "limit": "150 kB"}, {"path": "dist/foquz-ui.umd.js", "limit": "150 kB"}]}