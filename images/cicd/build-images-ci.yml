.job_build_image: &job_build_configuration
  allow_failure: true
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: ['']
  script:
    - chmod 777 ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - source ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(echo -n "json_key:${CI_REGISTRY_KEY}" | base64 | tr -d '\n' )\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor --cache=false
      --build-arg "RELEASE=${RELEASE}"
      --build-arg "MODE=${MODE}"
      --build-arg "TYPE=${TYPE}"
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/images/${IMAGE_NAME}/dockerfile"
      --destination "${CI_REGISTRY}/${IMAGE_NAME}:${TAG}"
    - echo "${NAME_POD}=true" >> build.env
  artifacts:
    reports:
      dotenv: build.env

task_branch_k8s:
  stage: prebuild
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  image: cr.yandex/crpsvv5iscafkhlubkmt/foquz-debian-builder-base:1.0.0
  script:
    - chmod 777 ./images/scripts/versions.sh
    - ./images/scripts/versions.sh
  artifacts:
    reports:
      dotenv: build.env

build-foquz-ui:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  variables:
    IMAGE_NAME: foquz-ui
    NAME_POD: UI
  <<: *job_build_configuration

set-version-task:
  stage: deploy
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  variables:
    SET_VERSION: true
    TAG: $TAG
    RELEASE: $RELEASE
    MODE: $MODE
    TYPE: $TYPE
    BRANCH: $CI_COMMIT_BRANCH
    SOURCE_CI_PROJECT_TITLE: $CI_PROJECT_TITLE
    SOURCE_CI_PROJECT_PATH: $CI_PROJECT_PATH
    SOURCE_CI_COMMIT_SHA: $CI_COMMIT_SHA
    SOURCE_CI_COMMIT_MESSAGE: $CI_COMMIT_TITLE
    SOURCE_CI_PIPELINE_CREATED_AT: $CI_PIPELINE_CREATED_AT
    UI: true
    CONTAINER_foquz_ui: $UI
  trigger:
    project: doxsw/foquz-app-deploy
    branch: stage
