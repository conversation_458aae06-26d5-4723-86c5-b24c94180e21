# Component Structure

- Components must be placed in dedicated directories: `/src/components/FzComponentName/`
- Required files for each component:
  - `FzComponentName.vue` - Component implementation
  - `index.ts` - Export file
- Optional files:
  - `FzComponentName.spec.ts` - Unit tests
  - `FzComponentName.stories.ts` - Storybook stories

# CSS Guidelines

- Base level CSS: Use CSS variables with `fz` prefix instead of direct Tailwind classes
  ```css
  /* DO THIS */
  .btn {
    min-height: var(--fz-min-button-height);
    padding: var(--fz-spacing-2);
  }

  /* DON'T DO THIS */
  .btn {
    min-height: 2.5rem; /* Use CSS variable instead */
    padding: 0.5rem; /* Use CSS variable instead */
  }
  ```

Available css variables for this theme defined in `src/styles/theme.css`. Tailwind CSS variables are also available. Use them!

IMPORTANT: NEVER USE @apply! Only use css variables for low-level components and use classes with `fz:` for layout to high-level components.

- Layout level: Use Tailwind classes with `fz:` prefix for component layout
  ```vue
  <!-- Example of proper layout with prefixed classes -->
  <div class="fz-card fz-card--rounded">
    <div class="fz-card__header fz:p-4 fz:bg-gray-50 fz:border-b">
      <!-- Content -->
    </div>
  </div>
  ```

- Component level: Use BEM naming conventions with `fz-` prefix for blocks, elements, and modifiers when structuring component classes, and combine with Tailwind utility classes prefixed `fz:` for spacing, colors, and other utilities.
  ```vue
  <!-- Example of BEM naming with prefixed utility classes -->
  <div class="fz-card fz-card--rounded">
    <div class="fz-card__header">
      <!-- Header content -->
    </div>

    <div class="fz-card__body fz:p-4">
      <!-- Body content -->
    </div>

    <div class="fz-card__footer fz:p-4 fz:text-right">
      <!-- Footer content -->
    </div>
  </div>
  ```

## Theme variable namespaces

Theme variables are defined in namespaces and each namespace corresponds to one or more utility class or variant APIs.

Defining new theme variables in these namespaces will make new corresponding utilities and variants available in your project:

| Namespace | Utility classes |
|-----|----|
| --color-* | Color utilities like bg-red-500, text-sky-300, and many more |
| --font-* | Font family utilities like font-sans |
| --text-* | Font size utilities like text-xl |
| --font-weight-* | Font weight utilities like font-bold |
| --tracking-* | Letter spacing utilities like tracking-wide |
| --leading-* | Line height utilities like leading-tight |
| --breakpoint-* | Responsive breakpoint variants like sm:* |
| --container-* | Container query variants like @sm:* and size utilities like max-w-md |
| --spacing-* | Spacing and sizing utilities like px-4, max-h-16, and many more |
| --radius-* | Border radius utilities like rounded-sm |
| --shadow-* | Box shadow utilities like shadow-md |
| --inset-shadow-* | Inset box shadow utilities like inset-shadow-xs |
| --drop-shadow-* | Drop shadow filter utilities like drop-shadow-md |
| --blur-* | Blur filter utilities like blur-md |
| --perspective-* | Perspective utilities like perspective-near |
| --aspect-* | Aspect ratio utilities like aspect-video |
| --ease-* | Transition timing function utilities like ease-out |
| --animate-* | Animation utilities like animate-spin |

See [theme.css](mdc:src/styles/theme.css) for available custom css variables. They are available with `--fz-` prefix in css

# Components
– If you implement Components based on reka-ui, please add animations by using approach from that library, do not use `FzTransition` component. If forwarding of props/emits is needed see [forward-props-emits.mdc](mdc:.cursor/rules/forward-props-emits.mdc) rule

# Forward Props and Emits

When creating wrapper components around Reka UI components, you must properly forward props and emits to maintain all functionality.

## Available Utilities

Reka UI provides three utilities for forwarding props and emits:

### 1. `useForwardPropsEmits`

Use this when you need to forward both props and emits to a child component.

```vue
<script setup lang="ts">
import type { DialogRootEmits, DialogRootProps } from 'reka-ui'
import { DialogRoot, useForwardPropsEmits } from 'reka-ui'

const props = defineProps<DialogRootProps>()
const emits = defineEmits<DialogRootEmits>()

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <DialogRoot v-bind="forwarded">
    <slot />
  </DialogRoot>
</template>
```

### 2. `useForwardProps`

Use this when you only need to forward props to a child component.

```vue
<script setup lang="ts">
import type { DialogContentProps } from 'reka-ui'
import { DialogContent, useForwardProps } from 'reka-ui'

const props = defineProps<DialogContentProps>()
const forwarded = useForwardProps(props)
</script>

<template>
  <DialogContent v-bind="forwarded">
    <slot />
  </DialogContent>
</template>
```

### 3. `useEmitAsProps`

Use this when you need to convert emits to props format.

```vue
<script setup lang="ts">
import type { DialogContentEmits } from 'reka-ui'
import { useEmitAsProps } from 'reka-ui'

const emits = defineEmits<DialogContentEmits>()
const emitsAsProps = useEmitAsProps(emits)
</script>

<template>
  <Component v-bind="emitsAsProps">
    <slot />
  </Component>
</template>
```

## Custom Component Example

This is a complete example of creating a custom dialog component that wraps Reka UI primitives:

```vue
<!-- FzDialog.vue -->
<script setup lang="ts">
import type { DialogRootEmits, DialogRootProps } from 'reka-ui'
import { Cross2Icon } from '@radix-icons/vue'
import { DialogClose, DialogContent, DialogOverlay, DialogPortal, DialogRoot, DialogTitle, useForwardPropsEmits } from 'reka-ui'

const props = defineProps<DialogRootProps & { title?: string }>()
const emits = defineEmits<DialogRootEmits>()

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <DialogRoot v-bind="forwarded">
    <slot name="trigger" />
    <DialogPortal>
      <DialogOverlay class="fz-dialog__overlay" />
      <DialogContent class="fz-dialog__content">
        <DialogTitle v-if="title" class="fz-dialog__title">
          {{ title }}
        </DialogTitle>
        <div class="fz-dialog__body">
          <slot />
        </div>
        <DialogClose class="fz-dialog__close">
          <Cross2Icon />
          <span class="fz-sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<style>
.fz-dialog__overlay {
  background-color: var(--fz-color-surface-overlay-dark);
  position: fixed;
  inset: 0;
  z-index: 50;
}

.fz-dialog__content {
  background-color: var(--fz-color-surface-light);
  border-radius: var(--fz-radius-md);
  box-shadow: var(--fz-shadow-dropdown);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 450px;
  max-height: 85vh;
  padding: var(--fz-spacing-4);
  z-index: 51;
}

.fz-dialog__title {
  font: var(--fz-font-modal-title);
  margin-bottom: var(--fz-spacing-4);
}

.fz-dialog__close {
  position: absolute;
  top: var(--fz-spacing-4);
  right: var(--fz-spacing-4);
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--fz-color-border-icon);
}
</style>
```

## Usage of Custom Component

```vue
<template>
  <FzDialog v-model:open="isOpen" title="Settings">
    <template #trigger>
      <FzButton>Open Settings</FzButton>
    </template>

    <div class="fz:p-4">
      <!-- Dialog content here -->
    </div>
  </FzDialog>
</template>
```

Remember to follow the BEM naming convention with `fz-` prefix for component classes and use Tailwind utility classes with `fz:` prefix for component layouts as per our CSS guidelines.

# Linting

If you can't fix linting error after the first try run `npx eslint path_to_file --fix` to fix lint errors:

EXAMPLE:

```bash
npx eslint ./src/components/Button.vue --fix
```

If it's a lint error about blank space or order of imports – IGNORE it.

# Naming Conventions

- Component names must use `Fz` prefix (Example: `FzButton`)
- CSS variables must use `--fz-` prefix (Example: `--fz-color-surface-background`)
- TailwindCSS classes must use `fz:` prefix for component layouts (Example: `fz:p-4`, `fz:bg-gray-50`)
- Custom CSS classes should follow BEM convention with `fz-` prefix:
  - Block: Main component (Example: `fz-card`)
  - Element: Component parts (Example: `fz-card__header`, `fz-card__body`)
  - Modifier: Component variations (Example: `fz-card--primary`, `fz-button--large`)

Important: available theme variables are available in [theme.css](mdc:src/styles/theme.css)

# Project Structure

The project must follow this structure:

```
/src
  /assets         # Resources (Roboto font)
  /components     # Components in FzComponentName directories
  /composables    # Vue composables
  /styles         # Global styles
  /config         # Configurations
  /utils          # Utilities
```

## Documentation
- Storybook must be used for component documentation
- Each component should have a corresponding story file

## Testing
- Unit tests: Vitest + Vue Test Utils
- E2E tests: Playwright
- Visual regression testing: Chromatic

# Custom Scrollbars

For components that require custom scrollbars, use the `FzCustomScrollbar` component. This ensures consistent scrollbar styling across browsers and platforms, as it uses `simplebar-vue` underneath.

## Basic Usage

To add a custom scrollbar to a container, wrap the content with the `FzCustomScrollbar` component:

```vue
<script setup lang="ts">
import { FzCustomScrollbar } from '@/components/FzCustomScrollbar'
</script>

<template>
  <FzCustomScrollbar>
    <!-- Scrollable content here -->
  </FzCustomScrollbar>
</template>
```

## Configuration

The `FzCustomScrollbar` component accepts options to customize its appearance and behavior. For example, you can control shadow visibility:

```vue
<template>
  <FzCustomScrollbar :show-gradient-shadows="false">
    <!-- Scrollable content here -->
  </FzCustomScrollbar>
</template>
```

For more advanced customization, you can pass options directly to the underlying `simplebar-vue` instance:

```vue
<template>
  <FzCustomScrollbar :options="{ autoHide: false, scrollbarMinSize: 40 }">
    <!-- Scrollable content here -->
  </FzCustomScrollbar>
</template>
```

Refer to the `FzCustomScrollbar.vue` component for more details on available props and styling.

# Tech Stack

All components and features must use the following technology stack:

- Vue 3 + TypeScript for component development
- Vite for building
- Reka UI as the base component library
- TailwindCSS with CSS variables and PostCSS for styling
- ESLint with antfu configuration for code linting

## Dependencies

The following dependencies must be used for specific features:
- Icons: `vite-svg-loader`
- Dates: `dayjs`
- Forms: `tanstack-forms`
- Tables: `tanstack-table`
- UI components: `reka-ui`
- Modal windows: `reka-ui`
- Sidesheets: `reka-ui`
- Toasts: `reka-ui`
- Calendars/date pickers: `reka-ui`
- Custom scrollbars: `simplebar` and `simplebar-vue`

## Browser Support
Only modern browsers are supported (Chrome, Firefox, Safari, Edge)
