# Foquz UI Overview

## Project Goals

- Company interface UI standardization on Vue 3
- Highly customizable components
- Small bundle size and performance optimization

## Project Structure

```
/src
  /assets
    /fonts # Roboto font files
  /components      # Component implementations
    /Button
      Button.vue         # Main component file
      Button.spec.ts     # Unit/component tests
      Button.stories.ts  # Storybook stories
      ButtonGroup.vue    # Related component
      useButton.ts       # Component-specific composable (if needed)
      _button.css        # Component-specific styles (if not in-file)
      index.ts           # Public exports
    /Card
      ...
  /composables     # Vue 3 composables
  /styles          # Global CSS styles
  /config          # Configuration files (tailwindCSS config, etc.)
    /browser-support.js  # Browserslist configuration
  /utils           # Utility functions
```

**Key Characteristics:**

- Single package structure (non-monorepo)
- TypeScript-first approach
- TailwindCSS for styling

## Dependencies

### Core

| Dependency | Purpose |
|------------|---------||
| Vue 3 | Frontend framework |
| TypeScript | Type safety and DX |
| Vite | Build tooling |
| Reka UI | Component library |

### Styling

- TailwindCSS with CSS Variables
- PostCSS plugins

### Functionalities

– Icons: `vite-svg-loader`
– Date handling: `dayjs`
– Slider library: `embla-carousel` with `embla-carousel-vue` wrapper (OR `swiper`)
– Forms: `tanstack-forms`
– Data Tables: `tanstack-table`
– Modals: `reka-ui`
– Sidesheet dialogs: `reka-ui`
– Toasts: `reka-ui`
– Calendars, datepickers: `reka-ui`

### Documentation

- Storybook для документации и примеров компонентов в изолированном окружении
- Chromatic для визуальной проверки компонентов
  – TODO: Уточнить по поводу Chromatic у Виталия

### Testing

- Vitest
- Vue Test Utils
- Playwright
- Visual testing with Chromatic
- Test attributes for selectors

## TailwindCSS Strategy

**Challenge:** CSS bloat in component libraries

**Solution:** CSS Variables with prefixing and BEM convention

```css
/* Using CSS variables with --fz- prefix */
.fz-button {
  min-height: var(--fz-min-button-height);
  padding: var(--fz-p-2);
}

/* BEM modifier for primary button */
.fz-button--primary {
  background-color: var(--fz-bg-primary);
}

/* BEM element for button icon */
.fz-button__icon {
  margin-right: var(--fz-space-2);
}
```

/_ Example component with TailwindCSS utility classes _/

```vue
<template>
  <div class="fz-card">
    <div class="fz-card__header fz:p-4 fz:bg-gray-50 fz:border-b">
      <slot name="header">
        <h3 class="fz:text-lg fz:font-medium fz:text-gray-900">
          {{ title }}
        </h3>
      </slot>
    </div>
    <div class="fz-card__body fz:p-4">
      <slot />
    </div>
    <div
      v-if="$slots.footer"
      class="fz-card__footer fz:p-4 fz:bg-gray-50 fz:border-t"
    >
      <slot name="footer" />
    </div>
  </div>
</template>
```

## Technology Decisions

✅ **Resolved:**

- Icons: `vite-svg-loader`
- Date handling: `dayjs`
- API clients: `mande` (admin panel only)
- Styling: TailwindCSS with `--fz-` prefix using CSS variables
- CSS class naming: BEM convention with `fz-` prefix for custom components
- Linting: ESLint with antfu config
- Component name prefixing conventions (`Fz` for components or `--fz-` for CSS variables)
- Browser support: Evergreen browsers only (Chrome, Firefox, Safari, Edge)
  ```js
  // browserslist config
  module.exports = [
    'last 2 Chrome versions',
    'last 2 Firefox versions',
    'last 2 Safari versions',
    'last 2 Edge versions',
    'not IE 11',
  ]
  ```

⏳ **To Be Resolved:**

- Roboto font integration strategy
- Component initialization patterns
- Tree-shaking implementation

## Performance Monitoring

- Bundle size monitoring with `size-limit`:
  ```json
  {
    "size-limit": [
      {
        "path": "dist/index.js",
        "limit": "10 KB"
      }
    ]
  }
  ```

## Enhancement Opportunities

- Theme generator tool
- Component playground
- Interactive documentation

## Next Steps

- Create component development guide and best practices (contributing-guide.md)
- Guide to writing visual-regression tests in Chromatic and Playwright

TODOS: Write WHY we chose each technology
