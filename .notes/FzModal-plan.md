# FzModal Implementation Plan

## 1. Component Name

*   **Name:** `FzModal`
*   **Reasoning:** Follows the project's `Fz` prefix convention for components.

## 2. Core `reka-ui` & Project Building Blocks

*   `DialogRoot`, `DialogPortal`, `DialogOverlay`, `DialogContent`, `DialogTitle`, `DialogDescription`, `DialogClose` (from `reka-ui`)
*   `FzButton` (Project component)
*   `FzIcon` (Project component)
*   Layout: Tailwind CSS utility classes (`fz-` prefix).

## 3. Component Structure (Conceptual Vue Template)

```vue
<template>
  <DialogRoot :open="modelValue" @update:open="emit('update:modelValue')">
    <DialogPortal>
      <DialogOverlay class="fz-modal__overlay" /> 
      <DialogContent 
        class="fz-modal__content" 
        :style="{ width: width }" 
        @escape-key-down="/* handle escape */" 
        @pointer-down-outside="/* handle outside click */"
      >
        <header class="fz-modal__header">
          <DialogTitle v-if="title" class="fz-modal__title">{{ title }}</DialogTitle>
          <DialogClose v-if="!hideCloseButton" as-child>
            <FzButton variant="ghost" size="sm" class="fz-modal__close-button">
              <FzIcon name="x-small" />
            </FzButton>
          </DialogClose>
        </header>

        <main class="fz-modal__body">
          <slot></slot> <!-- Default slot -->
        </main>

        <footer v-if="$slots.footer" class="fz-modal__footer">
          <slot name="footer"></slot> <!-- Footer slot -->
        </footer>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<script setup lang="ts">
// ... imports for Dialog*, FzButton, FzIcon ...
// ... defineProps, defineEmits ...
</script>

<style>
/* Animation using data-state attributes */

/* Overlay Fade */
.fz-modal__overlay {
  transition: opacity 0.2s ease; /* Adjust timing */
  opacity: 0;
  background-color: /* Define overlay background color using --fz-color-* variable */;
  /* Ensure overlay covers the screen */
  position: fixed;
  inset: 0; 
}
.fz-modal__overlay[data-state="open"] {
  opacity: 1;
}

/* Content Fade-Up */
.fz-modal__content {
  transition: opacity 0.2s ease, transform 0.2s ease; /* Adjust timing */
  opacity: 0;
  transform: translateY(10px); /* Adjust distance */
  /* Add other base styles: background, padding, border-radius, shadow using --fz-* variables */
  /* Centering styles (example) */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateY(10px); /* Initial offset for animation */
  /* Add base styles like background, padding, border-radius */
}
.fz-modal__content[data-state="open"] {
  opacity: 1;
  transform: translate(-50%, -50%) translateY(0); /* Final position */
}
</style>
```

## 4. Props

*   `modelValue` (Boolean, required): Controls visibility (`v-model:modelValue`).
*   `title` (String, optional): Modal title text.
*   `hideCloseButton` (Boolean, optional, default: `false`): Hides the 'X' button.
*   `persistent` (Boolean, optional, default: `false`): Prevents closing via overlay click or Escape key.
*   `width` (String, optional, default: `'520px'`): Sets modal width.

## 5. Slots

*   **`default`**: Main modal body content.
*   **`footer`**: Action buttons area.

## 6. Visual States & Interaction

*   **Open/Closed:** Controlled by `modelValue`, animated via `data-state` CSS transitions.
*   **Overlay Click:** Closes modal unless `persistent`.
*   **Escape Key:** Closes modal unless `persistent`.
*   **Close Button ('X'):** Closes the modal.
*   **Action Buttons (in footer slot):** Trigger custom events or close the modal via `DialogClose`.
*   **Focus:** Trapped within the modal when open.

## 7. Accessibility (ARIA & Keyboard)

*   `reka-ui` primitives handle most ARIA attributes (`role="dialog"`, `aria-modal="true"`, `aria-labelledby`, `aria-describedby`).
*   Use `DialogTitle` for `aria-labelledby`.
*   Ensure slot content is appropriately linked via `aria-describedby`.
*   Standard keyboard navigation (`Tab`, `Shift+Tab`, `Escape`, `Enter`/`Space`).
*   CSS transitions respect `prefers-reduced-motion`.

## 8. Responsiveness

*   Default `width: 520px`.
*   Apply `max-width` (e.g., `90vw`) for smaller screens.
*   Handle vertical scrolling within `.fz-modal__body` or `.fz-modal__content` if content overflows.

## 9. Styling & Animation

*   BEM naming (`fz-modal`, `fz-modal__header`, etc.).
*   Prefixed Tailwind utilities (`fz:`).
*   CSS variables (`--fz-*`) for theme consistency.
*   Animation handled via CSS transitions targeting `[data-state="open"]` on `.fz-modal__overlay` and `.fz-modal__content`.

## 10. File Structure

*   `src/components/FzModal/`
    *   `FzModal.vue`
    *   `index.ts`
    *   `FzModal.stories.ts`
    *   `FzModal.spec.ts` (Optional)