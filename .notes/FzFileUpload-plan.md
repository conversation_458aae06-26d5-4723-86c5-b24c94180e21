# Plan for implementing `FzFileUpload` component

... (previous sections remain the same)

## 3. Input Props

Define props for customization and configuration:

*   `modelValue`: Represents the selected `File` object (for new uploads), a `string` (for a server image URL), or `null`.
*   `accept`: String specifying allowed file extensions (e.g., `.png,.jpg,.jpeg`).
*   `maxSize`: Number representing the maximum allowed file size in bytes.
*   `placeholderIcon`: String representing the name of the `FzIcon` to display in the initial state.
*   `placeholderText`: String for the text displayed in the initial state.
*   `loading`: Boolean prop to indicate an external loading state (e.g., during actual file upload to a backend).
*   `error`: Boolean prop to indicate an external error state.
*   `errorText`: String for displaying external error messages.

... (sections 4 and 5 updated below)

## 5. UI State Rendering

*   **Initial State (`idle`):**
    *   Display the trigger element with the `placeholderIcon` and `placeholderText` when `modelValue` is `null`.
*   **File Selected State (when `modelValue` is not `null` and no validation errors):**
    *   Display the selected image.
    *   If `modelValue` is a `File` object, display a preview using `URL.createObjectURL(modelValue)`.
    *   If `modelValue` is a `string` (representing a URL), display the image using `<img :src="modelValue">`.
    *   If `modelValue` is an object with a `url` property (e.g., `{ url: string, ... }`), display the image using `<img :src="modelValue.url">`. Adjust this based on the chosen structure for server images.
    *   Display a "Delete" button (`FzButton` with trash icon).
*   **Loading State (`loading` or internal loading):**
    *   If the `loading` prop is true or an internal loading process is active, display an `FzLoader`.
*   **Error State (`error` or internal error):**
    *   Display validation error messages (as per step 4).
    *   Potentially display the `errorText` prop if an external error occurs.

## 6. File Preview

*   Add a click handler to the displayed image preview.
*   Upon clicking, open a full-screen image viewer.
*   The image source passed to the viewer should be derived from the `modelValue`:
    *   If `modelValue` is a `File`, use `URL.createObjectURL(modelValue)`.
    *   If `modelValue` is a `string` (URL), use `modelValue`.
    *   If `modelValue` is an object, use `modelValue.url` (or the appropriate property).
*   Investigate the library used in the survey passage (`devfoquz.ru/p/F682582b89c332`) for image viewing. If it's a standard dependency (e.g., part of `reka-ui` or another allowed library from `tech-stack`), use it. Otherwise, propose adding a suitable image viewing library that aligns with the tech stack.

## 7. Delete Functionality

*   Implement a click handler for the "Delete" button.
*   This handler should:
    *   If the `modelValue` was a `File` object, revoke the object URL created with `URL.revokeObjectURL`.
    *   Reset the hidden file input value to allow selecting the same file again if needed.
    *   Emit `update:modelValue(null)` to clear the selected file or server image reference in the parent component.
    *   Transition the component state back to `idle`.

... (remaining sections remain the same)