# Publishing npm Packages Under an Organization Scope

This document outlines the process for publishing multiple npm packages under a company organization scope (e.g., `@foquz`).

## 1. Creating an npm Organization

To create an npm organization:

1. **Sign up for an npm account** if you don't have one already at [npmjs.com](https://www.npmjs.com/signup).

2. **Create the organization**:
   - Log in to your npm account
   - Click your profile picture in the upper right corner
   - Select "Add an Organization"
   - Enter your organization name (e.g., `foquz`) - this will become your scope (`@foquz`)
   - Choose a plan:
     - "Unlimited public packages" (free plan)
     - "Unlimited private packages" (paid plan)
   - Click "Create" or "Buy" depending on your plan selection

3. **Invite team members** (optional):
   - Enter npm usernames or email addresses
   - Assign them to teams
   - Set appropriate permissions

## 2. Scoping Your Packages

To scope your packages under your organization:

### For New Packages

When creating a new package, use the `--scope` flag with `npm init`:

```bash
npm init --scope=@foquz
```

This will automatically prefix your package name with `@foquz/` in the `package.json` file.

### For Existing Packages

Manually update the `name` field in your `package.json` file:

```json
{
  "name": "@foquz/your-package-name",
  "version": "1.0.0",
  // other fields...
}
```

## 3. Publishing Packages

### Publishing Public Packages

By default, scoped packages are published as private. To publish a public package:

```bash
npm publish --access public
```

This is required if your organization is on the free plan.

### Publishing Private Packages

If your organization has a paid plan with private packages:

```bash
npm publish
```

Scoped packages are published as private by default if your plan allows it.

## 4. Configuring npm Client

To streamline the publishing process, you can configure your npm client:

### Setting Default Scope

For all new packages globally:

```bash
npm config set scope @foquz --global
```

For a single project (run in the project directory):

```bash
npm config set scope @foquz
```

### Setting Default Access

To default to public publishing for a single package:

```bash
npm config set access public
```

To default to public publishing for all packages globally:

```bash
npm config set access public --global
```

**Note**: Setting global access affects all packages, including personal ones.

## 5. Managing Team Access

To manage team access to packages:

### Creating Teams

```bash
npm team create @foquz:team-name
```

### Adding Members to Teams

```bash
npm team add @foquz:team-name username
```

### Granting Package Access

```bash
npm access grant read-only @foquz:team-name @foquz/package-name
npm access grant read-write @foquz:team-name @foquz/package-name
```

### Revoking Access

```bash
npm access revoke @foquz:team-name @foquz/package-name
```

## 6. References

- [Creating an organization](https://docs.npmjs.com/creating-an-organization/)
- [Creating and publishing an organization scoped package](https://docs.npmjs.com/creating-and-publishing-an-organization-scoped-package/)
- [About organization scopes and packages](https://docs.npmjs.com/about-organization-scopes-and-packages/)
- [Configuring your npm client with your organization settings](https://docs.npmjs.com/configuring-your-npm-client-with-your-organization-settings/)
- [npm team documentation](https://docs.npmjs.com/cli/v8/commands/npm-team)
- [npm access documentation](https://docs.npmjs.com/cli/v8/commands/npm-access)