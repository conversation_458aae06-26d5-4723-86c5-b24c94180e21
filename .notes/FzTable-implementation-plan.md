# FzTable Implementation Plan

## Component Overview

The `FzTable` component will be a powerful, customizable data table based on TanStack Table with the following key features:
- Sortable and filterable columns
- Sticky header capability
- Horizontal scrolling with navigation arrows
- Row virtualization for performance
- Loading and empty states
- Clear TypeScript types

## Component Name

- **Name:** `FzTable`
- **Reasoning:** Follows the project's `Fz` prefix convention for components.

## Dependencies

- **Core Library:** `@tanstack/vue-table`
- **Virtualization:** `@tanstack/vue-virtual`
- **Project Components:**
  - `FzTooltip` (for column headers with info)
  - `FzLoader` (for loading state)
  - `FzIcon` (for sort indicators, arrows)

## Component Structure

```
/src/components/FzTable/
  ├── FzTable.vue           # Main component
  ├── index.ts              # Export file
  └── FzTable.stories.ts    # Storybook stories
```

## TypeScript Interfaces

```typescript
// Column definition - aligned with TanStack Table's ColumnDef for flexibility
// It's recommended to use FlexRender for header and cell rendering.
export interface FzTableColumn<TData extends object = object, TValue = any> {
  id?: string;
  accessorKey?: keyof TData | string;
  accessorFn?: (row: TData) => TValue;
  header?: string | ((props: { column: any /* TanStack Column type */ }) => any); // Allow string or render function
  cell?: (props: { row: TData, getValue: () => TValue, column: any, table: any }) => any; // Allow render function
  footer?: string | ((props: { column: any }) => any);
  size?: number;
  minSize?: number;
  maxSize?: number;
  enableSorting?: boolean;
  enableFiltering?: boolean; // Note: filtering is primarily driven by a top-level search term
  meta?: {
    info?: string
    // Add other custom meta properties as needed
  };
}

// Table props
export interface FzTableProps<TData extends object = object> {
  data: TData[];
  columns: FzTableColumn<TData>[]; // Uses the refined FzTableColumn
  stickyHeader?: boolean;
  loading?: boolean;
  noResultsText?: string;
  enableSorting?: boolean; // Global toggle for sorting
  searchTerm?: string; // External search term for filtering
  enablePagination?: boolean; // Future consideration
  enableVirtualization?: boolean;
  height?: string | number; // Fixed height is crucial for virtualization
  maxHeight?: string | number;
  showScrollArrows?: boolean;
}
```

## Component API

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | `Array` | `[]` | Data to display in the table |
| `columns` | `Array` | `[]` | Column definitions (using the refined `FzTableColumn` interface). Use `FlexRender` for complex header/cell content. |
| `stickyHeader` | `Boolean` | `false` | Enable sticky header. Works best with a defined `height`. |
| `loading` | `Boolean` | `false` | Show loading state |
| `noResultsText` | `String` | `'No results found'` | Text to show when there are no results or after filtering |
| `enableSorting` | `Boolean` | `true` | Globally enable/disable sorting for all columns. Individual columns can override this via `FzTableColumn.enableSorting`. |
| `searchTerm` | `String` | `undefined` | External search term to filter table data. The component will handle the filtering logic. |
| `enablePagination` | `Boolean` | `false` | Enable pagination (Future consideration) |
| `enableVirtualization` | `Boolean` | `false` | Enable row virtualization. Requires a fixed `height` on the table container. |
| `height` | `String/Number` | `undefined` | Table container height (e.g., `'500px'`, `500`). Crucial for virtualization and sticky header. |
| `maxHeight` | `String/Number` | `undefined` | Table container max height |
| `showScrollArrows` | `Boolean` | `true` | Show scroll arrows when content overflows horizontally |

### Events

| Event | Payload | Description |
|-------|---------|-------------|
| `row-click` | `{ row, event }` | Emitted when a row is clicked |
| `sort-change` | `{ sorting }` | Emitted when sorting changes |
| `filter-change` | `{ filters }` | Emitted when filtering changes |
| `page-change` | `{ pagination }` | Emitted when page changes |

### Slots

| Slot | Props | Description |
|------|-------|-------------|
| `header-[columnId]` | `{ column }` (TanStack Table column instance) | Custom header rendering for a specific column, identified by its `id`. Use `FlexRender`. |
| `empty` | None | Custom empty state when no data or no filter results |
| `loading` | None | Custom loading state |
| `cell-[columnId]` | `{ row, getValue, column, table }` (TanStack Table cell context) | Custom cell rendering for a specific column, identified by its `id`. Use `FlexRender`. |

## Implementation Steps

1. **Core Table Setup**
   - Create basic table structure with TanStack Table.
   - Implement column definitions using the `FzTableColumn` interface, leveraging `FlexRender` for header and cell content.
   - Implement internal filtering logic based on the `searchTerm` prop.
   - Add support for sorting, including UI indicators and `getToggleSortingHandler`.

2. **Sticky Header**
   - Implement sticky header functionality. Ensure it works correctly when `height` is set and with virtualization.
   - Add shadow effect when scrolled (if applicable).

3. **Scrollable Table**
   - Implement horizontal scrolling.
   - Add scroll arrows when content overflows.
   - Handle arrow click to scroll horizontally.

4. **Virtualization**
   - Integrate TanStack Virtual for row virtualization when `enableVirtualization` is true and `height` is provided.
   - Use a `computed` property for `rowVirtualizerOptions`.
   - Implement dynamic row height measurement using `measureElement` and `rowVirtualizer.value.measureElement(el)`.
   - Render only visible rows for performance.

5. **Loading and Empty States**
   - Implement loading state with `FzLoader`.
   - Implement empty state with customizable message (from `noResultsText` prop).
   - Add slots for custom loading and empty states (`loading`, `empty`).

6. **Styling and Animations**
   - Style table with Tailwind CSS following project conventions (`fz-` prefix for BEM, `fz:` for utilities).
   - Adapt table structure styling (`display: grid`, `display: flex` for `table`, `thead`, `tbody`, `tr`) when virtualization is enabled to ensure correct layout and row positioning.

## Detailed Implementation Plan

### 1. Core Table Setup (with Filtering and Sorting)

```vue
<script setup lang="ts">
import { 
  getCoreRowModel, 
  getSortedRowModel, 
  useVueTable, 
  FlexRender 
} from '@tanstack/vue-table'
import { computed, ref, h } from 'vue'
import { FzIcon } from '../FzIcon' // Assuming FzIcon is available
// import { FzLoader } from '../FzLoader' // Already in the plan
// import { FzTooltip } from '../FzTooltip' // Already in the plan
import type { FzTableColumn, FzTableProps } from './FzTable.types' // Assuming types are in a separate file or defined above

// Define props according to FzTableProps
const props = withDefaults(defineProps<FzTableProps<any>>(), {
  data: () => [],
  columns: () => [],
  enableSorting: true,
  loading: false,
  noResultsText: 'No results found',
  stickyHeader: false,
  enableVirtualization: false,
  showScrollArrows: true,
  // searchTerm: undefined, // Default is undefined
  // height: undefined,
  // maxHeight: undefined
})

// Filtering logic based on searchTerm
const filteredData = computed(() => {
  const term = props.searchTerm?.toLowerCase();
  if (!term) {
    return props.data;
  }
  return props.data.filter(row => {
    return props.columns.some(column => {
      let value;
      if (column.accessorFn) {
        value = column.accessorFn(row);
      } else if (column.accessorKey) {
        value = row[column.accessorKey as string];
      }
      return String(value).toLowerCase().includes(term);
    });
  });
});

// Table setup
const table = useVueTable({
  get data() { return filteredData.value },
  get columns() { return props.columns as any[] /* Cast needed if FzTableColumn isn't directly ColumnDef */ },
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  enableSorting: computed(() => props.enableSorting),
  // debugTable: true, // Optional for development
})

const tableContainerRef = ref<HTMLDivElement | null>(null); // Moved here for unified access

</script>

<template>
  <div class="fz-table-wrapper" :style="{ height: props.height ? `${props.height}px` : undefined, maxHeight: props.maxHeight ? `${props.maxHeight}px` : undefined }">
    <!-- Optional: Scroll Arrows would be positioned absolutely relative to this wrapper or the scrollable container -->

    <div 
      ref="tableContainerRef" 
      class="fz-table-scroll-container" 
      :style="{
        overflow: 'auto', 
        position: 'relative', // Needed for sticky header and virtualization
        height: props.enableVirtualization ? (props.height ? `${props.height}px` : '100%') : undefined,
      }"
    >
      <table class="fz-table" :style="{ display: props.enableVirtualization ? 'grid' : 'table' }">
        <thead 
          class="fz-table__header" 
          :style="{
            display: props.enableVirtualization ? 'grid' : 'table-header-group',
            position: props.stickyHeader ? 'sticky' : undefined,
            top: props.stickyHeader ? 0 : undefined,
            zIndex: props.stickyHeader ? 1 : undefined,
          }"
        >
          <tr 
            v-for="headerGroup in table.getHeaderGroups()" 
            :key="headerGroup.id" 
            class="fz-table__header-row"
            :style="{ display: props.enableVirtualization ? 'flex' : 'table-row', width: props.enableVirtualization ? '100%' : undefined }"
          >
            <th 
              v-for="header in headerGroup.headers" 
              :key="header.id" 
              :colspan="header.colSpan"
              class="fz-table__header-cell"
              :style="{ 
                width: props.enableVirtualization ? `${header.getSize()}px` : undefined, 
                cursor: header.column.getCanSort() ? 'pointer' : undefined 
              }"
              @click="header.column.getCanSort() ? header.column.getToggleSortingHandler()?.($event) : null"
            >
              <FlexRender 
                :render="header.column.columnDef.header" 
                :props="header.getContext()" 
              />
              <template v-if="header.column.getIsSorted()">
                <FzIcon :name="header.column.getIsSorted() === 'asc' ? 'arrow-up' : 'arrow-down'" />
              </template>
              <!-- Optional: FzTooltip for header.column.columnDef.meta?.info -->
            </th>
          </tr>
        </thead>
        <tbody 
          class="fz-table__body" 
          :style="{
            display: props.enableVirtualization ? 'grid' : 'table-row-group',
            position: props.enableVirtualization ? 'relative' : undefined,
            height: props.enableVirtualization ? `${totalSize}px` : undefined, // totalSize from virtualizer
          }"
        >
          <!-- Conditional rendering for loading and empty states -->
          <template v-if="props.loading">
            <tr><td :colspan="table.getAllColumns().length">Loading...</td></tr>
          </template>
          <template v-else-if="!table.getRowModel().rows.length">
            <tr><td :colspan="table.getAllColumns().length">{{ props.noResultsText }}</td></tr>
          </template>
          
          <template v-else-if="props.enableVirtualization">
            <!-- Virtualized rows will be rendered here by the virtualizer setup -->
          </template>
          <template v-else>
            <tr 
              v-for="row in table.getRowModel().rows" 
              :key="row.id" 
              class="fz-table__row"
              :style="{ display: props.enableVirtualization ? 'flex' : 'table-row', width: props.enableVirtualization ? '100%' : undefined }"
            >
              <td 
                v-for="cell in row.getVisibleCells()" 
                :key="cell.id" 
                class="fz-table__cell"
                :style="{ display: props.enableVirtualization ? 'flex' : 'table-cell', width: props.enableVirtualization ? `${cell.column.getSize()}px` : undefined }"
              >
                <FlexRender :render="cell.column.columnDef.cell" :props="cell.getContext()" />
              </td>
            </tr>
          </template>
        </tbody>
      </table>
      <!-- This div is for virtualizer total size when tbody is not the direct scrollable list -->
      <div v-if="props.enableVirtualization" :style="{ height: `${totalSize}px` }"></div>
    </div>
  </div>
</template>
```

### 2. Virtualization Integration (within FzTable.vue setup)

```typescript
// (Continuing FzTable.vue script setup)
import { useVirtualizer } from '@tanstack/vue-virtual'

// ... (props, filteredData, table setup from above)

// const tableContainerRef = ref<HTMLDivElement | null>(null); // Already defined above

const rows = computed(() => table.getRowModel().rows) // For virtualizer

const rowVirtualizerOptions = computed(() => ({
  count: rows.value.length,
  getScrollElement: () => tableContainerRef.value, // Ensure this ref points to the scrollable container
  estimateSize: () => 35, // Estimated row height (can be a prop or a more sophisticated estimate)
  overscan: 5, // Or a more suitable number based on estimateSize
  measureElement: typeof window !== 'undefined' && navigator.webdriver ? undefined : measureElementFn, // For dynamic row heights
}))

const rowVirtualizer = useVirtualizer(rowVirtualizerOptions)

const virtualRows = computed(() => rowVirtualizer.value.getVirtualItems())
const totalSize = computed(() => rowVirtualizer.value.getTotalSize())

function measureElementFn(el: Element | undefined) {
  if (!el) return;
  // Timeout to ensure styles are applied if they change row height dynamically
  setTimeout(() => {
    rowVirtualizer.value.measureElement(el);
  })
}

// Template for virtualized rows (to be placed inside tbody if enableVirtualization is true)
/*
<template v-if="props.enableVirtualization">
  <tr
    v-for="vRow in virtualRows"
    :key="rows[vRow.index].id"
    :data-index="vRow.index" // Important for measureElement
    :ref="(el) => measureElementFn(el as Element | undefined)" // Correct ref binding for measureElement
    class="fz-table__row"
    :style="{
      display: 'flex',
      position: 'absolute',
      transform: `translateY(${vRow.start}px)`,
      width: '100%',
      height: `${rows[vRow.index].getVisibleCells()[0]?.column.getSize()}px`, // Example for fixed height, or dynamic
    }"
  >
    <td
      v-for="cell in rows[vRow.index].getVisibleCells()"
      :key="cell.id"
      class="fz-table__cell"
      :style="{
        display: 'flex',
        width: `${cell.column.getSize()}px`,
      }"
    >
      <FlexRender :render="cell.column.columnDef.cell" :props="cell.getContext()" />
    </td>
  </tr>
</template>
*/
```

### 3. Scroll Arrows Implementation

This section remains conceptually the same. The arrow components (`<button>`) would be placed within the `fz-table-wrapper` or positioned relative to `fz-table-scroll-container`. Their visibility (`canScrollLeft`, `canScrollRight`) would be determined by observing `tableContainerRef.value.scrollLeft`, `scrollWidth`, and `clientWidth`. The `@click` handlers would programmatically set `tableContainerRef.value.scrollLeft`.

```vue
// (Inside FzTable.vue template, likely within fz-table-wrapper)

// <button v-if="canScrollLeft" @click="scrollHorizontal(-200)">Left</button>
// <button v-if="canScrollRight" @click="scrollHorizontal(200)">Right</button>

// (Inside FzTable.vue script setup)
/*
const canScrollLeft = ref(false);
const canScrollRight = ref(false);

function updateScrollButtonVisibility() {
  const el = tableContainerRef.value;
  if (el) {
    canScrollLeft.value = el.scrollLeft > 0;
    canScrollRight.value = el.scrollLeft < (el.scrollWidth - el.clientWidth -1); // -1 for subpixel precision issues
  }
}

function scrollHorizontal(amount: number) {
  if (tableContainerRef.value) {
    tableContainerRef.value.scrollLeft += amount;
  }
}

// Call updateScrollButtonVisibility on mount, and on scroll event of tableContainerRef
// Consider using a ResizeObserver on tableContainerRef as well.
*/
```

## Styling Approach

The component will use Tailwind CSS with the project's `fz:` prefix convention for utilities and `fz-` prefix for BEM classes. Specific attention will be paid to styles required for virtualization and sticky headers.

```css
.fz-table-wrapper {
  position: relative; /* For positioning scroll arrows if needed */
  /* height and maxHeight will be applied via props */
}

.fz-table-scroll-container {
  overflow: auto; /* Enables scrolling */
  position: relative; /* Context for sticky header & virtualization absolute positioning */
  /* height controlled by props, especially for virtualization */
}

.fz-table {
  width: 100%;
  border-collapse: separate; /* or collapse, depending on design */
  border-spacing: 0;
  /* When virtualization is enabled, display: grid will be applied inline */
}

.fz-table__header {
  /* position: sticky, top: 0, z-index: 1 will be applied inline for stickyHeader prop */
  background-color: var(--fz-color-surface-light); /* Example background */
  /* When virtualization is enabled, display: grid will be applied inline */
}

.fz-table__header-row {
  /* When virtualization is enabled, display: flex, width: 100% will be applied inline */
}

.fz-table__header-cell {
  padding: var(--fz-spacing-2) var(--fz-spacing-3); /* Example padding */
  text-align: left;
  font-weight: bold;
  border-bottom: 1px solid var(--fz-color-border-default);
  /* When virtualization is enabled, width is set by header.getSize() inline */
}

.fz-table__header-cell--sortable {
  cursor: pointer;
}

.fz-table__body {
  /* When virtualization is enabled, display: grid, position: relative, height (totalSize) will be applied inline */
}

.fz-table__row {
  border-bottom: 1px solid var(--fz-color-border-subtle);
  /* When virtualization is enabled, display: flex, position: absolute, transform: translateY, width: 100% will be applied inline */
}

.fz-table__row:hover {
  background-color: var(--fz-color-surface-hover);
}

.fz-table__cell {
  padding: var(--fz-spacing-2) var(--fz-spacing-3); /* Example padding */
  /* When virtualization is enabled, display: flex, width is set by cell.column.getSize() inline */
}

.fz-table__scroll-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  /* background, padding, border, etc. for the button */
  /* Example: */
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--fz-color-border-default);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.fz-table__scroll-arrow--left {
  left: 8px;
}

.fz-table__scroll-arrow--right {
  right: 8px;
}

/* Placeholder for loading/empty states */
.fz-table__loading-placeholder,
.fz-table__empty-placeholder {
  text-align: center;
  padding: var(--fz-spacing-10);
  color: var(--fz-color-text-secondary);
}
```

## Next Steps

1.  Create the basic component file structure (`FzTable.vue`, `index.ts`).
2.  Define `FzTableProps` and `FzTableColumn` TypeScript interfaces (potentially in a `FzTable.types.ts`).
3.  Implement the core table functionality within `FzTable.vue`:
    - Props setup with defaults.
    - `filteredData` computed property based on `searchTerm`.
    - TanStack Table instance (`useVueTable`) with core models, sorting, and data.
    - Basic template structure for headers and non-virtualized rows using `FlexRender`.
    - Sorting interaction and UI indicators.
4.  Implement virtualization:
    - Add `tableContainerRef`.
    - Set up `useVirtualizer` with `rowVirtualizerOptions` (including `getScrollElement`, `estimateSize`, `measureElement`).
    - Conditionally render virtualized rows in the template using `vRow.start` for positioning and `measureElementFn`.
    - Adjust table/thead/tbody/tr/td styling (`display: grid/flex`) dynamically when virtualization is active.
5.  Implement sticky header functionality, ensuring compatibility with virtualization and fixed height.
6.  Implement horizontal scrolling with navigation arrows (`canScrollLeft`, `canScrollRight`, scroll handlers).
7.  Implement loading and empty states (with slots).
8.  Thoroughly style the component using Tailwind CSS and project BEM conventions, ensuring responsiveness.
9.  Create Storybook stories (`FzTable.stories.ts`) to document all features and props (various data, virtualization on/off, sticky header, loading/empty states, etc.).
10. Write unit tests for the component (`FzTable.spec.ts`), covering core logic, filtering, sorting, and virtualization interactions if feasible.
