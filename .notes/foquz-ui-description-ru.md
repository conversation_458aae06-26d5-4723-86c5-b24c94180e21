# Обзор Foquz UI

## Цели проекта

- Миграция интерфейса компании на [Vue 3](https://vuejs.org/)
- Гибкие, настраиваемые компоненты
- Маленький бандл сайз

## Структура проекта

```
/src
  /assets         # Ресурсы (шрифт Roboto)
  /components     # Компоненты
    /Button       # Пример структуры компонента
      Button.vue
      Button.spec.ts # Юнит тесты (если будут)
      Button.stories.ts # Сторис (Изолированная презентация компонента)
      index.ts
  /composables    # Vue композаблы
  /styles         # Глобальные стили
  /config         # Конфигурации
  /utils          # Утилиты
```

## Зависимости

### Основные

- [Vue 3](https://vuejs.org/) в качестве фреймворка
- [TypeScript](https://www.typescriptlang.org/) для типизации
- [Vite](https://vitejs.dev/) для сборки
- [Reka UI](https://reka-ui.com/) в качестве базовой styless библиотеки компонентов

### Стилизация

- [TailwindCSS](https://tailwindcss.com/) с CSS-переменными
- [PostCSS](https://postcss.org/) плагины

### Функциональность

- Работа с иконками: [`vite-svg-loader`](https://github.com/jpkleemans/vite-svg-loader)
- Работа с датами: [`dayjs`](https://day.js.org/)
- Popup галереи: [`fancybox`](https://fancyapps.com/fancybox/) (Может есть что-то другое, более легковесное?)
- Слайдеры: [`embla-carousel`](https://www.embla-carousel.com/) (Может быть [`swiper`](https://swiperjs.com/))
- Формы: [`tanstack-forms`](https://tanstack.com/form/latest)
- Таблицы: [`tanstack-table`](https://tanstack.com/table/latest)
- Модальные окна: `reka-ui`
- Сайдшиты: `reka-ui`
- Тосты: `reka-ui`
- Календари, датапикеры: `reka-ui`

### Документация

- [Storybook](https://storybook.js.org/) галерея компонентов

### Тестирование

- [Vitest](https://vitest.dev/)
- [Vue Test Utils](https://test-utils.vuejs.org/)
- [Playwright](https://playwright.dev/)
- [Chromatic](https://www.chromatic.com/) для визуального тестирования компонентов

## Работа с [TailwindCSS](https://tailwindcss.com/)

На самом нижнем уровне избегаем CSS class bloat и не используем tailwind классы
Вместо них ихспользуем CSS-переменные с префиксом `--fz-`:

```css
.btn {
  min-height: var(--fz-min-button-height);
  padding: var(--fz-p-2);
}
```

На более высоком уровне допустимо использовать tailwind классы для общего лэйаута компонента, например так (Пример компонента `Card`):

```vue
<template>
  <div class="fz-card">
    <div class="fz-card__header fz:p-4 fz:bg-gray-50 fz:border-b">
      <slot name="header">
        <h3 class="fz:text-lg fz:font-medium fz:text-gray-900">
          {{ title }}
        </h3>
      </slot>
    </div>
    <div class="fz-card__body fz:p-4">
      <slot />
    </div>
    <div
      v-if="$slots.footer"
      class="fz-card__footer fz:p-4 fz:bg-gray-50 fz:border-t"
    >
      <slot name="footer" />
    </div>
  </div>
</template>
```

## Технологические решения

- Работа с иконками: [`vite-svg-loader`](https://github.com/jpkleemans/vite-svg-loader)
- Работа с датами: [`dayjs`](https://day.js.org/)
- Стилизация: [TailwindCSS](https://tailwindcss.com/) с префиксом `--fz-`
- Именование CSS классов: методология BEM с префиксом `fz-`
- Линтинг: [ESLint](https://eslint.org/)
- Префиксы компонентов: `Fz` для компонентов, `--fz-` для CSS-переменных
- Поддержка браузеров: только современные (Chrome, Firefox, Safari, Edge)

⏳ **Требует решения:**

- Интеграция шрифта Roboto

## Мониторинг производительности

- Контроль размера бандла: [`size-limit`](https://github.com/ai/size-limit)

## Возможности для улучшения

- Генератор тем
- Интерактивная документация

## TODO:

- Создание руководства по разработке компонентов
- Руководство по визуальному регрессионному тестированию ([Chromatic](https://www.chromatic.com/))
