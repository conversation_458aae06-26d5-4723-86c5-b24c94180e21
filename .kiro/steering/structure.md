# Project Structure

## Root Directory
```
/src                 # Source code
/dist               # Built library output
/.storybook         # Storybook configuration
/storybook-static   # Built Storybook site
```

## Source Structure (`/src`)
```
/assets             # Static assets
  /icons            # SVG icon files
  /foquz-roboto-font # Custom Roboto font files
/components         # Vue components (main library code)
/composables        # Vue composables/hooks
/constants          # Application constants
/helpers            # Utility helper functions
/styles             # Global CSS and theme files
/util               # General utilities
index.ts            # Main library entry point
```

## Component Organization
Each component follows a consistent directory structure:

```
/src/components/FzComponentName/
  FzComponentName.vue      # Main component implementation
  FzComponentName.stories.ts # Storybook stories
  FzComponentName.spec.ts  # Unit tests (optional)
  index.ts                 # Export file
```

### Component Files
- **`.vue`** - Main component with `<script setup>`, `<template>`, and `<style>`
- **`.stories.ts`** - Storybook stories for component documentation
- **`.spec.ts`** - Vitest unit tests (when needed)
- **`index.ts`** - Simple export: `export { FzComponentName } from './FzComponentName.vue'`

## Key Files
- **`src/index.ts`** - Main entry point, re-exports all components and utilities
- **`src/components/index.ts`** - Central component exports
- **`src/styles/index.css`** - Main stylesheet imports
- **`src/styles/theme.css`** - CSS variables and theme definitions
- **`package.json`** - Dependencies and build scripts
- **`vite.config.ts`** - Build configuration
- **`tsconfig.json`** - TypeScript configuration

## Naming Conventions
- **Components**: `FzComponentName` (PascalCase with Fz prefix)
- **Files**: `FzComponentName.vue`, `FzComponentName.stories.ts`
- **Directories**: `/FzComponentName/` (matching component name)
- **CSS Classes**: `fz-component-name` (kebab-case with fz- prefix)
- **CSS Variables**: `--fz-variable-name` (kebab-case with --fz- prefix)
- **Tailwind Classes**: `fz:utility-class` (with fz: prefix)

## Import Patterns
```typescript
// Component imports (internal)
import { FzButton } from '../FzButton'

// Library imports (external usage)
import { FzButton, FzInput } from '@foquz/foquz-ui'
import '@foquz/foquz-ui/foquz-ui.css'
import '@foquz/foquz-ui/theme.css'
```