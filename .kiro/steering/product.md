# Foquz UI Component Library

Foquz UI is a Vue 3 component library designed for company interface migration. The library provides flexible, customizable components with a focus on small bundle size and modern browser support.

## Key Goals
- Migrate company interfaces to Vue 3
- Provide flexible, customizable components
- Maintain small bundle size
- Support only modern browsers (Chrome, Firefox, Safari, Edge)

## Library Features
- TypeScript-first development
- Comprehensive Storybook documentation
- Visual regression testing with Chromatic
- Performance monitoring with size-limit
- Published to private GitLab npm registry

## Component Prefix
All components use the `Fz` prefix (e.g., `FzButton`, `FzInput`, `FzModal`) to maintain consistency and avoid naming conflicts.