# Technology Stack

## Core Technologies
- **Vue 3** - Component framework with Composition API
- **TypeScript** - Type safety and better developer experience
- **Vite** - Build tool and development server
- **Reka UI** - Base headless component library
- **TailwindCSS v4** - Utility-first CSS framework with `fz:` prefix
- **PostCSS** - CSS processing and transformations

## Key Dependencies
- **Icons**: `vite-svg-loader` - SVG icons as Vue components
- **Dates**: `dayjs` - Lightweight date manipulation
- **Forms**: `@tanstack/vue-form` - Form state management
- **Tables**: `@tanstack/vue-table` - Data table functionality
- **Virtualization**: `@tanstack/vue-virtual` - Virtual scrolling
- **Scrollbars**: `simplebar-vue` - Custom scrollbars
- **Galleries**: `@fancyapps/ui` - Image galleries and lightboxes
- **Input Masking**: `@maskito/vue` - Input formatting and validation

## Development Tools
- **ESLint** with `@antfu/eslint-config` - Code linting
- **Storybook** - Component documentation and testing
- **Vitest** - Unit testing framework
- **Playwright** - E2E testing
- **Chromatic** - Visual regression testing
- **size-limit** - Bundle size monitoring

## Common Commands

### Development
```bash
npm run dev          # Start development server
npm run storybook    # Start Storybook dev server
```

### Building
```bash
npm run build        # Build library for production
npm run build-storybook  # Build Storybook static site
```

### Testing & Quality
```bash
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run type-check   # TypeScript type checking
npm run size         # Check bundle size
npm run chromatic    # Run visual regression tests
```

## Build Configuration
- **Entry**: `src/index.ts`
- **Output**: ES modules only (`dist/foquz-ui.es.js`)
- **CSS**: Separate CSS file (`dist/foquz-ui.css`) + theme (`dist/theme.css`)
- **Types**: Generated TypeScript declarations
- **External**: Vue is externalized (peer dependency)