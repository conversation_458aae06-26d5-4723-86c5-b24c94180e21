stages:
  - lint
  - prebuild
  - build
  - publish
  - deploy
  - test
  - update_snapshots

variables:
  TAG: latest

lint:
  stage: lint
  image:
    name: node:20-alpine
  script:
    - npm install
    - npm run lint

publish-npm:
  stage: publish
  image:
    name: node:20-alpine
  script:
    - chmod +x scripts/publish-npm.sh
    - ./scripts/publish-npm.sh
  rules:
    - if: $CI_COMMIT_BRANCH == "release"
    - if: $CI_COMMIT_BRANCH == "bugfixes"
    - if: $CI_COMMIT_BRANCH =~ /^TASK-/
    - if: $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/

include:
  - images/cicd/build-images-ci.yml
