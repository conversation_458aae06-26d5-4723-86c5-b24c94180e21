import path from 'node:path'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'
import tailwindAutoReference from 'vite-plugin-vue-tailwind-auto-reference'
import svgLoader from 'vite-svg-loader'

// https://vite.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  plugins: [
    vue(),
    tailwindAutoReference(),
    tailwindcss(),
    svgLoader({
      svgoConfig: {
        plugins: [
          {
            name: 'preset-default',
            params: {
              overrides: {
                removeViewBox: false,
              },
            },
          },
          {
            name: 'convertColors',
            params: {
              currentColor: true,
            },
          },
        ],
      },
    }),
    dts({
      tsconfigPath: './tsconfig.build.json',
      outDir: 'dist',
      cleanVueFileName: true,
      rollupTypes: true,
      exclude: [
        'src/test/**',
        'src/**/*.stories.ts',
        'src/**/*.story.vue',
        'src/**/story/**',
      ],
    }),
  ],
  build: {
    minify: 'esbuild',
    cssCodeSplit: true,
    lib: {
      entry: path.resolve(__dirname, 'src/index.ts'),
      name: 'FoquzUI',
      fileName: format => `foquz-ui.${format}.js`,
      formats: ['es'],
    },
    rollupOptions: {
      input: {
        'foquz-ui': path.resolve(__dirname, 'src/index.ts'),
        'theme': path.resolve(__dirname, 'src/styles/theme.css'),
      },
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue',
        },
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'foquz-ui') {
            return `foquz-ui.es.js`
          }
          return `${chunkInfo.name}.es.js`
        },
        assetFileNames: (assetInfo) => {
          if (assetInfo.names.includes('theme.css')) {
            return 'theme.css'
          }
          if (assetInfo.names.includes('foquz-ui.css')) {
            return 'foquz-ui.css'
          }
          return 'assets/[name]-[hash][extname]'
        },
      },
    },
  },
})
