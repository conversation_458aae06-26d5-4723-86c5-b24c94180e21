# Foquz UI Development Guidelines

This document contains the development guidelines and best practices for the Foquz UI component library. These rules ensure consistency, maintainability, and quality across the codebase.

## Tech Stack

All components and features must use the following technology stack:

- **Vue 3 + TypeScript** for component development
- **Vite** for building
- **Reka UI** as the base component library
- **TailwindCSS** with CSS variables and PostCSS for styling
- **ESLint** with antfu configuration for code linting

### Required Dependencies

Use these specific dependencies for features:
- **Icons**: `vite-svg-loader`
- **Dates**: `dayjs`
- **Forms**: `tanstack-forms`
- **Tables**: `tanstack-table`
- **UI components**: `reka-ui`
- **Modal windows**: `reka-ui`
- **Sidesheets**: `reka-ui`
- **Toasts**: `reka-ui`
- **Calendars/date pickers**: `reka-ui`
- **Custom scrollbars**: `simplebar` and `simplebar-vue`

### Browser Support
Only modern browsers are supported (Chrome, Firefox, Safari, Edge).

## Project Structure

The project must follow this structure:

```
/src
  /assets         # Resources (Roboto font, icons)
  /components     # Components in FzComponentName directories
  /composables    # Vue composables
  /styles         # Global styles
  /config         # Configurations
  /utils          # Utilities
```

### Documentation & Testing
- **Storybook** must be used for component documentation
- Each component should have a corresponding story file
- **Unit tests**: Vitest + Vue Test Utils
- **E2E tests**: Playwright
- **Visual regression testing**: Chromatic

## Component Structure

Components must be placed in dedicated directories: `/src/components/FzComponentName/`

### Required files for each component:
- `FzComponentName.vue` - Component implementation
- `index.ts` - Export file

### Optional files:
- `FzComponentName.spec.ts` - Unit tests
- `FzComponentName.stories.ts` - Storybook stories

## Naming Conventions

- **Component names** must use `Fz` prefix (Example: `FzButton`)
- **CSS variables** must use `--fz-` prefix (Example: `--fz-color-surface-background`)
- **TailwindCSS classes** must use `fz:` prefix for component layouts (Example: `fz:p-4`, `fz:bg-gray-50`)
- **Custom CSS classes** should follow BEM convention with `fz-` prefix:
  - **Block**: Main component (Example: `fz-card`)
  - **Element**: Component parts (Example: `fz-card__header`, `fz-card__body`)
  - **Modifier**: Component variations (Example: `fz-card--primary`, `fz-button--large`)

## CSS Guidelines

### Base Level CSS
Use CSS variables with `fz` prefix instead of direct Tailwind classes:

```css
/* DO THIS */
.btn {
  min-height: var(--fz-min-button-height);
  padding: var(--fz-spacing-2);
}

/* DON'T DO THIS */
.btn {
  min-height: 2.5rem; /* Use CSS variable instead */
  padding: 0.5rem; /* Use CSS variable instead */
}
```

**IMPORTANT**: NEVER USE @apply! Only use CSS variables for low-level components and use classes with `fz:` for layout to high-level components.

### Layout Level
Use Tailwind classes with `fz:` prefix for component layout:

```vue
<!-- Example of proper layout with prefixed classes -->
<div class="fz-card fz-card--rounded">
  <div class="fz-card__header fz:p-4 fz:bg-gray-50 fz:border-b">
    <!-- Content -->
  </div>
</div>
```

### Component Level
Use BEM naming conventions with `fz-` prefix for blocks, elements, and modifiers when structuring component classes, and combine with Tailwind utility classes prefixed `fz:` for spacing, colors, and other utilities:

```vue
<!-- Example of BEM naming with prefixed utility classes -->
<div class="fz-card fz-card--rounded">
  <div class="fz-card__header">
    <!-- Header content -->
  </div>

  <div class="fz-card__body fz:p-4">
    <!-- Body content -->
  </div>

  <div class="fz-card__footer fz:p-4 fz:text-right">
    <!-- Footer content -->
  </div>
</div>
```

### Available Theme Variables

Theme variables are defined in `src/styles/theme.css` with `--fz-` prefix. Key namespaces include:

| Namespace | Utility classes |
|-----------|----------------|
| `--color-*` | Color utilities like bg-red-500, text-sky-300 |
| `--font-*` | Font family utilities like font-sans |
| `--text-*` | Font size utilities like text-xl |
| `--font-weight-*` | Font weight utilities like font-bold |
| `--spacing-*` | Spacing and sizing utilities like px-4, max-h-16 |
| `--radius-*` | Border radius utilities like rounded-sm |
| `--shadow-*` | Box shadow utilities like shadow-md |

### Typography Usage
Use the font shorthand property with typography variables:

```css
.my-heading { font: var(--fz-font-h1); }
.my-button { font: var(--fz-font-button-medium); }
.my-caption { font: var(--fz-font-caption); }
```

Available font variables:
- `--fz-font-h1`, `--fz-font-h2`, `--fz-font-h3`
- `--fz-font-body`, `--fz-font-body-bold`, `--fz-font-body-medium`
- `--fz-font-button-large`, `--fz-font-button-medium`, `--fz-font-button-small`
- `--fz-font-caption`, `--fz-font-caption-bold`
- `--fz-font-subtitle`, `--fz-font-label`, `--fz-font-control-text`
- `--fz-font-hint`, `--fz-font-hint-medium`
- `--fz-font-modal-title`, `--fz-font-question-title`

## Reka UI Integration

When implementing components based on Reka UI:
- Add animations using the Reka UI approach, do not use `FzTransition` component
- Properly forward props and emits to maintain all functionality

### Forwarding Props and Emits

Use these Reka UI utilities for forwarding:

#### 1. `useForwardPropsEmits`
For forwarding both props and emits:

```vue
<script setup lang="ts">
import type { DialogRootEmits, DialogRootProps } from 'reka-ui'
import { DialogRoot, useForwardPropsEmits } from 'reka-ui'

const props = defineProps<DialogRootProps>()
const emits = defineEmits<DialogRootEmits>()

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <DialogRoot v-bind="forwarded">
    <slot />
  </DialogRoot>
</template>
```

#### 2. `useForwardProps`
For forwarding only props:

```vue
<script setup lang="ts">
import type { DialogContentProps } from 'reka-ui'
import { DialogContent, useForwardProps } from 'reka-ui'

const props = defineProps<DialogContentProps>()
const forwarded = useForwardProps(props)
</script>

<template>
  <DialogContent v-bind="forwarded">
    <slot />
  </DialogContent>
</template>
```

#### 3. `useEmitAsProps`
For converting emits to props format:

```vue
<script setup lang="ts">
// @eslint
import type { DialogContentEmits } from 'reka-ui'
import { useEmitAsProps } from 'reka-ui'

const emits = defineEmits<DialogContentEmits>()
const emitsAsProps = useEmitAsProps(emits)
</script>

<template>
  <Component v-bind="emitsAsProps" :is="a">
    <slot />
  </Component>
</template>
```

## Custom Scrollbars

For components requiring custom scrollbars, use `simplebar` and `simplebar-vue`:

```vue
<script setup lang="ts">
import { Simplebar } from 'simplebar-vue'
import 'simplebar/dist/simplebar.min.css'
</script>

<template>
  <Simplebar class="fz-custom-scrollbar">
    <!-- Scrollable content here -->
  </Simplebar>
</template>

<style>
.fz-custom-scrollbar {
  --fz-scrollbar-track: var(--fz-color-background);
  --fz-scrollbar-thumb: var(--fz-color-secondary);

  /* Map to simplebar variables */
  --scrollbar-track: var(--fz-scrollbar-track);
  --scrollbar-thumb: var(--fz-scrollbar-thumb);
}
</style>
```

## Linting

If you encounter linting errors:

1. First try to fix manually
2. If unsuccessful, run: `npx eslint path_to_file --fix`
3. Ignore lint errors about blank space or import order

Example:
```bash
npx eslint ./src/components/Button.vue --fix
```

## Development Best Practices

1. **Always use the established tech stack** - Don't introduce new dependencies without justification
2. **Follow naming conventions consistently** - Use `Fz` prefix for components, `--fz-` for CSS variables, `fz:` for Tailwind classes
3. **Use CSS variables over hardcoded values** - Leverage the theme system defined in `src/styles/theme.css`
4. **Implement proper prop/emit forwarding** - When wrapping Reka UI components, use the provided utilities
5. **Write comprehensive stories** - Document component usage and variations in Storybook
6. **Test thoroughly** - Include unit tests for complex logic and visual regression tests
7. **Follow BEM methodology** - Structure CSS classes with proper block, element, modifier hierarchy
8. **Use semantic HTML** - Ensure accessibility and proper document structure
9. **Optimize for modern browsers** - Take advantage of modern CSS and JavaScript features
10. **Maintain consistency** - Follow established patterns and conventions throughout the codebase
