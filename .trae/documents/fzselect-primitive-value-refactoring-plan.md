# FzSelect Primitive Value Refactoring Plan

## Overview
Refactor FzSelect component to use primitive values (`AcceptableValue` from reka-ui) for `modelValue` instead of full option objects, following industry standards like PrimeVue's approach.

## Current State Analysis
- `AcceptableValue` from reka-ui is already used throughout the codebase (FzRadio, FzTag, etc.)
- FzSelect currently uses full option objects for `modelValue`
- Type `FzSelectPrimitiveValue = AcceptableValue` already exists in types.ts

## 1. Type System Changes

### 1.1 Update FzSelectProps Interface
```typescript
export interface FzSelectProps<OptionType extends FzSelectOptionBase<FzSelectOption> = FzSelectDefaultOptionType> extends FzSelectPropsBase {
  // Change from: modelValue: OptionType | OptionType[] | null
  modelValue: AcceptableValue | AcceptableValue[] | null
  options: ReadonlyArray<OptionType>
  optionValue?: string | ((option: OptionType) => AcceptableValue)
  optionLabel?: string | ((option: OptionType) => string)
  // ... rest unchanged
}
```

### 1.2 Update Emit Types
```typescript
const emit = defineEmits<{
  'update:modelValue': [value: AcceptableValue | AcceptableValue[] | null]
}>()
```

## 2. Composables Updates

### 2.1 useFzSelectSelectionLogic Changes

#### Key Functions to Update:
- `isOptionSelected`: Compare primitive values instead of option objects
- `selectOption`: Extract primitive value and emit it
- `deselectOption`: Work with primitive values
- `clearSelection`: Emit null or empty array of primitives

#### Implementation Strategy:
```typescript
function isOptionSelected(option: OptionType): boolean {
  const optionPrimitiveValue = getActualOptionValue(option)
  if (props.multiple) {
    return Array.isArray(props.modelValue) && 
           props.modelValue.includes(optionPrimitiveValue)
  }
  return props.modelValue === optionPrimitiveValue
}

function selectOption(option: OptionType): void {
  const primitiveValue = getActualOptionValue(option)
  if (props.multiple) {
    const currentValues = Array.isArray(props.modelValue) ? props.modelValue : []
    emit('update:modelValue', [...currentValues, primitiveValue])
  } else {
    emit('update:modelValue', primitiveValue)
  }
}
```

### 2.2 useFzSelectPropsParser Changes

#### Update getActualOptionValue:
- Ensure it returns `AcceptableValue` consistently
- Remove warnings about non-FzSelectValue types since we're standardizing on AcceptableValue

#### Add new helper functions:
```typescript
function findOptionByPrimitiveValue(primitiveValue: AcceptableValue): OptionType | undefined {
  return flattenedOptions.value.find(option => 
    getActualOptionValue(option) === primitiveValue
  )
}

function getSelectedOptions(): OptionType[] {
  if (!props.modelValue) return []
  
  const values = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
  return values.map(findOptionByPrimitiveValue).filter(Boolean) as OptionType[]
}
```

### 2.3 useFzSelectDisplayLogic Changes

#### Update display value computation:
```typescript
const selectedOptions = computed(() => {
  if (!props.modelValue) return []
  
  const values = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
  return values.map(value => 
    flattenedOptions.value.find(option => getActualOptionValue(option) === value)
  ).filter(Boolean) as OptionType[]
})
```

## 3. Component Changes (FzSelect.vue)

### 3.1 Props Definition
```typescript
const props = defineProps<FzSelectProps<OptionType>>()

const emit = defineEmits<{
  'update:modelValue': [value: AcceptableValue | AcceptableValue[] | null]
}>()
```

### 3.2 Template Updates
- No major template changes needed
- Display logic will automatically use the updated composables

## 4. Story Updates

### 4.1 Single Selection Stories
```typescript
// Before:
const modelValue = ref<FzSelectOption | null>({ label: 'Vue', value: 'vue' })

// After:
const modelValue = ref<string | null>('vue')
```

### 4.2 Multiple Selection Stories
```typescript
// Before:
const modelValue = ref<FzSelectOption[]>([
  { label: 'Vue', value: 'vue' },
  { label: 'React', value: 'react' }
])

// After:
const modelValue = ref<string[]>(['vue', 'react'])
```

### 4.3 Stories to Update
- `FzSelect.single.stories.ts`
- `FzSelect.multiple.stories.ts`
- `FzSelect.tree.stories.ts`
- `FzSelect.states.stories.ts`
- `FzSelect.performance.stories.ts`

## 5. Backward Compatibility

### 5.1 Breaking Changes
- `modelValue` type changes from option objects to primitive values
- Emit events now return primitive values instead of option objects

### 5.2 Migration Strategy
- This is a breaking change requiring major version bump
- Provide clear migration guide
- Consider deprecation warnings in current version

## 6. Migration Guide

### 6.1 For Single Selection
```typescript
// Before (v1.x)
const selected = ref<FzSelectOption | null>({ label: 'Vue', value: 'vue' })

// After (v2.x)
const selected = ref<string | null>('vue')

// To get the full option object when needed:
const selectedOption = computed(() => 
  options.find(opt => opt.value === selected.value)
)
```

### 6.2 For Multiple Selection
```typescript
// Before (v1.x)
const selected = ref<FzSelectOption[]>([
  { label: 'Vue', value: 'vue' },
  { label: 'React', value: 'react' }
])

// After (v2.x)
const selected = ref<string[]>(['vue', 'react'])

// To get full option objects when needed:
const selectedOptions = computed(() => 
  selected.value.map(val => options.find(opt => opt.value === val)).filter(Boolean)
)
```

### 6.3 Event Handling
```typescript
// Before (v1.x)
function onSelectionChange(selectedOption: FzSelectOption | null) {
  console.log('Selected:', selectedOption?.value)
}

// After (v2.x)
function onSelectionChange(selectedValue: string | null) {
  console.log('Selected:', selectedValue)
}
```

## 7. Implementation Order

1. **Phase 1**: Update type definitions in `types.ts`
2. **Phase 2**: Update `useFzSelectPropsParser` composable
3. **Phase 3**: Update `useFzSelectSelectionLogic` composable
4. **Phase 4**: Update `useFzSelectDisplayLogic` composable
5. **Phase 5**: Update `FzSelect.vue` component
6. **Phase 6**: Update all story files
7. **Phase 7**: Update tests
8. **Phase 8**: Update documentation

## 8. Testing Strategy

### 8.1 Unit Tests
- Test primitive value selection/deselection
- Test conversion between primitive values and option objects
- Test edge cases (null, empty arrays, invalid values)

### 8.2 Integration Tests
- Test with different option structures
- Test tree mode with primitive values
- Test search functionality

## 9. Benefits

### 9.1 Developer Experience
- Simpler API matching native HTML select behavior
- Easier form integration
- Reduced payload size
- Better serialization for APIs

### 9.2 Industry Alignment
- Matches PrimeVue, Material-UI, and Ant Design patterns
- Follows web standards
- More intuitive for developers coming from other libraries

## 10. Risks and Mitigation

### 10.1 Breaking Change Risk
- **Risk**: Existing applications will break
- **Mitigation**: Clear migration guide, major version bump, deprecation warnings

### 10.2 Performance Risk
- **Risk**: Additional lookups to find option objects from primitive values
- **Mitigation**: Optimize with computed caching, use Map for O(1) lookups

### 10.3 Complexity Risk
- **Risk**: Internal logic becomes more complex
- **Mitigation**: Comprehensive testing, clear internal documentation

This refactoring aligns FzSelect with industry standards while leveraging the existing `AcceptableValue` type from reka-ui, ensuring consistency across the component library.