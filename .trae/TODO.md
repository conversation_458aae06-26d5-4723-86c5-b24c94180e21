# TODO:

- [x] examine_types: Examine current FzSelectOptionBase and FzSelectOption type definitions in types.ts (priority: High)
- [x] remove_base_interface: Remove FzSelectOptionBase interface from types.ts (priority: High)
- [x] update_props_type: Update FzSelectProps to use FzSelectOption directly instead of FzSelectOptionBase (priority: High)
- [x] fix_option_value_type: Update FzSelectOption to have value: AcceptableValue instead of circular reference (priority: High)
- [x] update_references: Find and update all files that reference FzSelectOptionBase (priority: Medium)
- [x] type_check: Run type check to ensure all changes are correct (priority: Medium)
