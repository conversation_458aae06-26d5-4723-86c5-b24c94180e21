# Обзор Foquz UI

@TODO:
– Добавить общее описание проекта

## Установка и использование

### Установка из GitLab npm registry

Пакет опубликован в приватном GitLab npm registry. Для установки необходимо настроить доступ к registry:

1. Создайте файл `.npmrc` в корне вашего проекта:
```
@foquz:registry=https://doxsw.gitlab.yandexcloud.net/api/v4/projects/${CI_PROJECT_ID}/packages/npm/
//doxsw.gitlab.yandexcloud.net/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}
```

2. Или для локальной разработки с использованием Personal Access Token:
```
@foquz:registry=https://doxsw.gitlab.yandexcloud.net/api/v4/projects/PROJECT_ID/packages/npm/
//doxsw.gitlab.yandexcloud.net/api/v4/projects/PROJECT_ID/packages/npm/:_authToken=YOUR_PERSONAL_ACCESS_TOKEN
```

3. Установите пакет:
```bash
npm install @foquz/foquz-ui
```

### Использование

```typescript
import { FzButton, FzInput } from '@foquz/foquz-ui'
import '@foquz/foquz-ui/foquz-ui.css' // Импорт стилей компонентов
import '@foquz/foquz-ui/theme.css' // Импорт темы (tailwind)
```

### Версии

- **Стабильные версии** (latest): Создаются из git тегов в формате `v1.0.0`
- **Development версии** (dev): Автоматически создаются из веток `release` и `bugfixes`
- **Task версии** (dev): Создаются из веток с префиксом `TASK-`

## Цели проекта

- Миграция интерфейса компании на [Vue 3](https://vuejs.org/)
- Гибкие, настраиваемые компоненты
- Маленький бандл сайз

## Структура проекта

```
/src
  /assets         # Ресурсы (шрифт Roboto)
  /components     # Компоненты
    /Button       # Пример структуры компонента
      Button.vue
      Button.spec.ts # Юнит тесты (если будут)
      Button.stories.ts # Сторис (Изолированная презентация компонента)
      index.ts
  /composables    # Vue композаблы
  /styles         # Глобальные стили
  /config         # Конфигурации
  /utils          # Утилиты
```

## Зависимости

### Основные

- [Vue 3](https://vuejs.org/) в качестве фреймворка
- [TypeScript](https://www.typescriptlang.org/) для типизации
- [Vite](https://vitejs.dev/) для сборки
- [Reka UI](https://reka-ui.com/) в качестве базовой styless библиотеки компонентов

### Стилизация

- [TailwindCSS](https://tailwindcss.com/) с CSS-переменными
- [PostCSS](https://postcss.org/) плагины

### Функциональность

- Работа с иконками: [`vite-svg-loader`](https://github.com/jpkleemans/vite-svg-loader)
- Работа с датами: [`dayjs`](https://day.js.org/)
- Popup галереи: [`fancybox`](https://fancyapps.com/fancybox/) (Может есть что-то другое, более легковесное?)
- Слайдеры: [`embla-carousel`](https://www.embla-carousel.com/) (Может быть [`vue3-carousel`](https://vue3-carousel.ismail9k.com/))
- Формы: [`tanstack-forms`](https://tanstack.com/form/latest)
- Таблицы (Data Table): [`tanstack-table`](https://tanstack.com/table/latest)
- Модальные окна: `reka-ui`
- Сайдшиты: `reka-ui`
- Тосты: `reka-ui`
- Календари, датапикеры: `reka-ui`
- Кастомные скроллбары: [`simplebar`](https://github.com/Grsmto/simplebar) и [`simplebar-vue`](https://github.com/Grsmto/simplebar/tree/master/packages/simplebar-vue)

### Документация

- [Storybook](https://storybook.js.org/) галерея компонентов

### Тестирование

- [Vitest](https://vitest.dev/)
- [Vue Test Utils](https://test-utils.vuejs.org/)
- [Playwright](https://playwright.dev/)
- [Chromatic](https://www.chromatic.com/) для визуального тестирования компонентов

## Работа с [TailwindCSS](https://tailwindcss.com/)

На самом нижнем уровне избегаем CSS class bloat и не используем tailwind классы
Вместо них используем CSS-переменные с префиксом `fz-`:

```css
.btn {
  min-height: var(--fz-min-button-height);
  padding: var(--fz-p-2);
}
```

На более высоком уровне допустимо использовать tailwind классы для общего лэйаута компонента, например так (Пример компонента `Card`):

ВАЖНО:

- Для tailwind классов используем префикс `fz:`. Согласно [документации](https://tailwindcss.com/docs/upgrade-guide#using-a-prefix)

```vue
<template>
  <div class="fz-card fz:rounded-lg fz:shadow-md fz:overflow-hidden">
    <div class="fz-card-header fz:p-4 fz:bg-gray-50 fz:border-b">
      <slot name="header">
        <h3 class="fz:text-lg fz:font-medium fz:text-gray-900">
          {{ title }}
        </h3>
      </slot>
    </div>
    <div class="fz-card-body fz:p-4">
      <slot />
    </div>
    <div
      v-if="$slots.footer"
      class="fz-card-footer fz:p-4 fz:bg-gray-50 fz:border-t"
    >
      <slot name="footer" />
    </div>
  </div>
</template>
```

По профиксам получается немного запутанно, но в целом вот так:

- `fz:` для tailwind сгенерированных классов (например `fz:pt-2 fz:rounded-lg`)
- `fz-` для CSS-переменных (например `--fz-color-text-primary`)
- `Fz` для компонентов (например `FzButton`)

## Технологические решения

- Работа с иконками: [`vite-svg-loader`](https://github.com/jpkleemans/vite-svg-loader)
- Работа с датами: [`dayjs`](https://day.js.org/)
- Стилизация: [TailwindCSS](https://tailwindcss.com/) с префиксом `fz:`
- Линтинг: [ESLint](https://eslint.org/) с конфигурацией [antfu](https://github.com/antfu/eslint-config)
- Префиксы компонентов: `Fz` для компонентов, `--fz-` для CSS-переменных
- Поддержка браузеров: только современные (Chrome, Firefox, Safari, Edge)
- Кастомные скроллбары: [`simplebar`](https://github.com/Grsmto/simplebar) и [`simplebar-vue`](https://github.com/Grsmto/simplebar/tree/master/packages/simplebar-vue)

⏳ **Требует решения:**

- Интеграция шрифта Roboto

## Мониторинг производительности

- Контроль размера бандла: [`size-limit`](https://github.com/ai/size-limit)

## Возможности для улучшения

- Генератор тем
- Интерактивная документация

## TODO:

- Создание руководства по разработке компонентов
- Руководство по визуальному регрессионному тестированию ([Chromatic](https://www.chromatic.com/))
