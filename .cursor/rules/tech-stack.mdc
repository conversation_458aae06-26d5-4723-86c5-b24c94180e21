---
description: 
globs: 
alwaysApply: true
---
Tech stack of this repository

# Tech Stack

All components and features must use the following technology stack:

- Vue 3 + TypeScript for component development
- Vite for building
- Reka UI as the base component library
- TailwindCSS with CSS variables and PostCSS for styling
- ESLint with antfu configuration for code linting

## Dependencies

The following dependencies must be used for specific features:
- Icons: `vite-svg-loader`
- Dates: `dayjs`
- Forms: `tanstack-forms`
- Tables: `tanstack-table`
- UI components: `reka-ui`
- Modal windows: `reka-ui`
- Sidesheets: `reka-ui`
- Toasts: `reka-ui`
- Calendars/date pickers: `reka-ui`
- Custom scrollbars: `simplebar` and `simplebar-vue`

## Browser Support
Only modern browsers are supported (Chrome, Firefox, Safari, Edge) 