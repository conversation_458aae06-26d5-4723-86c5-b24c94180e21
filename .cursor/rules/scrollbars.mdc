---
description: 
globs: 
alwaysApply: false
---
# Custom Scrollbars

For components that require custom scrollbars, use simplebar and simplebar-vue. This ensures consistent scrollbar styling across browsers and platforms.

## Installation

The project should already have simplebar and simplebar-vue installed. If not, install them:

```bash
npm install simplebar simplebar-vue
```

## Basic Usage

For simple scrollable containers, use the Simplebar component:

```vue
<template>
  <Simplebar class="fz-custom-scrollbar">
    <!-- Scrollable content here -->
  </Simplebar>
</template>

<script setup lang="ts">
import { Simplebar } from 'simplebar-vue'
import 'simplebar/dist/simplebar.min.css'
</script>
```

## Configuration

You can customize the scrollbar appearance and behavior with options:

```vue
<template>
  <Simplebar 
    class="fz-custom-scrollbar" 
    :options="{ 
      autoHide: false,
      scrollbarMinSize: 40
    }"
  >
    <!-- Scrollable content here -->
  </Simplebar>
</template>
```

## Styling

Apply custom styles to the scrollbar using the simplebar CSS variables:

```css
.fz-custom-scrollbar {
  --fz-scrollbar-track: var(--fz-color-background);
  --fz-scrollbar-thumb: var(--fz-color-secondary);
  
  /* Map to simplebar variables */
  --scrollbar-track: var(--fz-scrollbar-track);
  --scrollbar-thumb: var(--fz-scrollbar-thumb);
}
```

## Advanced Usage

For more complex scenarios, refer to the [simplebar documentation](mdc:https:/github.com/Grsmto/simplebar).

Remember to follow the project naming conventions by using `fz-` prefix for CSS classes and variables. 