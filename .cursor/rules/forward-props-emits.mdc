---
description: When creating wrapper components around Reka UI components, you must properly forward props and emits to maintain all functionality.
globs: 
alwaysApply: false
---
# Forward Props and Emits

When creating wrapper components around Reka UI components, you must properly forward props and emits to maintain all functionality.

## Available Utilities

Reka UI provides three utilities for forwarding props and emits:

### 1. `useForwardPropsEmits`

Use this when you need to forward both props and emits to a child component.

```vue
<script setup lang="ts">
import type { DialogRootEmits, DialogRootProps } from 'reka-ui'
import { DialogRoot, useForwardPropsEmits } from 'reka-ui'

const props = defineProps<DialogRootProps>()
const emits = defineEmits<DialogRootEmits>()

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <DialogRoot v-bind="forwarded">
    <slot />
  </DialogRoot>
</template>
```

### 2. `useForwardProps`

Use this when you only need to forward props to a child component.

```vue
<script setup lang="ts">
import type { DialogContentProps } from 'reka-ui'
import { DialogContent, useForwardProps } from 'reka-ui'

const props = defineProps<DialogContentProps>()
const forwarded = useForwardProps(props)
</script>

<template>
  <DialogContent v-bind="forwarded">
    <slot />
  </DialogContent>
</template>
```

### 3. `useEmitAsProps`

Use this when you need to convert emits to props format.

```vue
<script setup lang="ts">
import type { DialogContentEmits } from 'reka-ui'
import { useEmitAsProps } from 'reka-ui'

const emits = defineEmits<DialogContentEmits>()
const emitsAsProps = useEmitAsProps(emits)
</script>

<template>
  <Component v-bind="emitsAsProps">
    <slot />
  </Component>
</template>
```

## Custom Component Example

This is a complete example of creating a custom dialog component that wraps Reka UI primitives:

```vue
<!-- FzDialog.vue -->
<script setup lang="ts">
import type { DialogRootEmits, DialogRootProps } from 'reka-ui'
import { DialogContent, DialogOverlay, DialogPortal, DialogRoot, DialogTitle, DialogClose, useForwardPropsEmits } from 'reka-ui'
import { Cross2Icon } from '@radix-icons/vue'

const props = defineProps<DialogRootProps & { title?: string }>()
const emits = defineEmits<DialogRootEmits>()

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <DialogRoot v-bind="forwarded">
    <slot name="trigger" />
    <DialogPortal>
      <DialogOverlay class="fz-dialog__overlay" />
      <DialogContent class="fz-dialog__content">
        <DialogTitle v-if="title" class="fz-dialog__title">{{ title }}</DialogTitle>
        <div class="fz-dialog__body">
          <slot />
        </div>
        <DialogClose class="fz-dialog__close">
          <Cross2Icon />
          <span class="fz-sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<style>
.fz-dialog__overlay {
  background-color: var(--fz-color-surface-overlay-dark);
  position: fixed;
  inset: 0;
  z-index: 50;
}

.fz-dialog__content {
  background-color: var(--fz-color-surface-light);
  border-radius: var(--fz-radius-md);
  box-shadow: var(--fz-shadow-dropdown);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 450px;
  max-height: 85vh;
  padding: var(--fz-spacing-4);
  z-index: 51;
}

.fz-dialog__title {
  font: var(--fz-font-modal-title);
  margin-bottom: var(--fz-spacing-4);
}

.fz-dialog__close {
  position: absolute;
  top: var(--fz-spacing-4);
  right: var(--fz-spacing-4);
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--fz-color-border-icon);
}
</style>
```

## Usage of Custom Component

```vue
<template>
  <FzDialog v-model:open="isOpen" title="Settings">
    <template #trigger>
      <FzButton>Open Settings</FzButton>
    </template>
    
    <div class="fz:p-4">
      <!-- Dialog content here -->
    </div>
  </FzDialog>
</template>
```

Remember to follow the BEM naming convention with `fz-` prefix for component classes and use Tailwind utility classes with `fz:` prefix for component layouts as per our CSS guidelines.
