---
description: 
globs: 
alwaysApply: true
---
# Project Structure

The project must follow this structure:

```
/src
  /assets         # Resources (Roboto font)
  /components     # Components in FzComponentName directories
  /composables    # Vue composables
  /styles         # Global styles
  /config         # Configurations
  /utils          # Utilities
```

## Documentation
- Storybook must be used for component documentation
- Each component should have a corresponding story file

## Testing
- Unit tests: Vitest + Vue Test Utils
- E2E tests: Playwright
- Visual regression testing: Chromatic
