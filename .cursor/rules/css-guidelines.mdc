---
description: 
globs: 
alwaysApply: true
---
# CSS Guidelines

- Base level CSS: Use CSS variables with `fz` prefix instead of direct Tailwind classes
  ```css
  /* DO THIS */
  .btn {
    min-height: var(--fz-min-button-height);
    padding: var(--fz-spacing-2);
  }
  
  /* DON'T DO THIS */
  .btn {
    min-height: 2.5rem; /* Use CSS variable instead */
    padding: 0.5rem; /* Use CSS variable instead */
  }
  ```

Available css variables for this theme defined in `src/styles/theme.css`. Tailwind CSS variables are also available. Use them!

IMPORTANT: NEVER USE @apply! Only use css variables for low-level components and use classes with `fz:` for layout to high-level components. 

- Layout level: Use Tailwind classes with `fz:` prefix for component layout
  ```vue
  <!-- Example of proper layout with prefixed classes -->
  <div class="fz-card fz-card--rounded">
    <div class="fz-card__header fz:p-4 fz:bg-gray-50 fz:border-b">
      <!-- Content -->
    </div>
  </div>
  ```

- Component level: Use BEM naming conventions with `fz-` prefix for blocks, elements, and modifiers when structuring component classes, and combine with Tailwind utility classes prefixed `fz:` for spacing, colors, and other utilities.
  ```vue
  <!-- Example of BEM naming with prefixed utility classes -->
  <div class="fz-card fz-card--rounded">
    <div class="fz-card__header">
      <!-- Header content -->
    </div>
    <div class="fz-card__body fz:p-4">
      <!-- Body content -->
    </div>
    <div class="fz-card__footer fz:p-4 fz:text-right">
      <!-- Footer content -->
    </div>
  </div>
  ```

## Theme variable namespaces

Theme variables are defined in namespaces and each namespace corresponds to one or more utility class or variant APIs.

Defining new theme variables in these namespaces will make new corresponding utilities and variants available in your project:

| Namespace | Utility classes |
|-----|----|
| --color-* | Color utilities like bg-red-500, text-sky-300, and many more |
| --font-* | Font family utilities like font-sans |
| --text-* | Font size utilities like text-xl |
| --font-weight-* | Font weight utilities like font-bold |
| --tracking-* | Letter spacing utilities like tracking-wide |
| --leading-* | Line height utilities like leading-tight |
| --breakpoint-* | Responsive breakpoint variants like sm:* |
| --container-* | Container query variants like @sm:* and size utilities like max-w-md |
| --spacing-* | Spacing and sizing utilities like px-4, max-h-16, and many more |
| --radius-* | Border radius utilities like rounded-sm |
| --shadow-* | Box shadow utilities like shadow-md |
| --inset-shadow-* | Inset box shadow utilities like inset-shadow-xs |
| --drop-shadow-* | Drop shadow filter utilities like drop-shadow-md |
| --blur-* | Blur filter utilities like blur-md |
| --perspective-* | Perspective utilities like perspective-near |
| --aspect-* | Aspect ratio utilities like aspect-video |
| --ease-* | Transition timing function utilities like ease-out |
| --animate-* | Animation utilities like animate-spin | 

See [theme.css](mdc:src/styles/theme.css) for available custom css variables. They are available with `--fz-` prefix in css

# Components
– If you implement Components based on reka-ui, please add animations by using approach from that library, do not use `FzTransition` component. If forwarding of props/emits is needed see [forward-props-emits.mdc](mdc:.cursor/rules/forward-props-emits.mdc) rule