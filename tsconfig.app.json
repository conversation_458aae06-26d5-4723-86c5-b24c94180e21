{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "lib": ["es2021", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "declaration": true, "emitDeclarationOnly": true, "outDir": "./dist", "noUncheckedSideEffectImports": true}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["src/**/*.test.ts"]}