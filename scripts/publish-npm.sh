#!/bin/sh

set -e

echo "Starting npm package publishing..."

# Check if we're in a Git repository
if [ ! -d ".git" ]; then
    echo "Error: Not in a Git repository"
    exit 1
fi

# Setup npm authentication
echo "Setting up npm authentication..."
if [ -z "$CI_JOB_TOKEN" ]; then
    echo "Error: CI_JOB_TOKEN is not set"
    exit 1
fi

# Replace the token placeholder in .npmrc with the actual token
sed -i "s/\${CI_JOB_TOKEN}/$CI_JOB_TOKEN/g" .npmrc
echo "Authentication configured successfully"

# Install dependencies and build
echo "Installing dependencies..."
npm install

echo "Building package..."
npm run build

# Determine version based on branch or tag
if [ -n "$CI_COMMIT_TAG" ]; then
    # For tagged releases
    TAG_VERSION=$(echo $CI_COMMIT_TAG | sed 's/^v//')
    echo "Publishing stable version: $TAG_VERSION"
    npm version $TAG_VERSION --no-git-tag-version
    npm publish --tag latest
elif [ "$CI_COMMIT_BRANCH" = "release" ]; then
    # For release branch - increment minor version
    CURRENT_VERSION=$(grep '"version"' package.json | cut -d'"' -f4)
    MAJOR=$(echo $CURRENT_VERSION | cut -d. -f1)
    MINOR=$(echo $CURRENT_VERSION | cut -d. -f2)
    PATCH=$(echo $CURRENT_VERSION | cut -d. -f3)
    NEW_MINOR=$((MINOR + 1))
    DEV_VERSION="$MAJOR.$NEW_MINOR.$PATCH-dev-$CI_PIPELINE_IID"
    echo "Publishing development version: $DEV_VERSION"
    npm version $DEV_VERSION --no-git-tag-version
    npm publish --tag dev
elif [ "$CI_COMMIT_BRANCH" = "bugfixes" ]; then
    # For bugfixes branch - increment patch version
    CURRENT_VERSION=$(grep '"version"' package.json | cut -d'"' -f4)
    MAJOR=$(echo $CURRENT_VERSION | cut -d. -f1)
    MINOR=$(echo $CURRENT_VERSION | cut -d. -f2)
    PATCH=$(echo $CURRENT_VERSION | cut -d. -f3)
    NEW_PATCH=$((PATCH + 1))
    DEV_VERSION="$MAJOR.$MINOR.$NEW_PATCH-dev-$CI_PIPELINE_IID"
    echo "Publishing development version: $DEV_VERSION"
    npm version $DEV_VERSION --no-git-tag-version
    npm publish --tag dev
elif [[ "$CI_COMMIT_BRANCH" =~ ^TASK- ]]; then
    # For task branches
    BRANCH_NAME=$(echo $CI_COMMIT_BRANCH | sed 's/[^a-zA-Z0-9]/-/g')
    DEV_VERSION="0.0.0-$BRANCH_NAME-$CI_PIPELINE_IID"
    echo "Publishing task version: $DEV_VERSION"
    npm version $DEV_VERSION --no-git-tag-version
    npm publish --tag dev
else
    echo "Branch $CI_COMMIT_BRANCH is not configured for publishing"
    exit 1
fi

echo "Package published successfully!" 