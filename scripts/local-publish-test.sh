#!/bin/bash

set -e

echo "Local npm publishing test script"

# Check if GITLAB_ACCESS_TOKEN is set
if [ -z "$GITLAB_ACCESS_TOKEN" ]; then
    echo "Error: GITLAB_ACCESS_TOKEN environment variable is not set"
    echo "Please set it with: export GITLAB_ACCESS_TOKEN=your_token_here"
    exit 1
fi

# You need to replace PROJECT_ID with the actual project ID from GitLab
# You can find it in GitLab UI: Project Settings -> General -> Project ID
PROJECT_ID="21"

if [ "$PROJECT_ID" = "REPLACE_WITH_ACTUAL_PROJECT_ID" ]; then
    echo "Error: Please replace PROJECT_ID in this script with the actual project ID"
    echo "You can find it in GitLab: Project Settings -> General -> Project ID"
    exit 1
fi

echo "Using Project ID: $PROJECT_ID"

# Create local .npmrc for testing
echo "Creating local .npmrc for testing..."
cat > .npmrc.local << EOF
@foquz:registry=https://doxsw.gitlab.yandexcloud.net/api/v4/projects/${PROJECT_ID}/packages/npm/
//doxsw.gitlab.yandexcloud.net/api/v4/projects/${PROJECT_ID}/packages/npm/:_authToken=${GITLAB_ACCESS_TOKEN}
EOF

# Backup current .npmrc and use local one
if [ -f .npmrc ]; then
    cp .npmrc .npmrc.backup
fi
cp .npmrc.local .npmrc

echo "Installing dependencies..."
npm install

echo "Building package..."
npm run build

# Set a test version
TEST_VERSION="0.0.1-local-test-$(date +%s)"
echo "Setting test version: $TEST_VERSION"
npm version $TEST_VERSION --no-git-tag-version

echo "Publishing to GitLab npm registry..."
npm publish --tag test

echo "Restoring original .npmrc..."
if [ -f .npmrc.backup ]; then
    mv .npmrc.backup .npmrc
else
    rm .npmrc
fi

echo "Cleaning up..."
rm .npmrc.local

echo "✅ Local publish test completed successfully!"
echo "Package published as: @foquz/foquz-ui@$TEST_VERSION with tag 'test'"
echo ""
echo "To test installation in another project:"
echo "npm install @foquz/foquz-ui@test" 