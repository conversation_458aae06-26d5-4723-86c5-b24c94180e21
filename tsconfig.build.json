{"extends": ["@vue/tsconfig/tsconfig.dom.json", "@vue/tsconfig/tsconfig.lib.json"], "compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom"], "baseUrl": ".", "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "strict": true, "declaration": true, "emitDeclarationOnly": true, "outDir": "dist", "sourceMap": true, "esModuleInterop": true, "skipLibCheck": true}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["src/**/*.test.ts"]}