---
description: Vue component structure
globs: 
alwaysApply: false
---
# Component Structure

- Components must be placed in dedicated directories: `/src/components/ComponentName/`
- Required files for each component:
  - `ComponentName.vue` - Component implementation
  - `index.ts` - Export file
- Optional files:
  - `ComponentName.spec.ts` - Unit tests
  - `ComponentName.stories.ts` - Storybook stories
